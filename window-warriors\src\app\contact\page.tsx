import type { Metadata } from 'next';
import Link from 'next/link';
import Image from 'next/image';
import ContactForm from './ContactForm';
import LocationMap from './LocationMap';

export const metadata: Metadata = {
  title: 'Contact Us | Window Warriors - Premium UPVC Windows & Doors in Newcastle',
  description: 'Get in touch with Window Warriors for high-quality UPVC windows, doors and conservatories. Free quotes, home visits and expert advice across Newcastle and the North East.',
  keywords: 'contact window warriors, window installation quotes, door replacement newcastle, upvc windows contact, conservatory quotes north east',
  openGraph: {
    title: 'Contact Window Warriors | Premium Windows & Doors Specialists',
    description: 'Get in touch with our team for a free quote on premium UPVC windows, doors and conservatories. Serving Newcastle and the North East with expert installation.',
    url: 'https://windowwarriors.uk/contact',
    siteName: 'Window Warriors',
    images: [
      {
        url: 'https://windowwarriors.uk/images/pexels-silverkblack-22046266 1 (1).png',
        width: 1200,
        height: 630,
        alt: 'Contact Window Warriors - Premium Windows & Doors',
      },
    ],
    locale: 'en_GB',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Contact Window Warriors | Premium Windows & Doors Specialists',
    description: 'Get in touch with our team for a free quote on premium UPVC windows, doors and conservatories in Newcastle.',
    images: ['https://windowwarriors.uk/images/pexels-silverkblack-22046266 1 (1).png'],
  },
  alternates: {
    canonical: 'https://windowwarriors.uk/contact',
  },
};

export default function ContactPage() {
  return (
    <main className="flex min-h-screen flex-col">
      {/* Hero Section - Mobile Optimized */}
      <section className="relative min-h-[500px] sm:min-h-[600px] lg:min-h-screen overflow-hidden">
        {/* Optimized background image */}
        <div className="absolute inset-0">
          <Image
            src="/images/optimized/front-view-front-door-with-blue-wall.webp"
            alt="Beautiful front door installation - Window Warriors contact"
            fill
            priority
            className="object-cover object-center"
            sizes="(max-width: 768px) 100vw, 100vw"
            quality={80}
            placeholder="blur"
            blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
          />
          {/* Enhanced gradient overlay with pink accent */}
          <div className="absolute inset-0 bg-gradient-to-br from-[#1a3a6d]/90 via-[#1a3a6d]/80 to-[#ec4899]/30"></div>
          <div className="absolute inset-0 bg-gradient-to-t from-[#0f172a]/60 via-transparent to-transparent"></div>
        </div>
        
        {/* Floating elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/6 left-1/6 w-32 sm:w-48 md:w-64 lg:w-80 h-32 sm:h-48 md:h-64 lg:h-80 bg-[#ec4899]/20 rounded-full blur-3xl animate-float-slow"></div>
          <div className="absolute bottom-1/6 right-1/6 w-40 sm:w-64 md:w-80 lg:w-[30rem] h-40 sm:h-64 md:h-80 lg:h-[30rem] bg-[#d946ef]/15 rounded-full blur-3xl animate-float-slow" style={{ animationDelay: '2s' }}></div>
        </div>
        
        {/* Hero content - Mobile Optimized */}
        <div className="container mx-auto px-4 sm:px-6 relative z-20 min-h-[500px] sm:min-h-[600px] lg:min-h-screen flex flex-col justify-center pt-16 sm:pt-20 md:pt-24 lg:pt-32">
          <div className="max-w-2xl animate-fade-in" style={{ animationDelay: '0.3s' }}>
            <span className="inline-block text-accessible-muted text-sm sm:text-base md:text-xl mb-3 sm:mb-4 border-b-2 border-[#ec4899]/50 pb-1 animate-fade-blur" style={{ animationDelay: '0.2s' }}>WINDOW WARRIORS</span>
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold text-white mb-4 sm:mb-6">
              Get in <span className="text-gradient-premium relative">
                Touch
                <span className="absolute bottom-0 left-0 w-full h-0.5 sm:h-1 bg-[#ec4899]/70 rounded-full animate-shimmer"></span>
              </span>
            </h1>
            <p className="text-sm sm:text-base md:text-lg lg:text-xl text-accessible-light mb-6 sm:mb-8 leading-relaxed animate-slide-up" style={{ animationDelay: '0.6s' }}>
              Have questions or ready for a free quote? Contact our friendly team today. We're here to help transform your home with premium windows, doors and conservatories.
            </p>
            
            {/* Contact Quick Info - Mobile Responsive */}
            <div className="flex flex-col sm:flex-row flex-wrap gap-4 sm:gap-6 mt-4 mb-6 sm:mb-8 animate-slide-up" style={{ animationDelay: '0.9s' }}>
              <div className="flex items-center">
                <div className="w-8 sm:w-10 h-8 sm:h-10 rounded-full bg-white/10 flex items-center justify-center mr-3">
                  <svg className="w-4 sm:w-5 h-4 sm:h-5 text-[#ec4899]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                  </svg>
                </div>
                <span className="text-accessible-light font-medium text-sm sm:text-base">0191 359 2774</span>
              </div>
              <div className="flex items-center">
                <div className="w-8 sm:w-10 h-8 sm:h-10 rounded-full bg-white/10 flex items-center justify-center mr-3">
                  <svg className="w-4 sm:w-5 h-4 sm:h-5 text-[#ec4899]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
                <span className="text-accessible-light font-medium text-sm sm:text-base"><EMAIL></span>
              </div>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 animate-slide-up" style={{ animationDelay: '0.9s' }}>
              <Link href="/products-services#quote" className="btn-primary animate-pulse-slow text-center text-sm sm:text-base">
                Get a Free Quote
              </Link>
              <Link href="/products-services" className="btn-outline text-center text-sm sm:text-base">
                Our Services
              </Link>
            </div>
            
            <div className="mt-6 sm:mt-8 md:mt-12 flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-4 animate-fade-in" style={{ animationDelay: '1.2s' }}>
              <span className="text-accessible-muted text-xs sm:text-sm md:text-base">Trusted by</span>
              <div className="flex space-x-3 sm:space-x-4 md:space-x-6">
                <div className="bg-white/90 rounded-full py-1 px-1 h-8 w-8 sm:h-10 sm:w-10 flex items-center justify-center overflow-hidden">
                  <Image 
                    src="/halo.jpeg" 
                    alt="Halo Certification" 
                    width={35} 
                    height={35}
                    className="object-contain"
                  />
                </div>
                <div className="bg-white/90 rounded-full py-1 px-1 h-8 w-8 sm:h-10 sm:w-10 flex items-center justify-center overflow-hidden">
                  <Image 
                    src="/veka.jpeg" 
                    alt="Veka Certification" 
                    width={35} 
                    height={35}
                    className="object-contain"
                  />
                </div>
                <div className="bg-white/90 rounded-full py-1 px-1 h-8 w-8 sm:h-10 sm:w-10 flex items-center justify-center overflow-hidden">
                  <Image 
                    src="/Assure Certified Logo 2.jpg" 
                    alt="Assure Certified" 
                    width={35} 
                    height={35}
                    className="object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
          
        {/* Scroll Indicator - Mobile Responsive */}
        <div className="absolute bottom-3 sm:bottom-4 left-1/2 transform -translate-x-1/2 z-30 animate-bounce">
          <div className="w-5 sm:w-6 h-8 sm:h-10 border-2 border-white/30 rounded-full flex justify-center">
            <div className="w-0.5 sm:w-1 h-2 sm:h-3 bg-[#ec4899] rounded-full mt-1.5 sm:mt-2 animate-pulse"></div>
          </div>
          <p className="text-accessible-subtle text-xs mt-1 sm:mt-2 text-center">Scroll</p>
        </div>
        </div>
      </section>

      {/* Contact Form and Map Section - Mobile Optimized */}
      <section className="py-12 sm:py-16 md:py-20 lg:py-24 xl:py-32 bg-white relative overflow-hidden">
        {/* Background decorative elements - Mobile responsive */}
        <div className="absolute inset-0 pointer-events-none">
          {/* Light patterns */}
          <div className="absolute top-0 left-0 w-full h-full opacity-5" style={{ 
            backgroundImage: 'radial-gradient(#ffffff 1px, transparent 1px)', 
            backgroundSize: '40px 40px'
          }}></div>
          
          {/* Floating elements - Responsive sizing */}
          <div className="absolute top-1/4 right-1/4 w-48 sm:w-64 md:w-80 lg:w-[40rem] h-48 sm:h-64 md:h-80 lg:h-[40rem] rounded-full bg-[#ec4899]/5 blur-3xl"></div>
          <div className="absolute bottom-1/3 left-1/6 w-40 sm:w-60 md:w-80 lg:w-96 h-40 sm:h-60 md:h-80 lg:h-96 rounded-full bg-[#1a3a6d]/30 blur-3xl"></div>
        </div>

        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          <div className="text-center mb-8 sm:mb-12 md:mb-16 lg:mb-20 xl:mb-24">
            <div className="inline-flex items-center justify-center mb-3 sm:mb-4">
              <span className="h-px w-6 sm:w-8 md:w-10 bg-[#ec4899]"></span>
              <span className="text-[#ec4899] font-medium mx-2 sm:mx-3 md:mx-4 uppercase tracking-wider text-xs sm:text-sm">Connect With Us</span>
              <span className="h-px w-6 sm:w-8 md:w-10 bg-[#ec4899]"></span>
            </div>
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-[#1a3a6d] mb-3 sm:mb-4 md:mb-6 leading-tight">
              Contact <span className="text-gradient-accent">Information</span>
            </h2>
            <p className="text-sm sm:text-base md:text-lg text-accessible-gray max-w-2xl mx-auto">
              Fill out the form below or use your preferred contact method. We aim to respond within 24 hours.
            </p>
          </div>
            
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 lg:gap-10 xl:gap-12">
            {/* Contact Form - Mobile Optimized */}
            <div className="bg-gray-50 p-4 sm:p-6 md:p-8 rounded-xl sm:rounded-2xl shadow-lg border border-gray-100 hover:border-[#ec4899]/30 transition-all duration-300">
              <h3 className="text-lg sm:text-xl md:text-2xl font-bold relative inline-block mb-4 sm:mb-6">
                <span className="text-[#1a3a6d]">Send us a message</span>
                <span className="absolute -bottom-1 sm:-bottom-2 left-0 w-10 sm:w-12 h-0.5 sm:h-1 bg-[#ec4899]/50 rounded-full"></span>
              </h3>
              <ContactForm />
            </div>
            
            {/* Map and Contact Details - Mobile Optimized */}
            <div className="flex flex-col space-y-4 sm:space-y-6 lg:space-y-8">
              {/* Map - Mobile responsive height */}
              <div className="bg-gray-50 p-3 sm:p-4 rounded-xl sm:rounded-2xl shadow-lg h-[250px] sm:h-[300px] md:h-[350px] overflow-hidden border border-gray-100 hover:border-[#ec4899]/30 transition-all duration-300">
                <LocationMap />
              </div>
              
              {/* Contact details - Mobile optimized */}
              <div className="bg-gray-50 p-4 sm:p-6 md:p-8 rounded-xl sm:rounded-2xl shadow-lg border border-gray-100 hover:border-[#ec4899]/30 transition-all duration-300">
                <h3 className="text-lg sm:text-xl md:text-2xl font-bold relative inline-block mb-4 sm:mb-6">
                  <span className="text-[#1a3a6d]">Contact Details</span>
                  <span className="absolute -bottom-1 sm:-bottom-2 left-0 w-10 sm:w-12 h-0.5 sm:h-1 bg-[#ec4899]/50 rounded-full"></span>
                </h3>
                
                <div className="space-y-4 sm:space-y-5 lg:space-y-6">
                  <div className="flex items-start group">
                    <div className="flex-shrink-0 w-8 sm:w-10 h-8 sm:h-10 rounded-full bg-[#ec4899]/20 group-hover:bg-[#ec4899]/30 flex items-center justify-center mr-3 sm:mr-4 transition-colors duration-300">
                      <svg className="w-4 sm:w-5 h-4 sm:h-5 text-[#ec4899]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-semibold text-[#1a3a6d] mb-1 text-sm sm:text-base">Our Service Area</h4>
                      <p className="text-accessible-gray text-sm sm:text-base">
                        Newcastle upon Tyne, Durham, <br/>
                        Northumberland, Sunderland
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start group">
                    <div className="flex-shrink-0 w-8 sm:w-10 h-8 sm:h-10 rounded-full bg-[#ec4899]/20 group-hover:bg-[#ec4899]/30 flex items-center justify-center mr-3 sm:mr-4 transition-colors duration-300">
                      <svg className="w-4 sm:w-5 h-4 sm:h-5 text-[#ec4899]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-semibold text-[#1a3a6d] mb-1 text-sm sm:text-base">Phone</h4>
                      <p className="text-accessible-gray text-sm sm:text-base">
                        <a href="tel:+441913592774" className="hover:text-[#ec4899] transition-colors duration-300">
                          0191 359 2774
                        </a>
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start group">
                    <div className="flex-shrink-0 w-8 sm:w-10 h-8 sm:h-10 rounded-full bg-[#ec4899]/20 group-hover:bg-[#ec4899]/30 flex items-center justify-center mr-3 sm:mr-4 transition-colors duration-300">
                      <svg className="w-4 sm:w-5 h-4 sm:h-5 text-[#ec4899]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-semibold text-[#1a3a6d] mb-1 text-sm sm:text-base">Email</h4>
                      <p className="text-accessible-gray text-sm sm:text-base">
                        <a href="mailto:<EMAIL>" className="hover:text-[#ec4899] transition-colors duration-300 break-all">
                          <EMAIL>
                        </a>
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start group">
                    <div className="flex-shrink-0 w-8 sm:w-10 h-8 sm:h-10 rounded-full bg-[#ec4899]/20 group-hover:bg-[#ec4899]/30 flex items-center justify-center mr-3 sm:mr-4 transition-colors duration-300">
                      <svg className="w-4 sm:w-5 h-4 sm:h-5 text-[#ec4899]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-semibold text-[#1a3a6d] mb-1 text-sm sm:text-base">Business Hours</h4>
                      <p className="text-accessible-gray text-sm sm:text-base leading-relaxed">
                        Monday-Friday: 8am - 6pm<br/>
                        Saturday: 9am - 2pm<br/>
                        Sunday: Closed
                      </p>
                    </div>
                  </div>
                </div>
                
                {/* Social Media Links - Mobile Optimized */}
                <div className="mt-6 sm:mt-8 relative z-10">
                  <h4 className="font-semibold text-[#1a3a6d] mb-3 sm:mb-4 text-sm sm:text-base">Connect With Us</h4>
                  <div className="flex gap-3 sm:gap-4">
                    <a href="https://www.facebook.com/share/16AtRv9XUC/?mibextid=wwXIfr" target="_blank" rel="noopener noreferrer" 
                      className="w-10 sm:w-12 h-10 sm:h-12 rounded-full bg-[#ec4899]/20 hover:bg-[#1877F2]/80 hover:text-white flex items-center justify-center transition-all duration-300 hover:-translate-y-1">
                      <span className="sr-only">Facebook</span>
                      <svg className="w-5 sm:w-6 h-5 sm:h-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path fillRule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clipRule="evenodd" />
                      </svg>
                    </a>
                    <a href="https://www.tiktok.com/@windowarriors?_t=ZN-8vO56sswgEw&_r=1" target="_blank" rel="noopener noreferrer" 
                      className="w-10 sm:w-12 h-10 sm:h-12 rounded-full bg-[#ec4899]/20 hover:bg-[#000000]/80 hover:text-white flex items-center justify-center transition-all duration-300 hover:-translate-y-1">
                      <span className="sr-only">TikTok</span>
                      <svg className="w-5 sm:w-6 h-5 sm:h-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path d="M16.6 5.82s.51.5 0 0A4.278 4.278 0 0 1 15.54 3h-3.09v12.4a2.592 2.592 0 0 1-2.59 2.5c-1.42 0-2.6-1.16-2.6-2.6 0-1.72 1.66-3.01 3.37-2.48V9.66c-3.45-.46-6.47 2.22-6.47 5.64 0 3.33 2.76 5.7 5.69 5.7 3.14 0 5.69-2.55 5.69-5.7V9.01a7.35 7.35 0 0 0 4.3 1.38V7.3s-1.88.09-3.24-1.48z" />
                      </svg>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      {/* FAQ Section - Mobile Optimized */}
      <section className="py-12 sm:py-16 md:py-20 lg:py-24 xl:py-32 bg-white relative overflow-hidden">
        {/* Decorative elements - Mobile responsive */}
        <div className="absolute inset-0 overflow-hidden">
          {/* Background pattern */}
          <div className="absolute inset-0 bg-pattern opacity-5"></div>
          
          {/* Floating elements - Responsive sizing */}
          <div className="absolute top-1/4 right-1/4 w-32 sm:w-48 md:w-64 lg:w-72 h-32 sm:h-48 md:h-64 lg:h-72 bg-[#ec4899]/5 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/4 left-1/4 w-40 sm:w-60 md:w-80 lg:w-96 h-40 sm:h-60 md:h-80 lg:h-96 bg-[#1a3a6d]/20 rounded-full blur-3xl"></div>
        </div>
        
        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          <div className="text-center mb-8 sm:mb-12 md:mb-16 lg:mb-20">
            <div className="inline-flex items-center justify-center space-x-2 mb-3 sm:mb-4">
              <div className="flex-1 h-px max-w-[30px] sm:max-w-[40px] md:max-w-[60px] bg-gradient-to-r from-transparent to-[#ec4899]/70"></div>
              <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 md:w-3 md:h-3 rounded-full bg-[#ec4899]"></div>
              <div className="flex-1 h-px max-w-[30px] sm:max-w-[40px] md:max-w-[60px] bg-gradient-to-l from-transparent to-[#ec4899]/70"></div>
            </div>
            
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-[#1a3a6d] mb-3 sm:mb-4 md:mb-6 leading-tight">
              Frequently Asked <span className="text-gradient-accent">Questions</span>
            </h2>
            <p className="text-sm sm:text-base md:text-lg text-accessible-gray max-w-2xl mx-auto">
              Find quick answers to common questions about our products and services
            </p>
          </div>
          
          <div className="max-w-4xl mx-auto">
            <div className="grid grid-cols-1 gap-4 sm:gap-6 md:gap-8">
              {/* FAQ Cards - Mobile Optimized */}
              <div className="bg-gray-50 rounded-lg sm:rounded-xl md:rounded-2xl p-4 sm:p-6 md:p-8 border border-gray-100 hover:border-[#ec4899]/30 transform transition-all duration-500 hover:-translate-y-1 sm:hover:-translate-y-2 group">
                <h3 className="text-base sm:text-lg md:text-xl font-bold text-[#1a3a6d] mb-2 sm:mb-3 relative inline-block">
                  How quickly can you provide a quote?
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#ec4899] transition-all duration-500 group-hover:w-full"></span>
                </h3>
                <p className="text-accessible-gray leading-relaxed text-sm sm:text-base">
                  We aim to provide quotes within 24-48 hours after a home visit. For straightforward projects, we may be able to give a rough estimate over the phone, but an in-person assessment ensures the most accurate pricing.
                </p>
              </div>
              
              <div className="bg-gray-50 rounded-lg sm:rounded-xl md:rounded-2xl p-4 sm:p-6 md:p-8 border border-gray-100 hover:border-[#ec4899]/30 transform transition-all duration-500 hover:-translate-y-1 sm:hover:-translate-y-2 group">
                <h3 className="text-base sm:text-lg md:text-xl font-bold text-[#1a3a6d] mb-2 sm:mb-3 relative inline-block">
                  What areas do you service?
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#ec4899] transition-all duration-500 group-hover:w-full"></span>
                </h3>
                <p className="text-accessible-gray leading-relaxed text-sm sm:text-base">
                  We primarily serve Newcastle upon Tyne, Durham, Northumberland, and Sunderland. If you're located outside these areas but still in the North East, please contact us as we may still be able to help.
                </p>
              </div>
              
              <div className="bg-gray-50 rounded-lg sm:rounded-xl md:rounded-2xl p-4 sm:p-6 md:p-8 border border-gray-100 hover:border-[#ec4899]/30 transform transition-all duration-500 hover:-translate-y-1 sm:hover:-translate-y-2 group">
                <h3 className="text-base sm:text-lg md:text-xl font-bold text-[#1a3a6d] mb-2 sm:mb-3 relative inline-block">
                  Do you offer any guarantees on your products?
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#ec4899] transition-all duration-500 group-hover:w-full"></span>
                </h3>
                <p className="text-accessible-gray leading-relaxed text-sm sm:text-base">
                  Yes, all our UPVC windows, doors and conservatories come with a 10-year manufacturer's guarantee. We also provide a 2-year workmanship guarantee on all installations to ensure your complete satisfaction.
                </p>
              </div>
              
              <div className="bg-gray-50 rounded-lg sm:rounded-xl md:rounded-2xl p-4 sm:p-6 md:p-8 border border-gray-100 hover:border-[#ec4899]/30 transform transition-all duration-500 hover:-translate-y-1 sm:hover:-translate-y-2 group">
                <h3 className="text-base sm:text-lg md:text-xl font-bold text-[#1a3a6d] mb-2 sm:mb-3 relative inline-block">
                  How long does installation typically take?
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#ec4899] transition-all duration-500 group-hover:w-full"></span>
                </h3>
                <p className="text-accessible-gray leading-relaxed text-sm sm:text-base">
                  Installation time varies by project. A standard window replacement might take just a few hours, while a full house of windows or a conservatory installation could take 1-3 days. We'll provide a timeframe estimate during your consultation.
                </p>
              </div>
              
              <div className="bg-gray-50 rounded-lg sm:rounded-xl md:rounded-2xl p-4 sm:p-6 md:p-8 border border-gray-100 hover:border-[#ec4899]/30 transform transition-all duration-500 hover:-translate-y-1 sm:hover:-translate-y-2 group">
                <h3 className="text-base sm:text-lg md:text-xl font-bold text-[#1a3a6d] mb-2 sm:mb-3 relative inline-block">
                  Do I need to be home during installation?
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#ec4899] transition-all duration-500 group-hover:w-full"></span>
                </h3>
                <p className="text-accessible-gray leading-relaxed text-sm sm:text-base">
                  We prefer that someone is present at the beginning of the installation to confirm details and at the end to inspect the completed work. However, arrangements can be made if you can't be present the entire time.
                </p>
              </div>
            </div>
            
            <div className="mt-8 sm:mt-12 md:mt-16 lg:mt-20 text-center">
              <p className="text-accessible-gray mb-4 sm:mb-6 md:mb-8 text-sm sm:text-base">Still have questions? We're here to help!</p>
              <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 md:gap-6 justify-center">
                <Link
                  href="tel:+441913592774"
                  className="btn-accent group"
                >
                  <span className="relative z-10 flex items-center justify-center text-sm sm:text-base">
                    <svg className="w-4 sm:w-5 h-4 sm:h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                    </svg>
                    Call Us
                  </span>
                </Link>
                <Link
                  href="mailto:<EMAIL>"
                  className="inline-block bg-transparent border-2 border-[#ec4899] text-[#ec4899] hover:bg-[#ec4899] hover:text-white font-medium py-3 px-6 sm:px-8 rounded-full transition-all duration-300 text-center relative overflow-hidden group text-sm sm:text-base"
                >
                  <span className="relative z-10 flex items-center justify-center">
                    <svg className="w-4 sm:w-5 h-4 sm:h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                    Email Us
                  </span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
} 