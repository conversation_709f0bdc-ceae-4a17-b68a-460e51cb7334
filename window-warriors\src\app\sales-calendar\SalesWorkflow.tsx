'use client';

import { useState, useEffect } from 'react';
import SalesForm from './SalesForm';
import SalesCalendar from './SalesCalendar';

interface ClientInfo {
  name: string;
  email: string;
  phone: string;
  address: string;
  serviceInterest: string;
  notes: string;
}

const SalesWorkflow = () => {
  const [currentStep, setCurrentStep] = useState<'form' | 'calendar'>('form');
  const [clientInfo, setClientInfo] = useState<ClientInfo | null>(null);
  const [isFormSubmitted, setIsFormSubmitted] = useState(false);

  // Listen for form submission events from the GHL form
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      // Listen for GHL form submission events
      if (event.data && typeof event.data === 'object') {
        if (event.data.type === 'ghl-form-submit' || event.data.formSubmitted) {
          // Form was submitted successfully
          setIsFormSubmitted(true);
          // You can extract client info from the event if GHL provides it
          // For now, we'll show a generic success message
        }
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  const handleFormSuccess = (data: ClientInfo) => {
    setClientInfo(data);
    setCurrentStep('calendar');
  };

  const handleBackToForm = () => {
    setCurrentStep('form');
    setIsFormSubmitted(false);
  };

  const handleStartOver = () => {
    setCurrentStep('form');
    setClientInfo(null);
    setIsFormSubmitted(false);
  };

  return (
    <div className="w-full">
      {/* Progress Indicator */}
      <div className="mb-8">
        <div className="flex items-center justify-center space-x-4 mb-4">
          <div className={`flex items-center ${currentStep === 'form' ? 'text-[#ec4899]' : 'text-gray-400'}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
              currentStep === 'form' ? 'bg-[#ec4899] text-white' : 'bg-gray-200 text-gray-600'
            }`}>
              1
            </div>
            <span className="ml-2 text-sm font-medium">Client Info</span>
          </div>
          
          <div className={`w-12 h-0.5 ${currentStep === 'calendar' ? 'bg-[#ec4899]' : 'bg-gray-300'}`}></div>
          
          <div className={`flex items-center ${currentStep === 'calendar' ? 'text-[#ec4899]' : 'text-gray-400'}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
              currentStep === 'calendar' ? 'bg-[#ec4899] text-white' : 'bg-gray-200 text-gray-600'
            }`}>
              2
            </div>
            <span className="ml-2 text-sm font-medium">Book Appointment</span>
          </div>
        </div>
      </div>

      {/* Step Content */}
      {currentStep === 'form' && (
        <div>
          <div className="text-center mb-6">
            <h3 className="text-xl sm:text-2xl font-bold text-[#1a3a6d] mb-2">
              Step 1: Collect Client Information
            </h3>
            <p className="text-gray-600">
              Fill out the form below with your client's details. This information will be saved for the appointment booking.
            </p>
          </div>
          
          <SalesForm onSuccess={handleFormSuccess} />
          
          {isFormSubmitted && (
            <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center">
                <svg className="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                </svg>
                <div>
                  <h4 className="text-green-800 font-medium">Form Submitted Successfully!</h4>
                  <p className="text-green-700 text-sm">
                    Client information has been saved. You can now proceed to book their appointment.
                  </p>
                </div>
              </div>
              <button
                onClick={() => setCurrentStep('calendar')}
                className="mt-3 bg-[#ec4899] hover:bg-[#be185d] text-white font-medium py-2 px-4 rounded-lg transition-colors duration-300"
              >
                Proceed to Booking →
              </button>
            </div>
          )}
        </div>
      )}

      {currentStep === 'calendar' && (
        <div>
          <div className="text-center mb-6">
            <h3 className="text-xl sm:text-2xl font-bold text-[#1a3a6d] mb-2">
              Step 2: Book Client Appointment
            </h3>
            <p className="text-gray-600">
              Select an available time slot for your client. Their information is already saved.
            </p>
          </div>

          {/* Client Info Summary */}
          {(clientInfo || isFormSubmitted) && (
            <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-start justify-between">
                <div>
                  <h4 className="text-blue-800 font-medium mb-2">Client Information Saved</h4>
                  {clientInfo ? (
                    <div className="text-blue-700 text-sm space-y-1">
                      <p><strong>Name:</strong> {clientInfo.name}</p>
                      <p><strong>Email:</strong> {clientInfo.email}</p>
                      <p><strong>Phone:</strong> {clientInfo.phone}</p>
                      {clientInfo.address && <p><strong>Address:</strong> {clientInfo.address}</p>}
                      {clientInfo.serviceInterest && <p><strong>Service Interest:</strong> {clientInfo.serviceInterest}</p>}
                    </div>
                  ) : (
                    <p className="text-blue-700 text-sm">
                      Client details have been submitted and saved in the system.
                    </p>
                  )}
                </div>
                <button
                  onClick={handleBackToForm}
                  className="text-blue-600 hover:text-blue-800 text-sm underline"
                >
                  Edit Info
                </button>
              </div>
            </div>
          )}

          <SalesCalendar />

          <div className="mt-6 flex justify-center">
            <button
              onClick={handleStartOver}
              className="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-300"
            >
              ← Start Over with New Client
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default SalesWorkflow;
