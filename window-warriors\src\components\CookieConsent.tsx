'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { getCookie, setCookie } from '@/utils/cookieUtils';

interface CookieConsentProps {
  privacyPolicyUrl: string;
}

const CookieConsent = ({ privacyPolicyUrl }: CookieConsentProps) => {
  const [isVisible, setIsVisible] = useState(false);
  const [animateIn, setAnimateIn] = useState(false);

  useEffect(() => {
    // Check if the user has already accepted cookies
    const hasConsent = getCookie('cookie-consent');
    
    // If there's no consent yet, show the banner
    if (!hasConsent) {
      // Short delay before showing the banner for better UX
      const timer = setTimeout(() => {
        setIsVisible(true);
        // Trigger animation after a tiny delay
        setTimeout(() => setAnimateIn(true), 50);
      }, 1500);
      
      return () => clearTimeout(timer);
    }
  }, []);

  const handleAcceptAll = () => {
    setCookie('cookie-consent', 'all', 180); // 6 months
    setCookie('analytics-cookies', 'true', 180);
    setCookie('marketing-cookies', 'true', 180);
    setCookie('preference-cookies', 'true', 180);
    
    // Activate Google Analytics immediately if it exists
    if (typeof window !== 'undefined' && window.gtag) {
      (window.gtag as any)('consent', 'update', {
        'analytics_storage': 'granted',
        'ad_storage': 'granted'
      });
    }
    
    // Animate out
    setAnimateIn(false);
    setTimeout(() => setIsVisible(false), 500);
    
    // Dispatch custom event to notify that consent has been given
    const consentEvent = new Event('consentUpdated');
    window.dispatchEvent(consentEvent);
  };

  const handleEssentialOnly = () => {
    setCookie('cookie-consent', 'essential', 180); // 6 months
    setCookie('analytics-cookies', 'false', 180);
    setCookie('marketing-cookies', 'false', 180);
    setCookie('preference-cookies', 'false', 180);
    
    // Animate out
    setAnimateIn(false);
    setTimeout(() => setIsVisible(false), 500);
  };

  if (!isVisible) return null;

  return (
    <div 
      className={`fixed bottom-0 left-0 right-0 z-[9999] overflow-hidden transition-all duration-500 ease-in-out ${
        animateIn ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
      }`}
    >
      {/* Decorative top edge - adds design consistency with the site */}
      <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#1a3a6d] via-[#ec4899] to-[#1a3a6d]"></div>
      
      {/* Glass effect background */}
      <div className="bg-white/95 backdrop-blur-md shadow-lg border-t border-gray-200">
        <div className="container mx-auto">
          <div className="py-6 sm:py-8 px-4 sm:px-6 relative">
            {/* Decorative elements */}
            <div className="absolute top-1/4 right-1/4 w-32 h-32 bg-[#ec4899]/5 rounded-full blur-3xl"></div>
            <div className="absolute bottom-1/3 left-1/6 w-24 h-24 bg-[#1a3a6d]/5 rounded-full blur-3xl"></div>
            
            <div className="flex flex-col md:flex-row gap-6 items-start md:items-center justify-between relative">
              <div className="flex-1">
                <h3 className="text-xl sm:text-2xl font-bold text-[#1a3a6d] mb-3 relative inline-block">
                  We Value Your Privacy
                  <span className="absolute -bottom-1 left-0 w-12 h-1 bg-[#ec4899]/70 rounded-full"></span>
                </h3>
                <p className="text-sm sm:text-base text-accessible-gray mb-2 max-w-2xl">
                  We use cookies to enhance your browsing experience, analyze site traffic, and personalize content. 
                  By clicking "Accept All", you consent to our use of all cookies.
                </p>
                <p className="text-sm text-accessible-gray">
                  <Link 
                    href={privacyPolicyUrl}
                    className="text-[#1a3a6d] font-medium hover:text-[#ec4899] transition-colors"
                  >
                    Privacy Policy
                  </Link> • <Link 
                    href="/cookies"
                    className="text-[#1a3a6d] font-medium hover:text-[#ec4899] transition-colors"
                  >
                    Cookie Policy
                  </Link>
                </p>
              </div>
              <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
                <button 
                  onClick={handleEssentialOnly}
                  className="text-[#1a3a6d] border border-[#1a3a6d] hover:bg-[#1a3a6d]/5 px-5 py-2.5 rounded-full text-sm font-medium transition-colors duration-300 whitespace-nowrap"
                >
                  Essential Only
                </button>
                <button 
                  onClick={handleAcceptAll}
                  className="relative overflow-hidden group bg-[#1a3a6d] text-white px-6 py-2.5 rounded-full text-sm font-medium transition-colors duration-300 whitespace-nowrap
                  before:absolute before:inset-0 before:bg-gradient-accent before:translate-x-full 
                  before:transition-transform before:duration-300 hover:before:translate-x-0"
                >
                  <span className="relative z-10">Accept All</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CookieConsent; 
