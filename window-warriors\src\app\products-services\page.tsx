import React from 'react';
import Image from "next/image";
import Link from "next/link";
import { Metadata } from 'next';
import QuoteCalendar from "@/components/QuoteCalendar";

export const metadata: Metadata = {
  title: "UPVC Windows & Doors North East | Window Warriors Products & Services",
  description: "Premium UPVC windows, doors and conservatories in Newcastle, Durham and across the North East. Energy-efficient, secure solutions with 10-year guarantee.",
  keywords: "UPVC windows Newcastle, UPVC doors Durham, conservatories North East, window repairs Sunderland, energy-efficient windows UK, secure UPVC doors North East",
  openGraph: {
    title: "Premium UPVC Windows & Doors | Window Warriors Products & Services",
    description: "Explore our range of energy-efficient UPVC windows, secure doors and custom conservatories. Quality installations across Newcastle and the North East.",
    url: 'https://windowwarriors.uk/products-services',
    siteName: 'Window Warriors',
    images: [
      {
        url: 'https://windowwarriors.uk/images/beautiful-view-blue-lake-captured-from-inside-villa.jpg',
        width: 1200,
        height: 630,
        alt: 'Window Warriors UPVC Windows and Doors',
      },
    ],
    locale: 'en_GB',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "Premium UPVC Windows & Doors | Window Warriors Products",
    description: "Explore our range of energy-efficient UPVC windows, secure doors and custom conservatories for homes in the North East.",
    images: ['https://windowwarriors.uk/images/beautiful-view-blue-lake-captured-from-inside-villa.jpg'],
  },
  alternates: {
    canonical: 'https://windowwarriors.uk/products-services',
  },
};

export default function ProductsServicesPage() {
  // Windows product data
  const windowProducts = [
    {
      id: "casement-windows",
      title: "Casement Windows",
      description: "Our most popular window style, casement windows are versatile and provide excellent ventilation and security.",
      features: [
        "Multi-point locking systems for enhanced security",
        "Energy-efficient double glazing (A-rated available)",
        "Customizable opening configurations",
        "Wide range of colors and finishes",
        "Low maintenance and easy to clean",
      ],
      imageSrc: "/images/casement-window.jpg",
      imageAlt: "UPVC Casement Window",
    },
    {
      id: "sash-windows",
      title: "Sash Windows",
      description: "Combining traditional aesthetics with modern performance, our UPVC sash windows are perfect for period properties.",
      features: [
        "Traditional look with modern performance",
        "Sliding mechanism for smooth operation",
        "Tilt function for easy cleaning",
        "Enhanced security features",
        "Excellent thermal and acoustic insulation",
      ],
      imageSrc: "/images/sash-window.jpg",
      imageAlt: "UPVC Sash Window",
    },
    {
      id: "tilt-turn-windows",
      title: "Tilt & Turn Windows",
      description: "Versatile and practical, tilt and turn windows offer maximum versatility for ventilation and cleaning.",
      features: [
        "Dual-function operation - tilt for ventilation, turn for cleaning",
        "Excellent for hard-to-reach areas",
        "Superior insulation and security",
        "Modern, streamlined design",
        "Child safety features available",
      ],
      imageSrc: "/images/tilt-turn-window.jpg",
      imageAlt: "UPVC Tilt & Turn Window",
    },
  ];

  // Doors product data
  const doorProducts = [
    {
      id: "front-doors",
      title: "Front Doors",
      description: "Create a stunning entrance to your home with our secure and stylish UPVC front doors.",
      features: [
        "High-security multi-point locking systems",
        "Wide range of styles and colors",
        "Optional glazing patterns",
        "Low maintenance and highly durable",
        "Excellent thermal insulation",
      ],
      imageSrc: "/images/front-door.jpg",
      imageAlt: "UPVC Front Door",
    },
    {
      id: "patio-doors",
      title: "Patio Doors",
      description: "Connect your home to your garden with our smooth-sliding UPVC patio doors.",
      features: [
        "Smooth sliding operation",
        "Slim sightlines for maximum light",
        "Anti-lift mechanism for security",
        "Energy-efficient double glazing",
        "Available in 2, 3, or 4 panel configurations",
      ],
      imageSrc: "/images/patio-door.jpg",
      imageAlt: "UPVC Patio Door",
    },
    {
      id: "bifold-doors",
      title: "Bi-Fold Doors",
      description: "Transform your living space with fully-opening bi-fold doors that create a seamless indoor-outdoor experience.",
      features: [
        "Fully opening design for maximum space",
        "Creates a seamless connection to outdoors",
        "Robust security features",
        "Smooth operation on stainless steel rollers",
        "Multiple configuration options",
      ],
      imageSrc: "/images/bifold-door.jpg",
      imageAlt: "UPVC Bi-Fold Door",
    },
  ];

  // Conservatories data
  const conservatoryProducts = [
    {
      id: "lean-to-conservatory",
      title: "Lean-to Conservatory",
      description: "A simple, cost-effective conservatory solution that works well with most property types.",
      features: [
        "Clean, contemporary lines",
        "Ideal for properties with limited space",
        "Cost-effective design",
        "Mediterranean feel with maximum light",
        "Multiple roof glazing options",
      ],
      imageSrc: "/images/lean-to-conservatory.jpg",
      imageAlt: "Lean-to Conservatory",
    },
    {
      id: "victorian-conservatory",
      title: "Victorian Conservatory",
      description: "A classic design featuring a bay front, pitched roof and ornate details for a timeless appeal.",
      features: [
        "Traditional bay front with pitched roof",
        "Ornate roof ridge detailing",
        "Multiple facets for panoramic views",
        "Ideal for period properties",
        "Creates a spacious feel",
      ],
      imageSrc: "/images/victorian-conservatory.jpg",
      imageAlt: "Victorian Conservatory",
    },
    {
      id: "orangery",
      title: "Orangery",
      description: "Combining elements of a conservatory and traditional extension for a luxury living space.",
      features: [
        "Solid brick pillars for a substantial feel",
        "Lantern roof allows light to flood in",
        "More privacy than a full glass conservatory",
        "Seamless connection to your home",
        "Creates a premium living space",
      ],
      imageSrc: "/images/orangery.jpg",
      imageAlt: "Orangery",
    },
  ];

  // Repair services
  const repairServices = [
    {
      id: "misted-glass",
      title: "Misted Glass Replacement",
      description: "Replace failed double-glazed units that have become misted or condensated between the panes.",
      imageSrc: "/images/misted-glass.jpg",
      imageAlt: "Misted Glass Replacement",
    },
    {
      id: "lock-repairs",
      title: "Lock Repairs & Replacements",
      description: "Fix or replace faulty locks, handles and hinges to restore security and functionality.",
      imageSrc: "/images/lock-repair.jpg",
      imageAlt: "Lock Repair and Replacement",
    },
    {
      id: "seal-replacements",
      title: "Seal Replacements",
      description: "Replace damaged or deteriorated window and door seals to eliminate drafts and water ingress.",
      imageSrc: "/images/seal-replacement.jpg",
      imageAlt: "Seal Replacement",
    },
    {
      id: "window-adjustments",
      title: "Window & Door Adjustments",
      description: "Adjust poorly fitting windows and doors to ensure they open, close and lock correctly.",
      imageSrc: "/images/window-adjustment.jpg",
      imageAlt: "Window Adjustment",
    },
  ];

  return (
    <main className="flex min-h-screen flex-col">
      {/* Enhanced Hero Section - Mobile Optimized */}
      <section className="relative min-h-[500px] sm:min-h-[600px] lg:min-h-screen py-16 sm:py-20 md:py-24 lg:py-32 overflow-hidden">
        {/* Optimized background image */}
        <div className="absolute inset-0">
          <Image
            src="/images/optimized/exterior-home.webp"
            alt="Premium UPVC windows and doors on exterior home"
            fill
            priority
            className="object-cover object-center"
            sizes="(max-width: 768px) 100vw, 100vw"
            quality={80}
            placeholder="blur"
            blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
          />
          {/* Enhanced gradient overlay with pink accent */}
          <div className="absolute inset-0 bg-gradient-to-br from-[#1a3a6d]/90 via-[#1a3a6d]/80 to-[#ec4899]/30"></div>
          <div className="absolute inset-0 bg-gradient-to-t from-[#0f172a]/60 via-transparent to-transparent"></div>
        </div>
          
          {/* Simplified decorative elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute top-1/6 left-1/6 w-32 sm:w-48 md:w-64 lg:w-80 h-32 sm:h-48 md:h-64 lg:h-80 bg-[#ec4899]/20 rounded-full blur-3xl animate-float-slow"></div>
            <div className="absolute bottom-1/6 right-1/6 w-40 sm:w-64 md:w-80 lg:w-[30rem] h-40 sm:h-64 md:h-80 lg:h-[30rem] bg-[#d946ef]/15 rounded-full blur-3xl animate-float-slow" style={{ animationDelay: '2s' }}></div>
          </div>
        
        {/* Enhanced Hero Content - Mobile Optimized */}
        <div className="container mx-auto px-4 sm:px-6 relative z-10 flex flex-col justify-center min-h-[500px] sm:min-h-[600px] lg:min-h-screen pb-8 sm:pb-12 lg:pb-16">
          <div className="max-w-4xl animate-fade-in" style={{ animationDelay: '0.3s' }}>
            {/* Enhanced brand badge - Mobile responsive */}
            <div className="inline-flex items-center mb-4 sm:mb-6 md:mb-8">
              <div className="bg-white/10 backdrop-blur-sm border border-[#ec4899]/30 rounded-full px-4 sm:px-6 py-1.5 sm:py-2 animate-fade-blur" style={{ animationDelay: '0.2s' }}>
                <span className="text-accessible-light text-xs sm:text-sm md:text-base font-medium">WINDOW WARRIORS</span>
                <div className="w-full h-px bg-gradient-to-r from-transparent via-[#ec4899]/70 to-transparent mt-1"></div>
              </div>
            </div>
            
            {/* Enhanced title with pink gradients - Mobile responsive */}
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl 2xl:text-8xl font-bold text-white mb-3 sm:mb-4 md:mb-6 leading-tight">
              Our <span className="relative inline-block">
                <span className="text-gradient-logo">Products</span>
                <div className="absolute -bottom-1 sm:-bottom-2 left-0 w-full h-0.5 sm:h-1 bg-gradient-to-r from-[#ec4899] to-[#d946ef] rounded-full animate-shimmer"></div>
                <div className="absolute -top-0.5 sm:-top-1 -right-0.5 sm:-right-1 w-2 sm:w-3 h-2 sm:h-3 bg-[#ec4899]/60 rounded-full animate-pulse"></div>
              </span>
            </h1>
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold mb-3 sm:mb-4 md:mb-6">
              <span className="text-gradient-premium">& Services</span>
            </h2>
            
            {/* Enhanced description - Mobile responsive */}
            <div className="relative mb-4 sm:mb-6 md:mb-8">
              <p className="text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl text-accessible-light leading-relaxed animate-slide-up" style={{ animationDelay: '0.6s' }}>
                Premium quality UPVC windows, doors and conservatories for homes across Newcastle, Durham and the North East.
              </p>
              <div className="absolute -left-2 sm:-left-4 top-0 w-0.5 sm:w-1 h-full bg-gradient-to-b from-[#ec4899] to-transparent rounded-full opacity-60"></div>
            </div>
            
            {/* Enhanced buttons - Mobile responsive */}
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 md:gap-6 mb-6 sm:mb-8 md:mb-10 animate-slide-up" style={{ animationDelay: '0.9s' }}>
              <Link href="#quote" className="btn-primary text-center text-sm sm:text-base">
                Get a Free Quote
              </Link>
              <Link href="#windows" className="inline-block bg-white/10 backdrop-blur-sm border-2 border-white/30 text-white hover:bg-white hover:text-[#1a3a6d] font-medium py-3 sm:py-4 px-6 sm:px-8 rounded-full transition-all duration-300 text-center group text-sm sm:text-base">
                <span className="flex items-center justify-center">
                  <svg className="w-4 sm:w-5 h-4 sm:h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                  </svg>
                  Explore Our Services
                </span>
              </Link>
            </div>
            
            {/* Enhanced trust badges - Mobile responsive */}
            <div className="animate-fade-in" style={{ animationDelay: '1.2s' }}>
              <p className="text-white/70 text-xs sm:text-sm md:text-base mb-3 sm:mb-4">Trusted by homeowners across the North East</p>
              <div className="flex flex-wrap items-center gap-3 sm:gap-4 md:gap-6">
                {[
                  { src: "/halo.jpeg", alt: "Halo Certification" },
                  { src: "/veka.jpeg", alt: "Veka Certification" },
                  { src: "/Assure Certified Logo 2.jpg", alt: "Assure Certified" }
                ].map((cert, index) => (
                  <div key={index} className="relative group">
                    <div className="bg-white/90 backdrop-blur-sm rounded-full p-1.5 sm:p-2 h-8 w-8 sm:h-10 sm:w-10 md:h-12 md:w-12 lg:h-14 lg:w-14 flex items-center justify-center border border-white/20 transition-all duration-300 group-hover:scale-110 group-hover:border-[#ec4899]/50">
                      <Image 
                        src={cert.src}
                        alt={cert.alt}
                        width={40} 
                        height={40}
                        className="object-contain"
                      />
                    </div>
                    <div className="absolute inset-0 bg-[#ec4899]/20 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </div>
                ))}
              </div>
            </div>
          </div>
          
          {/* Modern scroll indicator - Mobile responsive */}
          <div className="mt-auto pt-6 sm:pt-8 flex justify-center animate-bounce-slow">
            <div className="flex flex-col items-center">
              <div className="w-5 sm:w-6 h-8 sm:h-10 border-2 border-white/30 rounded-full flex justify-center">
                <div className="w-0.5 sm:w-1 h-2 sm:h-3 bg-white/60 rounded-full mt-1.5 sm:mt-2 animate-pulse"></div>
              </div>
              <span className="text-accessible-subtle text-xs mt-1 sm:mt-2 hidden sm:block">Scroll to explore</span>
            </div>
          </div>
        </div>
      </section>

      {/* Windows Section - Mobile Optimized */}
      <section id="windows" className="relative py-12 sm:py-16 md:py-20 lg:py-24 bg-white">
        {/* Decorative elements - Mobile responsive */}
        <div className="absolute top-16 sm:top-24 md:top-32 left-0 w-32 sm:w-48 md:w-64 h-32 sm:h-48 md:h-64 bg-gradient-to-r from-[#ec4899]/20 to-transparent rounded-full blur-3xl"></div>
        
        <div className="container relative mx-auto px-4 sm:px-6 lg:px-8 pt-8 sm:pt-12 md:pt-16">
          <div className="text-center mb-8 sm:mb-10 md:mb-12">
            <div className="spinning-border p-0.5 inline-block rounded-full mb-3 sm:mb-4 md:mb-5">
              <span className="bg-white text-[#1a3a6d] text-xs sm:text-sm font-semibold px-3 sm:px-4 py-1 sm:py-1.5 rounded-full inline-block">
                Energy Efficient
              </span>
            </div>
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-[#1a3a6d] mb-3 sm:mb-4">UPVC <span className="text-gradient-accent">Windows</span></h2>
            <p className="text-sm sm:text-base md:text-lg text-accessible-gray max-w-2xl mx-auto">
              Our energy-efficient, secure and low-maintenance UPVC windows are designed to enhance your North East home's appearance, comfort and value
            </p>
          </div>

          {/* Windows product cards - Mobile Optimized */}
          <div className="space-y-12 sm:space-y-16 md:space-y-20 lg:space-y-24">
            {windowProducts.map((product, index) => (
              <div 
                key={product.id}
                id={product.id}
                className={`flex flex-col ${
                  index % 2 === 1 ? 'lg:flex-row-reverse' : 'lg:flex-row'
                } gap-6 sm:gap-8 lg:gap-12 xl:gap-16 items-center`}
              >
                {/* Product image - Mobile responsive */}
                <div className="w-full lg:w-1/2 relative">
                  <div className="relative h-[250px] sm:h-[300px] md:h-[350px] rounded-lg sm:rounded-xl overflow-hidden shadow-xl img-zoom-container">
                    <Image 
                      src="/images/beautiful-view-blue-lake-captured-from-inside-villa.jpg"
                      alt={product.imageAlt}
                      fill
                      className="object-cover img-zoom transition-transform duration-700"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-[#1a3a6d]/50 to-transparent"></div>
                  </div>
                  
                  {/* Floating badge - Mobile responsive */}
                  <div className={`absolute -bottom-3 sm:-bottom-4 md:-bottom-6 ${index % 2 === 1 ? '-left-3 sm:-left-4 md:-left-6' : '-right-3 sm:-right-4 md:-right-6'} bg-[#ec4899] text-white font-bold py-1.5 sm:py-2 px-3 sm:px-4 rounded-full shadow-lg animate-float text-xs sm:text-sm`}>
                    A+ Energy Rated
                  </div>
                </div>

                {/* Product details - Mobile optimized */}
                <div className="w-full lg:w-1/2">
                  <h3 className="text-xl sm:text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-3 sm:mb-4">{product.title}</h3>
                  <p className="text-sm sm:text-base md:text-lg text-accessible-gray mb-4 sm:mb-6">{product.description}</p>
                  
                  <h4 className="font-semibold text-[#1a3a6d] mb-2 sm:mb-3 text-sm sm:text-base">Premium Features:</h4>
                  <ul className="space-y-2 sm:space-y-3 mb-4 sm:mb-6">
                    {product.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start group">
                        <div className="flex-shrink-0 h-5 sm:h-6 w-5 sm:w-6 rounded-full bg-[#ec4899]/10 flex items-center justify-center mr-2 sm:mr-3 group-hover:bg-[#ec4899]/20 transition-colors duration-300">
                          <svg 
                            className="w-3 sm:w-4 h-3 sm:h-4 text-[#ec4899]" 
                            fill="none" 
                            stroke="currentColor" 
                            viewBox="0 0 24 24" 
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path 
                              strokeLinecap="round" 
                              strokeLinejoin="round" 
                              strokeWidth="2" 
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                        </div>
                        <span className="text-accessible-gray text-sm sm:text-base">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  
                  <Link
                    href="#quote"
                    className="btn-primary text-sm sm:text-base"
                  >
                    Get a Free Quote
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Doors Section - Mobile Optimized */}
      <section id="doors" className="py-12 sm:py-16 md:py-20 lg:py-24 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8 sm:mb-10 md:mb-12">
            <div className="spinning-border p-0.5 inline-block rounded-full mb-3 sm:mb-4 md:mb-5">
              <span className="bg-white text-[#1a3a6d] text-xs sm:text-sm font-semibold px-3 sm:px-4 py-1 sm:py-1.5 rounded-full inline-block">
                Secure & Stylish
              </span>
            </div>
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-[#1a3a6d] mb-3 sm:mb-4">UPVC <span className="text-gradient-accent">Doors</span></h2>
            <p className="text-sm sm:text-base md:text-lg text-accessible-gray max-w-2xl mx-auto">
              Enhance your North East home's security, appearance and energy efficiency with our range of high-quality UPVC doors
            </p>
          </div>

          {/* Doors product cards - Mobile Optimized */}
          <div className="space-y-12 sm:space-y-16 md:space-y-20 lg:space-y-24">
            {doorProducts.map((product, index) => (
              <div 
                key={product.id}
                id={product.id}
                className="glass-card p-4 sm:p-6 md:p-8 rounded-xl transition-all duration-500 hover:shadow-xl"
              >
                <div className={`flex flex-col ${
                  index % 2 === 1 ? 'lg:flex-row-reverse' : 'lg:flex-row'
                } gap-6 sm:gap-8 lg:gap-12 items-center`}
                >
                  {/* Product image - Mobile responsive */}
                  <div className="w-full lg:w-1/2 relative">
                    <div className="relative h-[250px] sm:h-[300px] md:h-[350px] rounded-lg sm:rounded-xl overflow-hidden img-zoom-container">
                      <Image 
                        src={index === 0 
                          ? "/images/decorated-front-door-with-plant.jpg"
                          : index === 1
                            ? "/images/front-view-front-door-with-blue-wall.jpg"
                            : "/images/front-view-front-door-with-blue-wall-plants.jpg"
                        }
                        alt={product.imageAlt}
                        fill
                        className="object-cover img-zoom transition-transform duration-700"
                      />
                    </div>
                    
                    {/* Floating badge - Mobile responsive */}
                    <div className={`absolute -bottom-3 sm:-bottom-4 md:-bottom-6 ${index % 2 === 1 ? '-left-3 sm:-left-4 md:-left-6' : '-right-3 sm:-right-4 md:-right-6'} bg-[#ec4899] text-white font-bold py-1.5 sm:py-2 px-3 sm:px-4 rounded-full shadow-lg animate-float text-xs sm:text-sm`}>
                      High Security
                    </div>
                  </div>

                  {/* Product details - Mobile optimized */}
                  <div className="w-full lg:w-1/2">
                    <h3 className="text-xl sm:text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-3 sm:mb-4">{product.title}</h3>
                    <p className="text-sm sm:text-base md:text-lg text-accessible-gray mb-4 sm:mb-6">{product.description}</p>
                    
                    <h4 className="font-semibold text-[#1a3a6d] mb-2 sm:mb-3 text-sm sm:text-base">Premium Features:</h4>
                    <ul className="space-y-2 sm:space-y-3 mb-4 sm:mb-6">
                      {product.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-start group">
                          <div className="flex-shrink-0 h-5 sm:h-6 w-5 sm:w-6 rounded-full bg-[#ec4899]/10 flex items-center justify-center mr-2 sm:mr-3 group-hover:bg-[#ec4899]/20 transition-colors duration-300">
                            <svg 
                              className="w-3 sm:w-4 h-3 sm:h-4 text-[#ec4899]" 
                              fill="none" 
                              stroke="currentColor" 
                              viewBox="0 0 24 24" 
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path 
                                strokeLinecap="round" 
                                strokeLinejoin="round" 
                                strokeWidth="2" 
                                d="M5 13l4 4L19 7"
                              />
                            </svg>
                          </div>
                          <span className="text-accessible-gray text-sm sm:text-base">{feature}</span>
                        </li>
                      ))}
                    </ul>
                    
                    <Link
                      href="#quote"
                      className="btn-primary text-sm sm:text-base"
                    >
                      Get a Free Quote
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Conservatories Section - Mobile Optimized */}
      <section id="conservatories" className="relative py-12 sm:py-16 md:py-20 lg:py-24 bg-white">
        {/* Decorative elements - Mobile responsive */}
        <div className="absolute top-16 sm:top-20 md:top-24 right-0 w-32 sm:w-48 md:w-64 h-32 sm:h-48 md:h-64 bg-gradient-to-l from-[#ec4899]/20 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-16 sm:bottom-20 md:bottom-24 left-0 w-40 sm:w-60 md:w-80 h-40 sm:h-60 md:h-80 bg-gradient-to-r from-[#ec4899]/10 to-transparent rounded-full blur-3xl"></div>
        
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8 sm:mb-10 md:mb-12">
            <div className="spinning-border p-0.5 inline-block rounded-full mb-3 sm:mb-4 md:mb-5">
              <span className="bg-white text-[#1a3a6d] text-xs sm:text-sm font-semibold px-3 sm:px-4 py-1 sm:py-1.5 rounded-full inline-block">
                Extended Living Space
              </span>
            </div>
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-[#1a3a6d] mb-3 sm:mb-4"><span className="text-gradient-accent">Conservatories</span> & Orangeries</h2>
            <p className="text-sm sm:text-base md:text-lg text-accessible-gray max-w-2xl mx-auto">
              Create additional living space that connects your home to your garden, perfectly designed for the North East climate
            </p>
          </div>

          {/* Conservatory cards - Mobile Optimized */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8 xl:gap-10">
            {conservatoryProducts.map((product, index) => (
              <div 
                key={product.id}
                id={product.id}
                className="glass-card rounded-xl overflow-hidden transition-all duration-500 hover:shadow-xl group hover-lift"
              >
                <div className="relative h-48 sm:h-56 md:h-64">
                  <Image 
                    src="/images/beautiful-hotel-insights-details.jpg"
                    alt={product.imageAlt}
                    fill
                    className="object-cover transition-transform duration-700 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-[#1a3a6d] to-transparent opacity-70"></div>
                  
                  <div className="absolute bottom-3 sm:bottom-4 left-3 sm:left-4 right-3 sm:right-4">
                    <h3 className="text-lg sm:text-xl font-bold text-white">{product.title}</h3>
                  </div>
                </div>
                
                <div className="p-4 sm:p-6">
                  <p className="text-accessible-gray mb-3 sm:mb-4 text-sm sm:text-base">{product.description}</p>
                  
                  <h4 className="font-semibold text-[#1a3a6d] mb-2 sm:mb-3 text-sm">Key Features:</h4>
                  <ul className="space-y-1.5 sm:space-y-2 mb-4 sm:mb-6">
                    {product.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start text-xs sm:text-sm">
                        <svg 
                          className="w-3 sm:w-4 h-3 sm:h-4 text-[#ec4899] mr-1.5 sm:mr-2 mt-0.5 flex-shrink-0" 
                          fill="none" 
                          stroke="currentColor" 
                          viewBox="0 0 24 24" 
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path 
                            strokeLinecap="round" 
                            strokeLinejoin="round" 
                            strokeWidth="2" 
                            d="M5 13l4 4L19 7"
                          />
                        </svg>
                        <span className="text-accessible-gray">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  
                  <Link 
                    href="/contact" 
                    className="text-[#1a3a6d] font-medium hover:text-[#ec4899] transition-colors duration-300 inline-flex items-center text-xs sm:text-sm"
                  >
                    <span>Request a design consultation</span>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 sm:h-4 w-3 sm:w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
      
      {/* Repair Services Section - Mobile Optimized */}
      <section id="repairs" className="py-12 sm:py-16 md:py-20 lg:py-24 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8 sm:mb-10 md:mb-12">
            <div className="spinning-border-light p-0.5 inline-block rounded-full mb-3 sm:mb-4 md:mb-5">
              <span className="bg-[#ec4899] text-white text-xs sm:text-sm font-semibold px-3 sm:px-4 py-1 sm:py-1.5 rounded-full inline-block">
                Expert Maintenance
              </span>
            </div>
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-[#1a3a6d] mb-3 sm:mb-4">Repair & <span className="text-gradient-accent">Maintenance</span> Services</h2>
            <p className="text-sm sm:text-base md:text-lg text-accessible-gray max-w-2xl mx-auto">
              Professional repairs and maintenance for all types of windows and doors across Newcastle, Durham and the North East
            </p>
          </div>

          {/* Repair service cards - Mobile Optimized */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 lg:gap-8">
            {repairServices.map((service, index) => (
              <div 
                key={service.id}
                id={service.id}
                className="glass-card rounded-xl overflow-hidden transition-all duration-500 hover:shadow-xl group hover-lift"
              >
                <div className="relative h-40 sm:h-48">
                  <Image 
                    src="/images/ancient-window-old-building-quebec-city.jpg"
                    alt={service.imageAlt}
                    fill
                    className="object-cover transition-transform duration-700 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-[#0a1827] to-transparent opacity-70"></div>
                </div>
                
                <div className="p-4 sm:p-6">
                  <h3 className="text-lg sm:text-xl font-bold text-[#1a3a6d] mb-2 sm:mb-3">{service.title}</h3>
                  <p className="text-accessible-gray mb-3 sm:mb-5 text-sm sm:text-base">{service.description}</p>
                  
                  <Link 
                    href="/contact" 
                    className="text-[#ec4899] hover:text-[#be185d] transition-colors duration-300 inline-flex items-center text-xs sm:text-sm"
                  >
                    <span>Book a repair service</span>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 sm:h-4 w-3 sm:w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Comparison Table Section - Mobile Optimized */}
      <section className="py-12 sm:py-16 md:py-20 lg:py-24 bg-gray-50 relative overflow-hidden">
        {/* Decorative background elements - Mobile responsive */}
        <div className="absolute top-0 right-0 w-48 sm:w-64 md:w-96 h-48 sm:h-64 md:h-96 bg-gradient-to-bl from-[#ec4899]/5 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-48 sm:w-64 md:w-96 h-48 sm:h-64 md:h-96 bg-gradient-to-tr from-[#ec4899]/5 to-transparent rounded-full blur-3xl"></div>
        
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center mb-8 sm:mb-10 md:mb-12 relative">
            <div className="inline-block mb-3 sm:mb-4">
              <div className="relative inline-block">
                <span className="absolute inset-x-0 bottom-0 h-1.5 sm:h-2 bg-gradient-to-r from-[#ec4899]/30 to-[#ec4899]/80 transform -rotate-1"></span>
                <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-[#1a3a6d] mb-2 relative z-10">Why Choose <span className="text-gradient-accent">UPVC</span>?</h2>
              </div>
            </div>
            <p className="text-sm sm:text-base md:text-lg text-accessible-gray max-w-2xl mx-auto">
              Discover why UPVC has become the preferred choice for homeowners across the North East
            </p>
          </div>

          {/* Benefits cards before the table - Mobile Optimized */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 md:gap-8 mb-12 sm:mb-14 md:mb-16">
            {[
              {
                icon: (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 sm:h-10 md:h-12 w-8 sm:w-10 md:w-12 mb-3 sm:mb-4 md:mb-5 text-[#ec4899]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                ),
                title: 'Energy Efficient',
                description: 'UPVC frames combined with modern double glazing offer outstanding thermal efficiency, reducing heat loss by up to 40% compared to older windows.',
              },
              {
                icon: (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 sm:h-10 md:h-12 w-8 sm:w-10 md:w-12 mb-3 sm:mb-4 md:mb-5 text-[#ec4899]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                ),
                title: 'Superior Security',
                description: 'Our UPVC windows and doors feature multi-point locking systems and reinforced frames, providing excellent protection against break-ins.',
              },
              {
                icon: (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 sm:h-10 md:h-12 w-8 sm:w-10 md:w-12 mb-3 sm:mb-4 md:mb-5 text-[#ec4899]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                ),
                title: 'Virtually Maintenance-Free',
                description: "Unlike timber, UPVC won't rot, warp or need repainting. A simple wipe with soapy water keeps your windows and doors looking like new for years.",
              },
            ].map((benefit, index) => (
              <div 
                key={index} 
                className="glass-card relative p-4 sm:p-6 md:p-8 rounded-xl transition-all duration-500 hover:shadow-xl group border border-white/30 hover:border-[#ec4899]/30"
              >
                <div className="absolute -top-3 sm:-top-4 md:-top-5 -right-3 sm:-right-4 md:-right-5 w-12 sm:w-16 md:w-20 h-12 sm:h-16 md:h-20 bg-[#ec4899]/10 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
                <div className="absolute -bottom-3 sm:-bottom-4 md:-bottom-5 -left-3 sm:-left-4 md:-left-5 w-12 sm:w-16 md:w-20 h-12 sm:h-16 md:h-20 bg-[#ec4899]/10 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
                
                <div className="absolute -top-4 sm:-top-6 md:-top-8 left-4 sm:left-6 md:left-8 rounded-full p-2 sm:p-3 md:p-4 bg-gradient-to-br from-white to-gray-100 shadow-md group-hover:shadow-lg group-hover:-translate-y-1 transition-all duration-500 z-10">
                  {benefit.icon}
                </div>
                <div className="pt-8 sm:pt-10 md:pt-14">
                  <h3 className="text-lg sm:text-xl md:text-2xl font-bold text-[#1a3a6d] mb-2 sm:mb-3 md:mb-4 group-hover:text-gradient-accent transition-all duration-500">{benefit.title}</h3>
                  <p className="text-accessible-gray group-hover:text-accessible-gray transition-colors duration-500 text-sm sm:text-base">{benefit.description}</p>
                </div>
                
                <div className="mt-4 sm:mt-5 md:mt-6 h-0.5 sm:h-1 w-12 sm:w-14 md:w-16 bg-gradient-to-r from-[#ec4899]/50 to-[#ec4899] rounded-full transform origin-left scale-0 group-hover:scale-100 transition-transform duration-500"></div>
              </div>
            ))}
          </div>

          {/* Enhanced comparison table - Mobile Optimized */}
          <div className="max-w-5xl mx-auto overflow-hidden rounded-xl shadow-xl relative glass-card border border-white/40">
            <div className="absolute top-0 left-0 bg-[#ec4899] text-white text-xs sm:text-sm font-bold py-1.5 sm:py-2 px-3 sm:px-4 rounded-br-lg">
              Material Comparison
            </div>
            
            <div className="overflow-x-auto mt-6 sm:mt-8">
              <table className="w-full min-w-[600px]">
                <thead>
                  <tr>
                    <th className="py-3 sm:py-4 px-3 sm:px-6 text-left"></th>
                    <th className="py-3 sm:py-4 px-3 sm:px-6 text-center relative">
                      <div className="mb-2 flex justify-center">
                        <div className="h-12 sm:h-14 md:h-16 w-12 sm:w-14 md:w-16 bg-white rounded-full shadow-lg flex items-center justify-center mb-1">
                          <span className="text-[#1a3a6d] text-sm sm:text-base md:text-lg font-bold">UPVC</span>
                        </div>
                      </div>
                      <div className="bg-[#1a3a6d] text-white py-1 px-2 sm:px-3 rounded-full text-xs sm:text-sm font-medium">Recommended</div>
                    </th>
                    <th className="py-3 sm:py-4 px-3 sm:px-6 text-center">
                      <div className="mb-2 flex justify-center">
                        <div className="h-10 sm:h-12 w-10 sm:w-12 bg-gray-200 rounded-full flex items-center justify-center mb-1">
                          <span className="text-accessible-gray text-xs sm:text-sm font-medium">Aluminum</span>
                        </div>
                      </div>
                    </th>
                    <th className="py-3 sm:py-4 px-3 sm:px-6 text-center">
                      <div className="mb-2 flex justify-center">
                        <div className="h-10 sm:h-12 w-10 sm:w-12 bg-gray-200 rounded-full flex items-center justify-center mb-1">
                          <span className="text-accessible-gray text-xs sm:text-sm font-medium">Timber</span>
                        </div>
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {[
                    { 
                      feature: 'Energy Efficiency', 
                      upvc: { rating: 'Excellent', note: 'A+ ratings available', highlight: true }, 
                      aluminum: { rating: 'Good', note: 'Thermal breaks required' }, 
                      timber: { rating: 'Good', note: 'Varies by wood type' } 
                    },
                    { 
                      feature: 'Maintenance', 
                      upvc: { rating: 'Minimal', note: 'Simple cleaning only', highlight: true }, 
                      aluminum: { rating: 'Low', note: 'Occasional checks' }, 
                      timber: { rating: 'High', note: 'Regular treatment needed' } 
                    },
                    { 
                      feature: 'Lifespan', 
                      upvc: { rating: '20-30 years', note: 'Long warranty options', highlight: true }, 
                      aluminum: { rating: '30-40 years', note: 'Higher initial cost' }, 
                      timber: { rating: '20-30 years*', note: 'With proper maintenance' } 
                    },
                    { 
                      feature: 'Security', 
                      upvc: { rating: 'Excellent', note: 'Multi-point locking', highlight: true }, 
                      aluminum: { rating: 'Excellent', note: 'Strong material' }, 
                      timber: { rating: 'Good', note: 'Depends on locks fitted' } 
                    },
                    { 
                      feature: 'Cost', 
                      upvc: { rating: 'Affordable', note: 'Best value option', highlight: true }, 
                      aluminum: { rating: 'Expensive', note: '40-60% more than UPVC' }, 
                      timber: { rating: 'Very Expensive', note: 'Highest cost option' } 
                    },
                    { 
                      feature: 'Weather Resistance', 
                      upvc: { rating: 'Excellent', note: 'Won\'t rot or corrode', highlight: true }, 
                      aluminum: { rating: 'Excellent', note: 'May conduct cold' }, 
                      timber: { rating: 'Good*', note: 'Requires maintenance' } 
                    },
                  ].map((row, index) => (
                    <tr key={index} className={index % 2 === 0 ? 'bg-white/50' : 'bg-white/30'}>
                      <td className="py-3 sm:py-4 px-3 sm:px-6 font-medium text-[#1a3a6d] text-sm sm:text-base">{row.feature}</td>
                      <td className="py-3 sm:py-4 px-3 sm:px-6 text-center relative">
                        <div className={`flex flex-col items-center ${row.upvc.highlight ? 'bg-[#ec4899]/10 rounded-lg py-1.5 sm:py-2 px-2 sm:px-3 -mx-2 sm:-mx-3' : ''}`}>
                          <span className="inline-flex items-center font-medium text-[#1a3a6d] text-sm sm:text-base">
                            {row.upvc.rating === 'Excellent' && (
                              <svg className="w-4 sm:w-5 h-4 sm:h-5 text-green-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                              </svg>
                            )}
                            {row.upvc.rating}
                          </span>
                          <span className="text-xs text-accessible-gray mt-0.5 sm:mt-1">{row.upvc.note}</span>
                        </div>
                      </td>
                      <td className="py-3 sm:py-4 px-3 sm:px-6 text-center">
                        <div className="flex flex-col items-center">
                          <span className="font-medium text-accessible-gray text-sm sm:text-base">{row.aluminum.rating}</span>
                          <span className="text-xs text-accessible-gray mt-0.5 sm:mt-1">{row.aluminum.note}</span>
                        </div>
                      </td>
                      <td className="py-3 sm:py-4 px-3 sm:px-6 text-center">
                        <div className="flex flex-col items-center">
                          <span className="font-medium text-accessible-gray text-sm sm:text-base">{row.timber.rating}</span>
                          <span className="text-xs text-accessible-gray mt-0.5 sm:mt-1">{row.timber.note}</span>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <div className="bg-[#ec4899]/5 text-accessible-gray px-4 sm:px-6 py-3 sm:py-4 text-xs sm:text-sm border-t border-[#ec4899]/10">
              <div className="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 sm:h-5 w-4 sm:w-5 text-[#ec4899] mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div>
                  <strong>Expert recommendation:</strong> For homes in the North East of England, we recommend UPVC windows and doors as the optimal choice, offering the best balance of energy efficiency, security, and value for money while requiring minimal maintenance in our variable climate.
                </div>
              </div>
            </div>
          </div>
          
          {/* Energy rating badge - Mobile Optimized */}
          <div className="mt-12 sm:mt-14 md:mt-16 flex justify-center">
            <div className="glass-card px-4 sm:px-6 py-3 sm:py-4 rounded-lg border border-white/30 inline-flex items-center shadow-lg">
              <div className="mr-3 sm:mr-4 h-12 sm:h-14 md:h-16 w-12 sm:w-14 md:w-16 rounded-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center text-white font-bold text-lg sm:text-xl">A+</div>
              <div>
                <span className="text-[#1a3a6d] font-medium block text-sm sm:text-base">Our UPVC windows achieve top energy ratings</span>
                <span className="text-accessible-gray text-xs sm:text-sm">Helping you save on heating bills and reduce carbon footprint</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section - Mobile Optimized */}
      <section className="py-12 sm:py-16 md:py-20 lg:py-24 bg-white relative overflow-hidden">
        {/* Decorative elements - Mobile responsive */}
        <div className="absolute top-0 left-0 w-full h-full pointer-events-none">
          <div className="absolute top-0 right-0 w-48 sm:w-64 md:w-96 h-48 sm:h-64 md:h-96 bg-[#ec4899]/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 left-0 w-32 sm:w-48 md:w-64 h-32 sm:h-48 md:h-64 bg-[#ec4899]/5 rounded-full blur-3xl"></div>
          
          {/* Floating particles - hidden on mobile */}
          <div className="hidden sm:block absolute top-1/4 left-1/3 w-6 sm:w-8 h-6 sm:h-8 rounded-full bg-[#ec4899]/5"></div>
          <div className="hidden sm:block absolute top-1/2 right-1/4 w-8 sm:w-10 md:w-12 h-8 sm:h-10 md:h-12 rounded-full bg-[#ec4899]/5"></div>
          <div className="hidden sm:block absolute bottom-1/3 left-1/4 w-6 sm:w-8 md:w-10 h-6 sm:h-8 md:h-10 rounded-full bg-[#ec4899]/5"></div>
        </div>
        
        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <span className="inline-block text-[#ec4899] font-medium mb-3 sm:mb-4 text-xs sm:text-sm md:text-base">GET IN TOUCH TODAY</span>
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-[#1a3a6d] mb-3 sm:mb-4 md:mb-6">Ready to Transform Your Home?</h2>
            <p className="text-sm sm:text-base md:text-lg lg:text-xl text-accessible-gray mb-6 sm:mb-8 md:mb-10 lg:mb-12 max-w-2xl mx-auto">
              Contact us today for a free, no-obligation quote on our premium UPVC windows, doors or conservatories for your North East home.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-3 sm:gap-4 md:gap-6">
              <Link href="#quote" className="btn-accent text-center text-sm sm:text-base px-6 sm:px-8 py-3 sm:py-4">
                Get Your Free Quote
              </Link>
              <Link href="/about" className="inline-block bg-transparent border-2 border-[#ec4899] text-[#ec4899] hover:bg-[#ec4899] hover:text-white font-medium py-3 px-6 sm:px-8 rounded-full transition-all duration-300 text-center text-sm sm:text-base">
                Learn More About Us
              </Link>
            </div>
            
            {/* Trust badges - Mobile Optimized */}
            <div className="mt-8 sm:mt-10 md:mt-12 lg:mt-16 grid grid-cols-2 md:grid-cols-4 gap-2 sm:gap-3 md:gap-6">
              <div className="bg-gray-50 border border-gray-200 p-2 sm:p-3 md:p-4 rounded-lg flex flex-col items-center">
                <svg className="w-6 sm:w-8 md:w-10 h-6 sm:h-8 md:h-10 text-[#ec4899] mb-1 sm:mb-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 12L11 14L15 10M20.618 5.984C20.846 6.887 20.846 7.897 20.846 9C20.846 14.5 17.346 18 11.846 18C6.346 18 2.846 14.5 2.846 9C2.846 3.5 6.346 0 11.846 0C12.949 0 13.959 0 14.862 0.228" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span className="text-xs sm:text-sm font-medium text-center text-accessible-gray">Free No-Obligation Quotes</span>
              </div>
              <div className="bg-gray-50 border border-gray-200 p-2 sm:p-3 md:p-4 rounded-lg flex flex-col items-center">
                <svg className="w-6 sm:w-8 md:w-10 h-6 sm:h-8 md:h-10 text-[#ec4899] mb-1 sm:mb-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M19.4 15C19.1277 15.8031 19.2292 16.6718 19.6727 17.4019C20.1162 18.132 20.8622 18.6376 21.7 18.8C20.5 21.4 18.5 22 16 22C12 22 11 20 7.00001 20C5.50001 20 4.00001 20.5 3.00001 22L2.50001 18.5C2.20001 16.5 3.40001 14 6.00001 14C8.00001 14 9.00001 16 12 16C14 16 14.5 15 19.4 15Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M12 7C9 7 7.00001 5 7.00001 3C7.00001 1.5 8.00001 1 8.00001 1C8.00001 1 9.50001 3 12 3C14.5 3 16 1 16 1C16 1 17 1.5 17 3C17 5 15 7 12 7Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span className="text-xs sm:text-sm font-medium text-center text-accessible-gray">Energy Efficiency Ratings</span>
              </div>
              <div className="bg-gray-50 border border-gray-200 p-2 sm:p-3 md:p-4 rounded-lg flex flex-col items-center">
                <svg className="w-6 sm:w-8 md:w-10 h-6 sm:h-8 md:h-10 text-[#ec4899] mb-1 sm:mb-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
                <span className="text-xs sm:text-sm font-medium text-center text-accessible-gray">10 Year Guarantee</span>
              </div>
              <div className="bg-gray-50 border border-gray-200 p-2 sm:p-3 md:p-4 rounded-lg flex flex-col items-center">
                <svg className="w-6 sm:w-8 md:w-10 h-6 sm:h-8 md:h-10 text-[#ec4899] mb-1 sm:mb-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 12H15M9 16H15M17 21H7C5.89543 21 5 20.1046 5 19V5C5 3.89543 5.89543 3 7 3H12.5858C12.851 3 13.1054 3.10536 13.2929 3.29289L18.7071 8.70711C18.8946 8.89464 19 9.149 19 9.41421V19C19 20.1046 18.1046 21 17 21Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
                <span className="text-xs sm:text-sm font-medium text-center text-accessible-gray">ASSURE Approved</span>
            </div>
            </div>
          </div>
        </div>
      </section>

      {/* Quote Calendar Section - Mobile Optimized */}
      <section id="quote" className="py-12 sm:py-16 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="max-w-4xl mx-auto">
            <QuoteCalendar
              title="Request Your Free Quote Today"
              subtitle="Schedule your free consultation and get a personalized quote for your UPVC windows, doors or conservatory project"
            />
          </div>
        </div>
      </section>
    </main>
  );
} 