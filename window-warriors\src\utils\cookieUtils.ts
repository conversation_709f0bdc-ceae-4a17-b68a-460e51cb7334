/**
 * Get a cookie by name
 * @param name The name of the cookie to retrieve
 * @returns The cookie value or null if not found
 */
export function getCookie(name: string): string | null {
  if (typeof document === 'undefined') {
    return null; // Return null on server side
  }
  
  const nameEQ = name + "=";
  const ca = document.cookie.split(';');
  
  for (let i = 0; i < ca.length; i++) {
    let c = ca[i];
    while (c.charAt(0) === ' ') c = c.substring(1, c.length);
    if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
  }
  
  return null;
}

/**
 * Set a cookie with the given name, value and expiry days
 * @param name The name of the cookie
 * @param value The value to store
 * @param days Number of days until the cookie expires
 */
export function setCookie(name: string, value: string, days: number): void {
  if (typeof document === 'undefined') {
    return; // Do nothing on server side
  }
  
  let expires = "";
  
  if (days) {
    const date = new Date();
    date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
    expires = "; expires=" + date.toUTCString();
  }
  
  document.cookie = name + "=" + value + expires + "; path=/; SameSite=Lax";
}

/**
 * Delete a cookie by name
 * @param name The name of the cookie to delete
 */
export function deleteCookie(name: string): void {
  if (typeof document === 'undefined') {
    return; // Do nothing on server side
  }
  
  setCookie(name, '', -1);
}

/**
 * Check if the user has accepted cookies
 * @returns Boolean indicating if cookies are accepted
 */
export function hasCookieConsent(): boolean {
  return getCookie('cookie-consent') === 'all' || getCookie('cookie-consent') === 'essential';
}

/**
 * Check if a specific cookie type is accepted
 * @param type The type of cookie to check (analytics, marketing, preference)
 * @returns Boolean indicating if the cookie type is accepted
 */
export function isCookieTypeAccepted(type: 'analytics' | 'marketing' | 'preference'): boolean {
  const cookieConsent = getCookie('cookie-consent');
  
  if (cookieConsent === 'all') {
    return true;
  }
  
  // Check specific cookie type
  return getCookie(`${type}-cookies`) === 'true';
} 