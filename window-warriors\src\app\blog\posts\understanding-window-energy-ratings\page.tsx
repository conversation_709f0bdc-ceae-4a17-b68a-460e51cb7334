import Image from "next/image";
import Link from "next/link";

export const metadata = {
  title: "Understanding Window Energy Ratings: A Buyer's Guide | Window Warriors",
  description: "Decode window energy ratings and learn what they mean for your home's efficiency and comfort. Expert guidance from Newcastle's trusted window specialists.",
  keywords: "window energy ratings, energy efficient windows, window WER, window efficiency, Newcastle windows, Window Warriors",
  icons: {
    icon: '/window-warriors-logo.png',
    apple: '/window-warriors-logo.png',
  },
  openGraph: {
    title: "Understanding Window Energy Ratings: A Buyer's Guide | Window Warriors",
    description: "Decode window energy ratings and learn what they mean for your home's efficiency and comfort.",
    url: 'https://windowwarriors.uk/blog/posts/understanding-window-energy-ratings',
    siteName: 'Window Warriors',
    images: [
      {
        url: 'https://windowwarriors.uk/images/beautiful-view-blue-lake-captured-from-inside-villa.jpg',
        width: 1200,
        height: 630,
        alt: 'Window Energy Ratings Guide - Window Warriors',
      },
    ],
    locale: 'en_GB',
    type: 'article',
  },
  twitter: {
    card: 'summary_large_image',
    title: "Understanding Window Energy Ratings: A Buyer's Guide | Window Warriors",
    description: "Decode window energy ratings and learn what they mean for your home's efficiency and comfort.",
    images: ['https://windowwarriors.uk/images/beautiful-view-blue-lake-captured-from-inside-villa.jpg'],
  },
  alternates: {
    canonical: 'https://windowwarriors.uk/blog/posts/understanding-window-energy-ratings',
  },
};

export default function UnderstandingWindowEnergyRatingsPage() {
  return (
    <main className="flex min-h-screen flex-col bg-white">
      {/* Hero Section - Following Main Website Pattern */}
      <section className="relative min-h-screen overflow-hidden bg-gradient-to-br from-[#1a3a6d] via-[#1a3a6d] to-[#ec4899]/20 pt-24 sm:pt-28 md:pt-32 lg:pt-36">
        {/* Background Image */}
        <div className="absolute inset-0 bg-cover bg-center" style={{
          backgroundImage: 'url("/images/beautiful-view-blue-lake-captured-from-inside-villa.jpg")',
          backgroundAttachment: 'scroll',
          backgroundPosition: 'center 30%',
        }}></div>
        
        {/* Multi-layered Background Overlays */}
        <div className="absolute inset-0">
          {/* Primary gradient background */}
          <div className="absolute inset-0 bg-gradient-to-br from-[#1a3a6d]/95 via-[#1a3a6d]/90 to-[#ec4899]/70"></div>
          
          {/* Animated gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-[#ec4899]/10 to-transparent animate-gradient-x"></div>
          
          {/* Noise texture */}
          <div className="absolute inset-0 opacity-[0.02] bg-noise"></div>
        </div>

        {/* Floating Pink Elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {/* Large floating blurs */}
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-[#ec4899]/20 rounded-full blur-3xl animate-float-slow"></div>
          <div className="absolute top-1/3 right-1/3 w-48 h-48 bg-[#d946ef]/15 rounded-full blur-2xl animate-float-reverse"></div>
          <div className="absolute bottom-1/4 left-1/3 w-56 h-56 bg-[#f9a8d4]/10 rounded-full blur-3xl animate-float"></div>
          
          {/* Glass particles */}
          <div className="absolute top-[20%] right-[20%] w-8 h-8 bg-white/10 rounded-full backdrop-blur-sm animate-float-slow border border-white/20"></div>
          <div className="absolute top-[60%] left-[15%] w-6 h-6 bg-[#ec4899]/20 rounded-full backdrop-blur-sm animate-float border border-[#ec4899]/30"></div>
          <div className="absolute top-[40%] right-[10%] w-4 h-4 bg-[#d946ef]/30 rounded-full animate-pulse"></div>
          <div className="absolute bottom-[40%] left-[20%] w-3 h-3 bg-white/30 rounded-full animate-ping"></div>
          
          {/* Geometric accents */}
          <div className="absolute top-[25%] left-[60%] w-12 h-12 border border-[#ec4899]/30 rotate-45 animate-spin-slow"></div>
          <div className="absolute bottom-[35%] right-[25%] w-8 h-8 border-2 border-white/20 rounded-full animate-pulse"></div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 sm:px-6 relative z-20 min-h-[60vh] flex flex-col justify-center py-16 sm:py-20">
          <div className="max-w-4xl">
            {/* Brand Tag */}
            <div className="inline-flex items-center mb-6 px-4 py-2 rounded-full border border-[#ec4899]/30 bg-white/5 backdrop-blur-sm animate-fade-in">
              <div className="w-2 h-2 bg-[#ec4899] rounded-full mr-3 animate-pulse"></div>
              <span className="text-accessible-light text-sm font-medium tracking-wider">ENERGY SAVING • NOVEMBER 8, 2023</span>
            </div>

            {/* Main Heading */}
            <h1 className="text-4xl sm:text-6xl md:text-7xl lg:text-8xl font-bold mb-6 animate-slide-up">
              <span className="text-white block mb-2">Understanding</span>
              <span className="text-gradient-logo relative inline-block">
                Window Energy Ratings
                <div className="absolute -bottom-2 left-0 w-full h-1 bg-gradient-to-r from-[#ec4899] to-[#d946ef] rounded-full animate-shimmer"></div>
              </span>
            </h1>

            {/* Description */}
            <p className="text-lg sm:text-xl lg:text-2xl text-accessible-muted mb-8 max-w-2xl leading-relaxed animate-fade-in" style={{ animationDelay: '0.3s' }}>
              Decode window energy ratings and learn what they mean for your home's efficiency and comfort
            </p>

            {/* Author Info */}
            <div className="flex items-center space-x-4 text-accessible-light mb-8 animate-fade-in" style={{ animationDelay: '0.6s' }}>
              <div className="flex items-center">
                <Image 
                  src="/window-warriors-logo.png" 
                  alt="Window Warriors Author" 
                  width={40} 
                  height={40}
                  className="rounded-full border-2 border-white/30 shadow-sm"
                />
                <span className="ml-2.5 font-medium">Window Warriors Team</span>
              </div>
              <span className="text-accessible-subtle">•</span>
              <span className="flex items-center">
                <svg className="w-4 h-4 mr-1.5 text-[#ec4899]" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd"></path>
                </svg>
                12 min read
              </span>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-30 animate-bounce">
          <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-[#ec4899] rounded-full mt-2 animate-pulse"></div>
          </div>
          <p className="text-accessible-subtle text-xs mt-2 text-center">Scroll</p>
        </div>
      </section>

      {/* Article Content */}
      <section className="py-12 md:py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl mx-auto">
            {/* Table of Contents */}
            <div className="bg-gray-50 rounded-xl p-6 mb-12 border border-gray-100 shadow-sm hover:shadow-md transition-shadow duration-300">
              <h2 className="text-lg font-semibold text-[#1a3a6d] mb-4 flex items-center">
                <svg className="w-5 h-5 mr-2 text-[#ec4899]" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z"></path>
                </svg>
                In this article
              </h2>
              <ul className="space-y-3">
                <li>
                  <a href="#introduction" className="text-[#1a3a6d] hover:text-[#ec4899] transition-colors flex items-center group">
                    <span className="w-6 h-6 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-3 group-hover:bg-[#ec4899]/20 transition-colors">1</span>
                    <span className="font-medium">Introduction</span>
                  </a>
                </li>
                <li>
                  <a href="#what-are-wers" className="text-[#1a3a6d] hover:text-[#ec4899] transition-colors flex items-center group">
                    <span className="w-6 h-6 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-3 group-hover:bg-[#ec4899]/20 transition-colors">2</span>
                    <span className="font-medium">What are Window Energy Ratings?</span>
                  </a>
                </li>
                <li>
                  <a href="#rating-explained" className="text-[#1a3a6d] hover:text-[#ec4899] transition-colors flex items-center group">
                    <span className="w-6 h-6 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-3 group-hover:bg-[#ec4899]/20 transition-colors">3</span>
                    <span className="font-medium">The Energy Rating Scale Explained</span>
                  </a>
                </li>
                <li>
                  <a href="#key-factors" className="text-[#1a3a6d] hover:text-[#ec4899] transition-colors flex items-center group">
                    <span className="w-6 h-6 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-3 group-hover:bg-[#ec4899]/20 transition-colors">4</span>
                    <span className="font-medium">Key Factors in Window Energy Performance</span>
                  </a>
                </li>
                <li>
                  <a href="#cost-benefits" className="text-[#1a3a6d] hover:text-[#ec4899] transition-colors flex items-center group">
                    <span className="w-6 h-6 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-3 group-hover:bg-[#ec4899]/20 transition-colors">5</span>
                    <span className="font-medium">Cost vs. Benefits Analysis</span>
                  </a>
                </li>
                <li>
                  <a href="#reading-labels" className="text-[#1a3a6d] hover:text-[#ec4899] transition-colors flex items-center group">
                    <span className="w-6 h-6 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-3 group-hover:bg-[#ec4899]/20 transition-colors">6</span>
                    <span className="font-medium">How to Read and Compare Window Energy Labels</span>
                  </a>
                </li>
                <li>
                  <a href="#conclusion" className="text-[#1a3a6d] hover:text-[#ec4899] transition-colors flex items-center group">
                    <span className="w-6 h-6 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-3 group-hover:bg-[#ec4899]/20 transition-colors">7</span>
                    <span className="font-medium">Conclusion & Recommendations</span>
                  </a>
                </li>
              </ul>
            </div>
            
            {/* Introduction */}
            <div id="introduction" className="mb-16 scroll-mt-24">
              <h2 className="text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-6 pb-2 border-b border-gray-200 flex items-center">
                <span className="w-8 h-8 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-3 text-sm">1</span>
                Introduction
              </h2>
              <div className="prose prose-lg max-w-none">
                <p className="text-xl md:text-2xl text-[#1a3a6d] font-medium mb-5 leading-relaxed">
                  Did you know that up to 25% of your home's heat can be lost through poorly performing windows? In the UK's often challenging climate, especially in the North East, choosing the right energy-efficient windows is not just about comfort—it's about substantial cost savings and reducing your environmental footprint.
                </p>
                
                <p className="text-accessible-gray mb-6 leading-relaxed">
                  At Window Warriors, we've helped thousands of Newcastle homeowners transform their properties with energy-efficient window solutions. Yet we find that while many people know they want "energy-efficient windows," relatively few understand the rating system that helps compare different options and make informed decisions.
                </p>
                
                <div className="bg-blue-50 border-l-4 border-[#1a3a6d] p-5 my-8 rounded-r-xl">
                  <h3 className="text-[#1a3a6d] font-semibold text-lg mb-2">Why Window Energy Ratings Matter</h3>
                  <p className="text-accessible-gray">Window Energy Ratings (WERs) provide a standardized way to compare the energy performance of different windows. Understanding these ratings can help you make better choices for your home, potentially saving thousands of pounds in energy costs over the lifetime of your windows while creating a more comfortable living environment.</p>
                </div>
              </div>
              
              <figure className="my-10 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300">
                <div className="relative h-[300px] md:h-[400px] overflow-hidden">
                  <Image
                    src="/images/beautiful-view-blue-lake-captured-from-inside-villa.jpg"
                    alt="Energy efficient double glazed windows"
                    fill
                    sizes="(max-width: 768px) 100vw, 800px"
                    className="object-cover w-full h-full transition-transform duration-700 hover:scale-105"
                  />
                </div>
                <figcaption className="bg-gray-50 text-accessible-gray text-sm p-4 italic border-t border-gray-100">
                  Modern energy-efficient windows can dramatically improve your home's thermal performance
                </figcaption>
              </figure>
            </div>

            {/* What are Window Energy Ratings? */}
            <div id="what-are-wers" className="mb-16 scroll-mt-24">
              <h2 className="text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-6 pb-2 border-b border-gray-200 flex items-center">
                <span className="w-8 h-8 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-3 text-sm">2</span>
                What are Window Energy Ratings?
              </h2>
              <div className="prose prose-lg max-w-none">
                <p className="text-accessible-gray mb-6 leading-relaxed">
                  Window Energy Ratings (WERs) are a standardized system used in the UK to measure and compare the energy efficiency of windows. Introduced by the British Fenestration Rating Council (BFRC), these ratings allow homeowners to make informed decisions when selecting new windows.
                </p>

                <div className="bg-gray-50 rounded-xl p-6 border border-gray-100 my-8">
                  <h3 className="text-xl font-bold text-[#1a3a6d] mb-4">The Window Energy Rating Label</h3>
                  
                  <div className="flex flex-col md:flex-row gap-6 items-center">
                    <div className="md:w-1/3">
                      <div className="relative h-48 overflow-hidden rounded-lg">
                        <Image 
                          src="/images/front-view-front-door-with-blue-wall.jpg"
                          alt="Window Energy Rating label example"
                          fill
                          sizes="(max-width: 768px) 100vw, 33vw"
                          className="object-cover"
                        />
                      </div>
                    </div>
                    
                    <div className="md:w-2/3">
                      <p className="text-accessible-gray mb-4">
                        Each rated window displays a rainbow-colored label similar to those found on household appliances. The rating scale runs from A++ (most efficient) to E (least efficient), with each grade representing the window's overall energy performance.
                      </p>
                      
                      <p className="text-accessible-gray">
                        The label provides a simple visual way to compare different windows at a glance, making it easier for homeowners to choose the most energy-efficient options for their property.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 my-8">
                  <div className="bg-gray-50 rounded-xl p-6 border border-gray-100 shadow-sm hover:shadow-md transition-shadow duration-300">
                    <h3 className="text-lg font-bold text-[#1a3a6d] mb-4">How Ratings Are Calculated</h3>
                    
                    <p className="text-accessible-gray mb-4">
                      Window energy ratings aren't arbitrary—they're based on a scientific calculation that considers three key factors:
                    </p>
                    
                    <ul className="text-accessible-gray space-y-3">
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#ec4899] mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                        </svg>
                        <span><strong>Solar heat gain (g-value):</strong> How much heat from the sun passes through the window</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#ec4899] mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                        </svg>
                        <span><strong>Heat loss (U-value):</strong> How much heat escapes through the window</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#ec4899] mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                        </svg>
                        <span><strong>Air leakage (L-value):</strong> How much air can pass through the window joints</span>
                      </li>
                    </ul>
                  </div>
                  
                  <div className="bg-gray-50 rounded-xl p-6 border border-gray-100 shadow-sm hover:shadow-md transition-shadow duration-300">
                    <h3 className="text-lg font-bold text-[#1a3a6d] mb-4">The Rating Formula</h3>
                    
                    <p className="text-accessible-gray mb-4">
                      The overall energy rating is determined by the following formula:
                    </p>
                    
                    <div className="bg-white p-4 rounded-lg border border-gray-200 text-center mb-4">
                      <p className="font-medium text-gray-800">
                        Energy Index = Solar Heat Gain – Heat Loss – Air Leakage
                      </p>
                      <p className="text-sm text-accessible-gray mt-1">
                        (g-value × 0.9) – (U-value × 1.0) – (L-value × 0.3)
                      </p>
                    </div>
                    
                    <p className="text-accessible-gray">
                      This calculation produces a single energy index number, which is then mapped to the A++ to E rating scale. The higher the number, the better the window's overall energy performance.
                    </p>
                  </div>
                </div>

                <div className="bg-blue-50 border-l-4 border-[#1a3a6d] p-5 my-8 rounded-r-xl">
                  <h3 className="text-[#1a3a6d] font-semibold text-lg mb-2">Window Warriors Expert Tip</h3>
                  <p className="text-accessible-gray">While the overall rating is useful for quick comparisons, understanding the individual components (U-value, g-value, and L-value) can help you choose windows that are optimized for your specific property's orientation and exposure. For north-facing windows, prioritize low U-values, while south-facing windows might benefit from optimized g-values to balance solar gain with heat retention.</p>
                </div>
              </div>
            </div>

            {/* Understanding the Key Factors: U-value, G-value, and L-value */}
            <div id="key-factors" className="mb-16 scroll-mt-24">
              <h2 className="text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-6 pb-2 border-b border-gray-200 flex items-center">
                <span className="w-8 h-8 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-3 text-sm">3</span>
                Understanding the Key Factors: U-value, G-value, and L-value
              </h2>
              <div className="prose prose-lg max-w-none">
                <p className="text-accessible-gray mb-6 leading-relaxed">
                  To truly understand window energy efficiency, it's essential to delve deeper into the three primary factors that determine a window's performance: the U-value, G-value, and L-value. Each of these measurements provides valuable insight into different aspects of a window's thermal efficiency.
                </p>

                <div className="my-8">
                  <h3 className="text-xl font-bold text-[#1a3a6d] mb-6">U-value: Heat Loss Through the Window</h3>
                  
                  <div className="flex flex-col md:flex-row gap-8 items-center mb-8">
                    <div className="md:w-1/3">
                      <div className="relative h-64 overflow-hidden rounded-lg shadow-md">
                        <Image 
                          src="/images/exterior-home.jpg"
                          alt="Heat loss visualization through a window"
                          fill
                          sizes="(max-width: 768px) 100vw, 33vw"
                          className="object-cover"
                        />
                      </div>
                    </div>
                    
                    <div className="md:w-2/3">
                      <p className="text-accessible-gray mb-4">
                        The U-value measures how easily heat can pass through a window. It quantifies the rate of heat transfer through a structure divided by the difference in temperature across that structure.
                      </p>
                      
                      <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-4">
                        <p className="font-medium text-gray-800">
                          U-value is measured in W/m²K (Watts per square meter per Kelvin)
                        </p>
                      </div>
                      
                      <p className="text-accessible-gray">
                        <strong>Lower U-values indicate better insulation.</strong> For example, a single-glazed window might have a U-value of 5.0 W/m²K, while a high-performance triple-glazed window could have a U-value as low as 0.8 W/m²K. This means the triple-glazed window allows approximately 6 times less heat to escape.
                      </p>
                    </div>
                  </div>
                  
                  <div className="bg-gray-50 rounded-xl p-6 border border-gray-100 mb-8">
                    <h4 className="text-lg font-bold text-[#1a3a6d] mb-3">Typical U-values for Different Window Types</h4>
                    
                    <div className="overflow-x-auto">
                      <table className="min-w-full bg-white">
                        <thead>
                          <tr className="bg-[#1a3a6d] text-white">
                            <th className="py-3 px-4 text-left">Window Type</th>
                            <th className="py-3 px-4 text-left">Typical U-value (W/m²K)</th>
                            <th className="py-3 px-4 text-left">Relative Performance</th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200">
                          <tr>
                            <td className="py-3 px-4">Single glazing</td>
                            <td className="py-3 px-4">4.8 - 5.8</td>
                            <td className="py-3 px-4">
                              <span className="px-2 py-1 rounded-full bg-red-100 text-red-800 text-xs font-medium">
                                Poor
                              </span>
                            </td>
                          </tr>
                          <tr>
                            <td className="py-3 px-4">Double glazing (standard)</td>
                            <td className="py-3 px-4">2.0 - 3.0</td>
                            <td className="py-3 px-4">
                              <span className="px-2 py-1 rounded-full bg-yellow-100 text-yellow-800 text-xs font-medium">
                                Average
                              </span>
                            </td>
                          </tr>
                          <tr>
                            <td className="py-3 px-4">Double glazing (low-E)</td>
                            <td className="py-3 px-4">1.2 - 1.8</td>
                            <td className="py-3 px-4">
                              <span className="px-2 py-1 rounded-full bg-green-100 text-green-800 text-xs font-medium">
                                Good
                              </span>
                            </td>
                          </tr>
                          <tr>
                            <td className="py-3 px-4">Triple glazing</td>
                            <td className="py-3 px-4">0.8 - 1.2</td>
                            <td className="py-3 px-4">
                              <span className="px-2 py-1 rounded-full bg-[#1a3a6d]/10 text-[#1a3a6d] text-xs font-medium">
                                Excellent
                              </span>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>

                <div className="my-8">
                  <h3 className="text-xl font-bold text-[#1a3a6d] mb-6">G-value: Solar Heat Gain</h3>
                  
                  <div className="flex flex-col md:flex-row gap-8 items-center mb-8">
                    <div className="md:w-2/3">
                      <p className="text-accessible-gray mb-4">
                        The G-value (also known as the Solar Heat Gain Coefficient or SHGC) measures how much solar radiation passes through a window. It represents the percentage of solar heat that enters through the window compared to the total solar radiation falling on it.
                      </p>
                      
                      <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-4">
                        <p className="font-medium text-gray-800">
                          G-value is expressed as a number between 0 and 1 (or as a percentage)
                        </p>
                      </div>
                      
                      <p className="text-accessible-gray">
                        <strong>Higher G-values indicate more solar heat gain.</strong> For example, a G-value of 0.7 means 70% of the available solar heat passes through the window. This can be beneficial in winter (providing free heating) but potentially problematic in summer (causing overheating).
                      </p>
                    </div>
                    
                    <div className="md:w-1/3">
                      <div className="relative h-64 overflow-hidden rounded-lg shadow-md">
                        <Image 
                          src="/images/beautiful-view-blue-lake-captured-from-inside-villa.jpg"
                          alt="Solar gain visualization through a window"
                          fill
                          sizes="(max-width: 768px) 100vw, 33vw"
                          className="object-cover"
                        />
                      </div>
                    </div>
                  </div>
                  
                  <div className="bg-yellow-50 border-l-4 border-[#ec4899] p-5 my-6 rounded-r-xl">
                    <h4 className="text-[#1a3a6d] font-semibold text-lg mb-2">Balancing U-value and G-value</h4>
                    <p className="text-accessible-gray">
                      The ideal balance between U-value and G-value depends on your climate and the orientation of your windows. In cold climates, you might want windows with low U-values and higher G-values on south-facing elevations to maximize heat gain in winter. In warmer climates or for windows with high sun exposure, you might prefer lower G-values to prevent overheating.
                    </p>
                  </div>
                </div>

                <div className="my-8">
                  <h3 className="text-xl font-bold text-[#1a3a6d] mb-6">L-value: Air Leakage</h3>
                  
                  <p className="text-accessible-gray mb-6">
                    The L-value measures the amount of air that can pass through the window frame joints. It quantifies how airtight a window is, which affects both energy efficiency and comfort by preventing drafts.
                  </p>
                  
                  <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-6">
                    <p className="font-medium text-gray-800">
                      L-value is measured in m³/h/m² (cubic meters per hour per square meter)
                    </p>
                  </div>
                  
                  <p className="text-accessible-gray mb-6">
                    <strong>Lower L-values indicate less air leakage.</strong> Modern, high-quality windows typically have L-values below 1.0 m³/h/m². Windows with poor seals or aging frames can have much higher values, leading to significant heat loss and uncomfortable drafts.
                  </p>
                  
                  <div className="bg-blue-50 border-l-4 border-[#1a3a6d] p-5 my-6 rounded-r-xl">
                    <h4 className="text-[#1a3a6d] font-semibold text-lg mb-2">Window Warriors Expert Tip</h4>
                    <p className="text-accessible-gray">
                      Even the most thermally efficient glazing will underperform if the window isn't properly installed or has poor airtightness. When replacing windows, ensure that installation is carried out by qualified professionals who use proper sealing techniques around the frame to minimize air leakage.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* How to Read and Compare Window Energy Labels */}
            <div id="reading-labels" className="mb-16 scroll-mt-24">
              <h2 className="text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-6 pb-2 border-b border-gray-200 flex items-center">
                <span className="w-8 h-8 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-3 text-sm">4</span>
                How to Read and Compare Window Energy Labels
              </h2>
              <div className="prose prose-lg max-w-none">
                <p className="text-accessible-gray mb-6 leading-relaxed">
                  When shopping for new windows, understanding how to interpret energy labels is crucial for making informed decisions. The standardized Window Energy Rating (WER) label provides a wealth of information that can help you compare different options.
                </p>

                <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm my-8">
                  <h3 className="text-xl font-bold text-[#1a3a6d] mb-6">Anatomy of a Window Energy Rating Label</h3>
                  
                  <div className="flex flex-col md:flex-row gap-8 items-center">
                    <div className="md:w-1/2">
                      <div className="relative h-96 overflow-hidden rounded-lg border border-gray-200">
                        <Image 
                          src="/images/ancient-window-old-building-quebec-city.jpg"
                          alt="Window Energy Rating label with annotations"
                          fill
                          sizes="(max-width: 768px) 100vw, 50vw"
                          className="object-cover"
                        />
                      </div>
                    </div>
                    
                    <div className="md:w-1/2">
                      <ul className="space-y-4">
                        <li className="flex items-start">
                          <div className="min-w-8 h-8 rounded-full bg-[#1a3a6d] text-white flex items-center justify-center mr-3 text-sm font-bold">1</div>
                          <div>
                            <h4 className="font-semibold text-[#1a3a6d]">Energy Rating Band</h4>
                            <p className="text-accessible-gray text-sm">The letter grade from A++ (most efficient) to E (least efficient) that gives you an at-a-glance indication of the window's overall energy performance.</p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <div className="min-w-8 h-8 rounded-full bg-[#1a3a6d] text-white flex items-center justify-center mr-3 text-sm font-bold">2</div>
                          <div>
                            <h4 className="font-semibold text-[#1a3a6d]">Energy Index</h4>
                            <p className="text-accessible-gray text-sm">The numerical value that determines the rating band. A positive value indicates that the window provides net energy gain.</p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <div className="min-w-8 h-8 rounded-full bg-[#1a3a6d] text-white flex items-center justify-center mr-3 text-sm font-bold">3</div>
                          <div>
                            <h4 className="font-semibold text-[#1a3a6d]">U-value</h4>
                            <p className="text-accessible-gray text-sm">The rate of heat loss through the window (lower is better).</p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <div className="min-w-8 h-8 rounded-full bg-[#1a3a6d] text-white flex items-center justify-center mr-3 text-sm font-bold">4</div>
                          <div>
                            <h4 className="font-semibold text-[#1a3a6d]">G-value</h4>
                            <p className="text-accessible-gray text-sm">The percentage of solar heat gain through the window.</p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <div className="min-w-8 h-8 rounded-full bg-[#1a3a6d] text-white flex items-center justify-center mr-3 text-sm font-bold">5</div>
                          <div>
                            <h4 className="font-semibold text-[#1a3a6d]">L-value</h4>
                            <p className="text-accessible-gray text-sm">The air leakage rating, indicating how airtight the window is.</p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <div className="min-w-8 h-8 rounded-full bg-[#1a3a6d] text-white flex items-center justify-center mr-3 text-sm font-bold">6</div>
                          <div>
                            <h4 className="font-semibold text-[#1a3a6d]">Verification & Certification</h4>
                            <p className="text-accessible-gray text-sm">Look for the BFRC logo and license number to ensure the ratings are independently verified.</p>
                          </div>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>

                <h3 className="text-xl font-bold text-[#1a3a6d] mb-6">Understanding the Rating Bands</h3>
                
                <div className="overflow-x-auto my-6">
                  <table className="min-w-full bg-white border border-gray-200 rounded-lg overflow-hidden">
                    <thead>
                      <tr className="bg-[#1a3a6d] text-white">
                        <th className="py-3 px-4 text-left">Rating</th>
                        <th className="py-3 px-4 text-left">Energy Index (kWh/m²/year)</th>
                        <th className="py-3 px-4 text-left">Efficiency Level</th>
                        <th className="py-3 px-4 text-left">Description</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      <tr className="hover:bg-gray-50">
                        <td className="py-3 px-4">
                          <span className="px-3 py-1 rounded-full bg-[#007d3c] text-white font-bold">A++</span>
                        </td>
                        <td className="py-3 px-4">20 or more</td>
                        <td className="py-3 px-4">Exceptional</td>
                        <td className="py-3 px-4">Net energy gain; contributes more energy to the home than it loses</td>
                      </tr>
                      <tr className="hover:bg-gray-50">
                        <td className="py-3 px-4">
                          <span className="px-3 py-1 rounded-full bg-[#3cb371] text-white font-bold">A+</span>
                        </td>
                        <td className="py-3 px-4">10 to 19</td>
                        <td className="py-3 px-4">Excellent</td>
                        <td className="py-3 px-4">Very high energy performance; likely to provide net energy gain in ideal conditions</td>
                      </tr>
                      <tr className="hover:bg-gray-50">
                        <td className="py-3 px-4">
                          <span className="px-3 py-1 rounded-full bg-[#35a853] text-white font-bold">A</span>
                        </td>
                        <td className="py-3 px-4">0 to 9</td>
                        <td className="py-3 px-4">Very Good</td>
                        <td className="py-3 px-4">High energy performance; minimal heat loss</td>
                      </tr>
                      <tr className="hover:bg-gray-50">
                        <td className="py-3 px-4">
                          <span className="px-3 py-1 rounded-full bg-[#85c446] text-white font-bold">B</span>
                        </td>
                        <td className="py-3 px-4">-10 to -1</td>
                        <td className="py-3 px-4">Good</td>
                        <td className="py-3 px-4">Good thermal performance; meets current building regulations</td>
                      </tr>
                      <tr className="hover:bg-gray-50">
                        <td className="py-3 px-4">
                          <span className="px-3 py-1 rounded-full bg-[#ec4899] text-white font-bold">C</span>
                        </td>
                        <td className="py-3 px-4">-20 to -11</td>
                        <td className="py-3 px-4">Average</td>
                        <td className="py-3 px-4">Standard thermal performance; acceptable for many applications</td>
                      </tr>
                      <tr className="hover:bg-gray-50">
                        <td className="py-3 px-4">
                          <span className="px-3 py-1 rounded-full bg-[#f87c43] text-white font-bold">D</span>
                        </td>
                        <td className="py-3 px-4">-30 to -21</td>
                        <td className="py-3 px-4">Below Average</td>
                        <td className="py-3 px-4">Moderate heat loss; typically older double glazing or basic products</td>
                      </tr>
                      <tr className="hover:bg-gray-50">
                        <td className="py-3 px-4">
                          <span className="px-3 py-1 rounded-full bg-[#e53935] text-white font-bold">E</span>
                        </td>
                        <td className="py-3 px-4">-70 to -31</td>
                        <td className="py-3 px-4">Poor</td>
                        <td className="py-3 px-4">High heat loss; typically single glazing or very old double glazing</td>
                      </tr>
                    </tbody>
                  </table>
                </div>

                <div className="bg-yellow-50 border-l-4 border-[#ec4899] p-5 my-8 rounded-r-xl">
                  <h3 className="text-[#1a3a6d] font-semibold text-lg mb-2">Building Regulations Requirements</h3>
                  <p className="text-accessible-gray">
                    Current UK Building Regulations typically require new windows to achieve a minimum WER of Band C or a U-value of 1.6 W/m²K for replacements in existing dwellings. For new builds, the requirements are more stringent, typically requiring Band B ratings or better. Always check the current regulations as they are periodically updated to improve energy efficiency standards.
                  </p>
                </div>

                <h3 className="text-xl font-bold text-[#1a3a6d] mb-6">Tips for Comparing Window Energy Labels</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 my-8">
                  <div className="bg-gray-50 rounded-xl p-6 border border-gray-100 shadow-sm hover:shadow-md transition-shadow duration-300">
                    <h4 className="text-lg font-bold text-[#1a3a6d] mb-4 flex items-center">
                      <svg className="w-5 h-5 text-[#ec4899] mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd"></path>
                      </svg>
                      Look Beyond the Letter Rating
                    </h4>
                    <p className="text-accessible-gray">
                      While the overall letter rating (A++, A, B, etc.) provides a useful snapshot, don't forget to examine the specific U-value, G-value, and L-value. Two windows with the same overall rating might have different performance characteristics that could be more suitable for your specific needs.
                    </p>
                  </div>
                  
                  <div className="bg-gray-50 rounded-xl p-6 border border-gray-100 shadow-sm hover:shadow-md transition-shadow duration-300">
                    <h4 className="text-lg font-bold text-[#1a3a6d] mb-4 flex items-center">
                      <svg className="w-5 h-5 text-[#ec4899] mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd"></path>
                      </svg>
                      Consider Window Orientation
                    </h4>
                    <p className="text-accessible-gray">
                      For south-facing windows, you might prioritize a balance of U-value and G-value to control solar gain. For north-facing windows, a lower U-value becomes more important as there's less direct sunlight to offset heat loss.
                    </p>
                  </div>
                  
                  <div className="bg-gray-50 rounded-xl p-6 border border-gray-100 shadow-sm hover:shadow-md transition-shadow duration-300">
                    <h4 className="text-lg font-bold text-[#1a3a6d] mb-4 flex items-center">
                      <svg className="w-5 h-5 text-[#ec4899] mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd"></path>
                      </svg>
                      Verify the Certification
                    </h4>
                    <p className="text-accessible-gray">
                      Ensure the windows you're considering have been rated by an accredited body like the BFRC. Look for the certification logo and registration number on the label to confirm the ratings are legitimate and independently verified.
                    </p>
                  </div>
                  
                  <div className="bg-gray-50 rounded-xl p-6 border border-gray-100 shadow-sm hover:shadow-md transition-shadow duration-300">
                    <h4 className="text-lg font-bold text-[#1a3a6d] mb-4 flex items-center">
                      <svg className="w-5 h-5 text-[#ec4899] mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd"></path>
                      </svg>
                      Calculate Potential Energy Savings
                    </h4>
                    <p className="text-accessible-gray">
                      Use the energy index value to estimate potential energy savings compared to your existing windows. While this won't give you an exact figure, it can help you assess the potential return on investment for different window options.
                    </p>
                  </div>
                </div>

                <div className="bg-blue-50 border-l-4 border-[#1a3a6d] p-5 my-8 rounded-r-xl">
                  <h3 className="text-[#1a3a6d] font-semibold text-lg mb-2">Window Warriors Expert Tip</h3>
                  <p className="text-accessible-gray">
                    When comparing prices between different window providers, ensure you're comparing products with similar energy ratings. A significantly cheaper window with a lower energy rating may cost you more in the long run through increased heating bills. Always ask for the window energy label details if they're not prominently displayed.
                  </p>
                </div>
              </div>
            </div>

            {/* Conclusion & Recommendations */}
            <div id="conclusion" className="mb-16 scroll-mt-24">
              <h2 className="text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-6 pb-2 border-b border-gray-200 flex items-center">
                <span className="w-8 h-8 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-3 text-sm">5</span>
                Conclusion & Recommendations
              </h2>
              <div className="prose prose-lg max-w-none">
                <p className="text-accessible-gray mb-6 leading-relaxed">
                  Window energy ratings provide a valuable framework for comparing the thermal performance of different windows and making informed decisions for your home. As energy costs continue to rise and environmental concerns become increasingly important, investing in energy-efficient windows is both economically sensible and environmentally responsible.
                </p>

                <h3 className="text-xl font-bold text-[#1a3a6d] mb-4">Key Takeaways</h3>
                
                <div className="bg-gray-50 rounded-xl p-6 border border-gray-100 my-6">
                  <ul className="space-y-4">
                    <li className="flex items-start">
                      <svg className="w-6 h-6 text-[#ec4899] mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                      </svg>
                      <div>
                        <p className="text-accessible-gray"><strong className="text-[#1a3a6d]">Higher ratings mean better efficiency:</strong> Windows with ratings of A+ or A++ offer the best thermal performance and can contribute to significant energy savings.</p>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <svg className="w-6 h-6 text-[#ec4899] mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                      </svg>
                      <div>
                        <p className="text-accessible-gray"><strong className="text-[#1a3a6d]">Balance is key:</strong> Consider all three performance factors (U-value, G-value, and L-value) rather than focusing solely on the overall rating.</p>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <svg className="w-6 h-6 text-[#ec4899] mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                      </svg>
                      <div>
                        <p className="text-accessible-gray"><strong className="text-[#1a3a6d]">Context matters:</strong> The ideal window specifications vary based on your property's orientation, local climate, and specific requirements.</p>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <svg className="w-6 h-6 text-[#ec4899] mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                      </svg>
                      <div>
                        <p className="text-accessible-gray"><strong className="text-[#1a3a6d]">Installation matters:</strong> Even the most energy-efficient windows will underperform if poorly installed. Professional installation is crucial for achieving optimal performance.</p>
                      </div>
                    </li>
                    <li className="flex items-start">
                      <svg className="w-6 h-6 text-[#ec4899] mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                      </svg>
                      <div>
                        <p className="text-accessible-gray"><strong className="text-[#1a3a6d]">Think long-term:</strong> While energy-efficient windows may have a higher upfront cost, the energy savings over their lifetime typically justify the investment.</p>
                      </div>
                    </li>
                  </ul>
                </div>

                <h3 className="text-xl font-bold text-[#1a3a6d] mb-4">Our Recommendations</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 my-8">
                  <div className="bg-gray-50 rounded-xl p-6 border border-gray-100 shadow-sm hover:shadow-md transition-shadow duration-300">
                    <h4 className="text-lg font-bold text-[#1a3a6d] mb-4 flex items-center">
                      <span className="w-8 h-8 rounded-full bg-[#1a3a6d] text-white flex items-center justify-center mr-3 text-sm">1</span>
                      For Maximum Energy Efficiency
                    </h4>
                    <p className="text-accessible-gray mb-4">
                      Choose windows with an A++ or A+ rating, featuring triple glazing and low-E glass. These provide the best insulation and will minimize heat loss in all conditions.
                    </p>
                    <p className="text-accessible-gray">
                      <strong>Best for:</strong> Homes in cold climates, north-facing elevations, or properties where minimizing heat loss is the primary concern.
                    </p>
                  </div>
                  
                  <div className="bg-gray-50 rounded-xl p-6 border border-gray-100 shadow-sm hover:shadow-md transition-shadow duration-300">
                    <h4 className="text-lg font-bold text-[#1a3a6d] mb-4 flex items-center">
                      <span className="w-8 h-8 rounded-full bg-[#1a3a6d] text-white flex items-center justify-center mr-3 text-sm">2</span>
                      For Balanced Performance
                    </h4>
                    <p className="text-accessible-gray mb-4">
                      Select windows with an A rating that balance good U-values with moderate G-values. This provides excellent thermal performance while managing solar gain.
                    </p>
                    <p className="text-accessible-gray">
                      <strong>Best for:</strong> Most UK homes, mixed orientations, and properties where a good balance of heat retention and solar management is desired.
                    </p>
                  </div>
                  
                  <div className="bg-gray-50 rounded-xl p-6 border border-gray-100 shadow-sm hover:shadow-md transition-shadow duration-300">
                    <h4 className="text-lg font-bold text-[#1a3a6d] mb-4 flex items-center">
                      <span className="w-8 h-8 rounded-full bg-[#1a3a6d] text-white flex items-center justify-center mr-3 text-sm">3</span>
                      For Budget Considerations
                    </h4>
                    <p className="text-accessible-gray mb-4">
                      If budget is a constraint, focus on windows with a B rating that still offer good thermal performance while being more cost-effective than top-rated models.
                    </p>
                    <p className="text-accessible-gray">
                      <strong>Best for:</strong> Rental properties, phased renovation projects, or homeowners working with limited budgets who still want good energy performance.
                    </p>
                  </div>
                </div>

                <p className="text-accessible-gray mb-6">
                  Remember that window energy ratings, while important, are just one factor to consider alongside aesthetics, security features, ventilation options, and durability. The optimal choice for your home will depend on balancing all these factors within your budget and architectural requirements.
                </p>

                <p className="text-accessible-gray">
                  By understanding window energy ratings, you'll be well-equipped to make informed decisions that can enhance your home's comfort, reduce your energy bills, and minimize your environmental footprint for years to come.
                </p>
              </div>
            </div>

            {/* Call to Action */}
            <div className="bg-gradient-to-r from-[#1a3a6d] to-[#2a5ca3] rounded-xl overflow-hidden shadow-lg mb-16">
              <div className="p-8 md:p-10">
                <h3 className="text-2xl font-bold text-white mb-4">Need Help Choosing Energy-Efficient Windows?</h3>
                <p className="text-gray-100 mb-6">
                  Our window specialists can guide you through the options and help you select the perfect energy-efficient windows for your home. Book a free consultation today!
                </p>
                <div className="flex flex-col sm:flex-row gap-4">
                  <Link href="/contact" className="bg-white text-[#1a3a6d] px-6 py-3 rounded-lg font-semibold hover:bg-[#ec4899] hover:text-white transition-colors inline-flex items-center justify-center">
                    <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                    </svg>
                    Book a Consultation
                  </Link>
                  <Link href="/projects" className="bg-transparent text-white border border-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-[#1a3a6d] transition-colors inline-flex items-center justify-center">
                    <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd"></path>
                    </svg>
                    View Our Projects
                  </Link>
                </div>
              </div>
            </div>

            {/* Author Section */}
            <div className="mb-16">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-16 h-16 rounded-full overflow-hidden border-2 border-[#1a3a6d]">
                  <Image 
                    src="/images/blonde-woman-architect-working-with-blueprints.jpg"
                    alt="James W."
                    width={64}
                    height={64}
                    className="object-cover w-full h-full"
                    loading="lazy"
                  />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-[#1a3a6d]">James W.</h3>
                  <p className="text-accessible-gray text-sm">Thermal Performance Specialist | Window Warriors</p>
                </div>
              </div>
              <p className="text-accessible-gray text-sm italic">
                James has over 15 years of experience in the fenestration industry, specializing in thermal performance testing and window energy certifications. He regularly contributes to industry publications and has helped numerous homeowners optimize their window choices for maximum energy efficiency.
              </p>
            </div>

            {/* Related Articles */}
            <div className="mb-16">
              <h3 className="text-xl font-bold text-[#1a3a6d] mb-6">Related Articles</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Link href="/blog/posts/energy-efficiency-windows" className="group">
                  <div className="bg-white rounded-xl overflow-hidden border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
                    <div className="relative h-48">
                      <Image 
                        src="/images/exterior-home.jpg"
                        alt="Energy Efficiency: How New Windows Can Reduce Your Bills"
                        fill
                        sizes="(max-width: 768px) 100vw, 50vw"
                        loading="lazy"
                        className="object-cover"
                      />
                    </div>
                    <div className="p-5">
                      <h4 className="text-lg font-bold text-[#1a3a6d] group-hover:text-[#ec4899] transition-colors">Energy Efficiency: How New Windows Can Reduce Your Bills</h4>
                      <p className="text-accessible-gray text-sm mt-2">Find out how modern window technology can significantly cut your energy bills and make your home more comfortable year-round.</p>
                    </div>
                  </div>
                </Link>
                
                <Link href="/blog/posts/upvc-vs-aluminum-windows-comparison" className="group">
                  <div className="bg-white rounded-xl overflow-hidden border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
                    <div className="relative h-48">
                      <Image 
                        src="/images/exterior-home.jpg"
                        alt="UPVC vs. Aluminum Windows: A Comprehensive Comparison"
                        fill
                        sizes="(max-width: 768px) 100vw, 50vw"
                        loading="lazy"
                        className="object-cover"
                      />
                    </div>
                    <div className="p-5">
                      <h4 className="text-lg font-bold text-[#1a3a6d] group-hover:text-[#ec4899] transition-colors">UPVC vs. Aluminum Windows: A Comprehensive Comparison</h4>
                      <p className="text-accessible-gray text-sm mt-2">Compare the pros and cons of UPVC and aluminum window frames to find the best option for your home's energy efficiency needs.</p>
                    </div>
                  </div>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
} 


