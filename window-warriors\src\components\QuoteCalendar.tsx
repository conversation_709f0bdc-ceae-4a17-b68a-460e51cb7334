'use client';

import { useEffect } from 'react';

interface QuoteCalendarProps {
  title?: string;
  subtitle?: string;
  className?: string;
}

const QuoteCalendar = ({
  title = "Get Your Free Quote Today",
  subtitle = "Schedule your free consultation and get a personalized quote for your UPVC windows, doors or conservatory project",
  className = '',
}: QuoteCalendarProps) => {
  
  useEffect(() => {
    // Load the Go High Level form embed script
    const script = document.createElement('script');
    script.src = 'https://link.msgsndr.com/js/form_embed.js';
    script.type = 'text/javascript';
    script.async = true;
    document.body.appendChild(script);
    
    return () => {
      // Clean up
      try {
        document.body.removeChild(script);
      } catch (e) {
        console.log('Error removing script:', e);
      }
    };
  }, []);

  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 md:p-8 ${className}`}>
      <div className="text-center mb-6">
        <h3 className="text-2xl font-bold text-[#1a3a6d]">{title}</h3>
        <p className="text-gray-600 mt-2">{subtitle}</p>
      </div>
      
      <div className="bg-gray-100 p-4 rounded-lg mb-6 border border-gray-200">
        <p className="text-sm sm:text-base text-gray-700">
          Select your preferred date and time below for your free consultation.
        </p>
      </div>
      
      {/* Calendar container with your new embed code */}
      <div className="rounded-lg overflow-hidden bg-white">
        <div 
          dangerouslySetInnerHTML={{ 
            __html: `
              <iframe 
                src="https://api.leadconnectorhq.com/widget/booking/j3hsqwv91ZCBz4pLrbmx" 
                style="width: 100%;border:none;overflow: hidden;" 
                scrolling="no" 
                id="j3hsqwv91ZCBz4pLrbmx_1753740272640">
              </iframe>
              <script src="https://link.msgsndr.com/js/form_embed.js" type="text/javascript"></script>
            `
          }} 
        />
      </div>
    </div>
  );
};

export default QuoteCalendar;
