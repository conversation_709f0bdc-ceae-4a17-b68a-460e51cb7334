'use client';

import { useEffect, useState } from 'react';
import { hasCookieConsent } from '@/utils/cookieUtils';
import AnalyticsProvider from './AnalyticsProvider';

export default function AnalyticsProviderWrapper() {
  const [consentGiven, setConsentGiven] = useState(false);

  // Initialize consent state when component mounts
  useEffect(() => {
    setConsentGiven(hasCookieConsent());

    // Listen for changes in cookie consent
    const handleStorageChange = () => {
      setConsentGiven(hasCookieConsent());
    };

    window.addEventListener('storage', handleStorageChange);
    
    // Also listen for custom event when consent is given without storage event
    window.addEventListener('consentUpdated', handleStorageChange);
    
    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('consentUpdated', handleStorageChange);
    };
  }, []);

  // Render nothing if no consent, otherwise render AnalyticsProvider
  return consentGiven ? <AnalyticsProvider /> : null;
} 