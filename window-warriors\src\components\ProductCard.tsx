import Link from 'next/link';
import Image from 'next/image';

interface ProductCardProps {
  id: string;
  title: string;
  description: string;
  imageSrc: string;
  imageAlt: string;
  href: string;
  features?: string[];
  className?: string;
}

const ProductCard = ({
  id,
  title,
  description,
  imageSrc,
  imageAlt,
  href,
  features = [],
  className = '',
}: ProductCardProps) => {
  return (
    <div className={`bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg ${className}`}>
      {/* Product Image */}
      <div className="relative h-64 w-full overflow-hidden">
        <Image
          src={imageSrc}
          alt={imageAlt}
          fill
          className="object-cover transition-transform hover:scale-105 duration-500"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />
      </div>

      {/* Product Content */}
      <div className="p-6">
        <h3 className="text-xl font-bold text-[#1a3a6d] mb-2">{title}</h3>
        <p className="text-accessible-gray mb-4">{description}</p>
        
        {/* Product Features */}
        {features.length > 0 && (
          <ul className="mb-6 space-y-1">
            {features.map((feature, index) => (
              <li key={`${id}-feature-${index}`} className="flex items-start">
                <svg 
                  className="w-5 h-5 text-green-500 mr-2 mt-0.5" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24" 
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth="2" 
                    d="M5 13l4 4L19 7"
                  />
                </svg>
                <span className="text-accessible-gray">{feature}</span>
              </li>
            ))}
          </ul>
        )}
        
        {/* CTA Button */}
        <Link 
          href={href}
          className="inline-block w-full text-center bg-[#1a3a6d] hover:bg-[#14305d] text-white font-semibold py-2 px-4 rounded-md transition duration-300"
        >
          Learn More
        </Link>
      </div>
    </div>
  );
};

export default ProductCard; 