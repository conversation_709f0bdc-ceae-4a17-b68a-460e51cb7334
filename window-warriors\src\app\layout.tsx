import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { PromotionNotificationWrapper, ExitIntentPopupWrapper, <PERSON><PERSON>entWrapper, AnalyticsProviderWrapper } from "@/components/ClientComponents";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

// Define the base URL
const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://windowwarriors.uk';

export const metadata: Metadata = {
  title: 'Window Warriors | Premium UPVC Windows, Doors & Conservatories in Newcastle',
  description: 'Transform your home with our premium UPVC windows, doors, and conservatories. Serving Newcastle, Durham and the North East with expert installation and exceptional service.',
  keywords: 'UPVC windows Newcastle, door installation Durham, conservatory fitters North East, energy efficient windows UK',
  authors: [{ name: 'Window Warriors' }],
  creator: 'Window Warriors',
  publisher: 'Window Warriors',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(baseUrl),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    siteName: 'Window Warriors',
    title: 'Window Warriors | Premium UPVC Windows & Doors',
    description: 'Expert UPVC window and door installation across Newcastle and the North East. Energy-efficient, secure, and beautifully crafted products.',
    url: baseUrl,
    locale: 'en_GB',
    images: [
      {
        url: '/images/optimized/beautiful-view-blue-lake-captured-from-inside-villa.webp',
        width: 1200,
        height: 630,
        alt: 'Window Warriors - Premium UPVC Windows & Doors',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Window Warriors | Premium UPVC Windows & Doors in Newcastle',
    description: 'Expert UPVC window and door installation across Newcastle and the North East.',
    images: ['/images/optimized/beautiful-view-blue-lake-captured-from-inside-villa.webp'],
  },
  robots: {
    index: true,
    follow: true,
  },
  icons: {
    icon: '/window-warriors-logo.png',
    apple: '/window-warriors-logo.png',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/window-warriors-logo.png" />
        <link rel="apple-touch-icon" href="/window-warriors-logo.png" />
        <link rel="canonical" href={baseUrl} />
        {/* Mobile viewport optimization */}
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover" />
        
        {/* Preload critical assets */}
        <link rel="preload" href="/images/optimized/beautiful-view-blue-lake-captured-from-inside-villa.webp" as="image" />
        <link rel="preload" href="/window-warriors-logo.png" as="image" />
        
        {/* DNS prefetch for external resources */}
        <link rel="dns-prefetch" href="//www.googletagmanager.com" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        
        {/* Google Analytics - loaded conditionally via client-side only */}
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
        suppressHydrationWarning
      >
        <Header />
        {children}
        <Footer />
        <PromotionNotificationWrapper />
        <ExitIntentPopupWrapper />
        <CookieConsentWrapper />
        <AnalyticsProviderWrapper />
      </body>
    </html>
  );
}