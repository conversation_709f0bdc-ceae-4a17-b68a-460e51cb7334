import Link from 'next/link';
import Image from 'next/image';
import React from 'react';

const Footer = () => {
  return (
    <footer className="bg-[#1a3a6d] relative overflow-hidden">
      {/* Enhanced background effects */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Gradient overlays */}
        <div className="absolute inset-0 bg-gradient-to-br from-[#1a3a6d] via-[#0f172a] to-[#1a3a6d] opacity-90"></div>
        
        {/* Floating elements - Mobile optimized */}
        <div className="absolute top-1/4 right-1/4 w-32 sm:w-64 md:w-80 lg:w-[40rem] h-32 sm:h-64 md:h-80 lg:h-[40rem] rounded-full bg-[#ec4899]/5 blur-3xl pointer-events-none"></div>
        <div className="absolute bottom-1/3 left-1/4 w-24 sm:w-48 md:w-64 h-24 sm:h-48 md:h-64 rounded-full bg-[#d946ef]/10 blur-3xl"></div>
        
        {/* Animated background particles */}
        <div className="absolute top-1/4 left-1/4 w-1.5 h-1.5 sm:w-2 sm:h-2 bg-white/20 rounded-full animate-pulse"></div>
        <div className="absolute top-1/2 right-1/3 w-1 h-1 sm:w-1.5 sm:h-1.5 bg-white/30 rounded-full animate-pulse" style={{animationDelay: '1s'}}></div>
        <div className="absolute bottom-1/4 left-1/2 w-0.5 h-0.5 sm:w-1 sm:h-1 bg-white/25 rounded-full animate-pulse" style={{animationDelay: '2s'}}></div>
      </div>
      
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-10 sm:py-12 md:py-16 lg:py-20 relative z-10">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8 lg:gap-10 mb-8 sm:mb-10 lg:mb-12">
          
          {/* Company Info - Mobile optimized */}
          <div className="lg:col-span-2 space-y-4 sm:space-y-5 lg:space-y-6 text-center sm:text-left">
            <div className="flex items-center justify-center sm:justify-start">
              <Image 
                src="/window-warriors-logo.png" 
                alt="Window Warriors Logo" 
                width={180} 
                height={54}
                className="max-w-[140px] sm:max-w-[160px] md:max-w-[180px] lg:max-w-[200px] h-auto"
                priority
              />
            </div>
            <p className="text-accessible-muted text-sm sm:text-base lg:text-base leading-relaxed max-w-md mx-auto sm:mx-0">
              Transform your home with Window Warriors. We provide premium UPVC windows, doors, and conservatories throughout the North East with unmatched craftsmanship and customer service.
            </p>
            <div className="flex flex-col space-y-3 sm:space-y-4">
              <div className="flex items-center justify-center sm:justify-start text-accessible-muted text-sm sm:text-base">
                <span className="w-2 h-2 bg-[#ec4899] rounded-full mr-2"></span>
                <span>Serving Newcastle & North East</span>
              </div>
              <div className="flex items-center justify-center sm:justify-start text-accessible-muted text-sm sm:text-base">
                <span className="w-2 h-2 bg-[#ec4899] rounded-full mr-2"></span>
                <span>15+ Years Experience</span>
              </div>
            </div>
          </div>

          {/* Quick Links - Mobile optimized */}
          <div className="text-center sm:text-left">
            <h3 className="text-white font-bold text-lg sm:text-xl mb-4 sm:mb-5 lg:mb-6 relative">
              Quick Links
              <span className="absolute -bottom-2 left-1/2 sm:left-0 transform -translate-x-1/2 sm:translate-x-0 w-12 h-1 bg-[#ec4899]/50 rounded-full"></span>
            </h3>
            <ul className="space-y-2 sm:space-y-3">
              <li>
                <Link href="/" className="text-accessible-muted hover:text-[#ec4899] transition-all duration-300 flex items-center group justify-center sm:justify-start text-sm sm:text-base py-1">
                  <span className="w-0 h-px bg-[#ec4899] mr-0 group-hover:w-3 sm:group-hover:w-4 group-hover:mr-2 transition-all duration-300"></span>
                  Home
                </Link>
              </li>
              <li>
                <Link href="/about" className="text-accessible-muted hover:text-[#ec4899] transition-all duration-300 flex items-center group justify-center sm:justify-start text-sm sm:text-base py-1">
                  <span className="w-0 h-px bg-[#ec4899] mr-0 group-hover:w-3 sm:group-hover:w-4 group-hover:mr-2 transition-all duration-300"></span>
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/products-services" className="text-accessible-muted hover:text-[#ec4899] transition-all duration-300 flex items-center group justify-center sm:justify-start text-sm sm:text-base py-1">
                  <span className="w-0 h-px bg-[#ec4899] mr-0 group-hover:w-3 sm:group-hover:w-4 group-hover:mr-2 transition-all duration-300"></span>
                  Products & Services
                </Link>
              </li>
              <li>
                <Link href="/gallery" className="text-accessible-muted hover:text-[#ec4899] transition-all duration-300 flex items-center group justify-center sm:justify-start text-sm sm:text-base py-1">
                  <span className="w-0 h-px bg-[#ec4899] mr-0 group-hover:w-3 sm:group-hover:w-4 group-hover:mr-2 transition-all duration-300"></span>
                  Gallery
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-accessible-muted hover:text-[#ec4899] transition-all duration-300 flex items-center group justify-center sm:justify-start text-sm sm:text-base py-1">
                  <span className="w-0 h-px bg-[#ec4899] mr-0 group-hover:w-3 sm:group-hover:w-4 group-hover:mr-2 transition-all duration-300"></span>
                  Contact
                </Link>
              </li>
            </ul>
          </div>

          {/* Services - Mobile optimized */}
          <div className="text-center sm:text-left">
            <h3 className="text-white font-bold text-lg sm:text-xl mb-4 sm:mb-5 lg:mb-6 relative">
              Our Services
              <span className="absolute -bottom-2 left-1/2 sm:left-0 transform -translate-x-1/2 sm:translate-x-0 w-12 h-1 bg-[#ec4899]/50 rounded-full"></span>
            </h3>
            <ul className="space-y-2 sm:space-y-3">
              <li>
                <Link href="/products-services#windows" className="text-accessible-muted hover:text-[#ec4899] transition-all duration-300 flex items-center group justify-center sm:justify-start text-sm sm:text-base py-1">
                  <span className="w-0 h-px bg-[#ec4899] mr-0 group-hover:w-3 sm:group-hover:w-4 group-hover:mr-2 transition-all duration-300"></span>
                  UPVC Windows
                </Link>
              </li>
              <li>
                <Link href="/products-services#doors" className="text-accessible-muted hover:text-[#ec4899] transition-all duration-300 flex items-center group justify-center sm:justify-start text-sm sm:text-base py-1">
                  <span className="w-0 h-px bg-[#ec4899] mr-0 group-hover:w-3 sm:group-hover:w-4 group-hover:mr-2 transition-all duration-300"></span>
                  Doors
                </Link>
              </li>
              <li>
                <Link href="/products-services#conservatories" className="text-accessible-muted hover:text-[#ec4899] transition-all duration-300 flex items-center group justify-center sm:justify-start text-sm sm:text-base py-1">
                  <span className="w-0 h-px bg-[#ec4899] mr-0 group-hover:w-3 sm:group-hover:w-4 group-hover:mr-2 transition-all duration-300"></span>
                  Conservatories
                </Link>
              </li>
              <li>
                <Link href="/products-services#repairs" className="text-accessible-muted hover:text-[#ec4899] transition-all duration-300 flex items-center group justify-center sm:justify-start text-sm sm:text-base py-1">
                  <span className="w-0 h-px bg-[#ec4899] mr-0 group-hover:w-3 sm:group-hover:w-4 group-hover:mr-2 transition-all duration-300"></span>
                  Window Repairs
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Contact Section - Mobile optimized */}
        <div className="border-t border-white/10 pt-6 sm:pt-8 lg:pt-10">
          <h3 className="text-white font-bold text-lg sm:text-xl mb-5 sm:mb-6 text-center relative">
            Get In Touch
            <span className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-12 h-1 bg-[#ec4899]/50 rounded-full"></span>
          </h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8 mb-6 sm:mb-8 lg:mb-10">
            {/* Phone */}
            <div className="group text-center sm:text-left">
              <div className="flex items-center justify-center sm:justify-start mb-2 sm:mb-3">
                <div className="flex-shrink-0 w-10 h-10 sm:w-11 sm:h-11 rounded-full bg-white/10 group-hover:bg-[#ec4899]/20 flex items-center justify-center mr-3 sm:mr-4 transition-colors duration-300">
                  <svg className="w-4 h-4 sm:w-5 sm:h-5 text-[#ec4899]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                  </svg>
                </div>
                <div className="text-left">
                  <h4 className="text-white font-medium mb-1 text-sm sm:text-base">Call Us</h4>
                  <a href="tel:+441913592774" className="text-accessible-muted transition-colors duration-300 hover:text-[#ec4899] text-sm sm:text-base font-medium">
                    0191 359 2774
                  </a>
                </div>
              </div>
            </div>
            
            {/* Email */}
            <div className="group text-center sm:text-left">
              <div className="flex items-center justify-center sm:justify-start mb-2 sm:mb-3">
                <div className="flex-shrink-0 w-10 h-10 sm:w-11 sm:h-11 rounded-full bg-white/10 group-hover:bg-[#ec4899]/20 flex items-center justify-center mr-3 sm:mr-4 transition-colors duration-300">
                  <svg className="w-4 h-4 sm:w-5 sm:h-5 text-[#ec4899]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
                <div className="text-left">
                  <h4 className="text-white font-medium mb-1 text-sm sm:text-base">Email Us</h4>
                  <a href="mailto:<EMAIL>" className="text-accessible-muted transition-colors duration-300 hover:text-[#ec4899] text-sm sm:text-base font-medium break-all">
                    <EMAIL>
                  </a>
                </div>
              </div>
            </div>
            
            {/* Business Hours */}
            <div className="group text-center sm:text-left sm:col-span-2 lg:col-span-1">
              <div className="flex items-center justify-center sm:justify-start mb-2 sm:mb-3">
                <div className="flex-shrink-0 w-10 h-10 sm:w-11 sm:h-11 rounded-full bg-white/10 group-hover:bg-[#ec4899]/20 flex items-center justify-center mr-3 sm:mr-4 transition-colors duration-300">
                  <svg className="w-4 h-4 sm:w-5 sm:h-5 text-[#ec4899]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="text-left">
                  <h4 className="text-white font-medium mb-1 text-sm sm:text-base">Business Hours</h4>
                  <p className="text-accessible-muted text-sm sm:text-base">Mon-Fri: 8am-6pm</p>
                </div>
              </div>
            </div>
          </div>

          {/* Social Media - Mobile optimized */}
          <div className="mb-6 sm:mb-8 lg:mb-10">
            <h4 className="text-white font-medium text-center mb-4 sm:mb-5 text-sm sm:text-base">Follow Us</h4>
            <div className="flex justify-center space-x-3 sm:space-x-4 lg:space-x-6">
              <a href="https://www.facebook.com/share/16AtRv9XUC/?mibextid=wwXIfr" target="_blank" rel="noopener noreferrer" className="group w-11 h-11 sm:w-12 sm:h-12 lg:w-14 lg:h-14 rounded-full bg-white/10 hover:bg-[#1877F2]/80 flex items-center justify-center transition-all duration-300 hover:scale-110 hover:shadow-lg hover:shadow-[#1877F2]/20" aria-label="Follow us on Facebook">
                <svg className="w-5 h-5 sm:w-6 sm:h-6 text-[#ec4899] group-hover:text-white transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18 2h-3a5 5 0 00-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 011-1h3z" />
                </svg>
              </a>
              
              <a href="https://www.instagram.com/windowwarriors.uk/" target="_blank" rel="noopener noreferrer" className="group w-11 h-11 sm:w-12 sm:h-12 lg:w-14 lg:h-14 rounded-full bg-white/10 hover:bg-gradient-to-br hover:from-[#833AB4] hover:via-[#FD1D1D] hover:to-[#F56040] flex items-center justify-center transition-all duration-300 hover:scale-110 hover:shadow-lg hover:shadow-pink-500/20" aria-label="Follow us on Instagram">
                <svg className="w-5 h-5 sm:w-6 sm:h-6 text-[#ec4899] group-hover:text-white transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <rect width="20" height="20" x="2" y="2" rx="5" ry="5"/>
                  <path d="m16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"/>
                  <line x1="17.5" x2="17.51" y1="6.5" y2="6.5"/>
                </svg>
              </a>
              
              <a href="https://www.tiktok.com/@windowarriors?_t=ZN-8vO56sswgEw&_r=1" target="_blank" rel="noopener noreferrer" className="group w-11 h-11 sm:w-12 sm:h-12 lg:w-14 lg:h-14 rounded-full bg-white/10 hover:bg-[#000000]/80 flex items-center justify-center transition-all duration-300 hover:scale-110 hover:shadow-lg hover:shadow-black/20" aria-label="Follow us on TikTok">
                <svg className="w-5 h-5 sm:w-6 sm:h-6 text-[#ec4899] group-hover:text-white transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l4-4v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2z" />
                </svg>
              </a>
              
              <a href="https://www.youtube.com/" target="_blank" rel="noopener noreferrer" className="group w-11 h-11 sm:w-12 sm:h-12 lg:w-14 lg:h-14 rounded-full bg-white/10 hover:bg-[#FF0000]/80 flex items-center justify-center transition-all duration-300 hover:scale-110 hover:shadow-lg hover:shadow-red-500/20" aria-label="Follow us on YouTube">
                <svg className="w-5 h-5 sm:w-6 sm:h-6 text-[#ec4899] group-hover:text-white transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.5a2.5 2.5 0 015 0H17m-8 8h.01M8 21l4-7 4 7M3 4h18M4 4h16v10a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                </svg>
              </a>
            </div>
          </div>
        </div>

        {/* Bottom section - Mobile optimized */}
        <div className="border-t border-white/10 pt-5 sm:pt-6 lg:pt-8 text-center">
          <div className="flex flex-col sm:flex-row justify-between items-center space-y-3 sm:space-y-0">
            <p className="text-accessible-subtle text-xs sm:text-sm order-2 sm:order-1">
              © {new Date().getFullYear()} Window Warriors. All rights reserved.
            </p>
            <div className="flex flex-wrap justify-center sm:justify-end gap-3 sm:gap-4 lg:gap-6 order-1 sm:order-2">
              <Link 
                href="/privacy-policy" 
                className="text-accessible-subtle text-xs sm:text-sm hover:text-[#ec4899] transition-colors duration-300 relative z-20 pointer-events-auto py-2 px-1 hover:underline"
              >
                Privacy Policy
              </Link>
              <Link 
                href="/terms-of-service" 
                className="text-accessible-subtle text-xs sm:text-sm hover:text-[#ec4899] transition-colors duration-300 relative z-20 pointer-events-auto py-2 px-1 hover:underline"
              >
                Terms of Service
              </Link>
              <Link 
                href="/cookies" 
                className="text-accessible-subtle text-xs sm:text-sm hover:text-[#ec4899] transition-colors duration-300 relative z-20 pointer-events-auto py-2 px-1 hover:underline"
              >
                Cookie Policy
              </Link>
              <Link 
                href="/sitemap" 
                className="text-accessible-subtle text-xs sm:text-sm hover:text-[#ec4899] transition-colors duration-300 relative z-20 pointer-events-auto py-2 px-1 hover:underline"
              >
                Sitemap
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer; 