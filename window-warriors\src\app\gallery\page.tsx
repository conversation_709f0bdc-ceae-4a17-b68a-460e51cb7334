import React from 'react';
import { Metadata } from 'next';
import Image from 'next/image';
import Link from 'next/link';
import GalleryContent from '@/components/GalleryContent';

export const metadata: Metadata = {
  title: 'Our Project Gallery | Window Warriors UPVC Windows & Doors',
  description: 'Browse our gallery of premium UPVC window and door installations across Newcastle, Durham and the North East. See the quality of our work in homes like yours.',
  keywords: 'UPVC window gallery UK, door installation photos Newcastle, conservatory projects Durham, window transformations North East',
  openGraph: {
    title: 'Window Warriors Gallery | See Our Premium UPVC Projects',
    description: 'Browse our showcase of premium UPVC window and door installations across Newcastle and the North East. View our craftsmanship and get inspired.',
    url: 'https://windowwarriors.uk/gallery',
    siteName: 'Window Warriors',
    images: [
      {
        url: 'https://windowwarriors.uk/images/beautiful-view-blue-lake-captured-from-inside-villa.jpg',
        width: 1200,
        height: 630,
        alt: 'Window Warriors Project Gallery',
      },
    ],
    locale: 'en_GB',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Window Warriors Gallery | Premium UPVC Window & Door Projects',
    description: 'Browse our showcase of premium UPVC window and door installations across Newcastle and the North East.',
    images: ['https://windowwarriors.uk/images/beautiful-view-blue-lake-captured-from-inside-villa.jpg'],
  },
  alternates: {
    canonical: 'https://windowwarriors.uk/gallery',
  },
};

export default function GalleryPage() {
  return (
    <main className="flex min-h-screen flex-col">
      {/* Enhanced Hero Section - Mobile Optimized */}
      <section className="relative min-h-[500px] sm:min-h-[600px] lg:min-h-screen py-16 sm:py-20 md:py-24 lg:py-32 overflow-hidden">
        {/* Optimized background image */}
        <div className="absolute inset-0">
          <Image
            src="/images/optimized/old-brick-house-paved-street.webp"
            alt="Beautiful window installation project showcase"
            fill
            priority
            className="object-cover object-center"
            sizes="(max-width: 768px) 100vw, 100vw"
            quality={80}
            placeholder="blur"
            blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
          />
          {/* Enhanced gradient overlay with pink accent */}
          <div className="absolute inset-0 bg-gradient-to-br from-[#1a3a6d]/90 via-[#1a3a6d]/80 to-[#ec4899]/30"></div>
          <div className="absolute inset-0 bg-gradient-to-t from-[#0f172a]/60 via-transparent to-transparent"></div>
        </div>
        
        {/* Simplified decorative elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/6 left-1/6 w-32 sm:w-48 md:w-64 lg:w-80 h-32 sm:h-48 md:h-64 lg:h-80 bg-[#ec4899]/20 rounded-full blur-3xl animate-float-slow"></div>
          <div className="absolute bottom-1/6 right-1/6 w-40 sm:w-64 md:w-80 lg:w-[30rem] h-40 sm:h-64 md:h-80 lg:h-[30rem] bg-[#d946ef]/15 rounded-full blur-3xl animate-float-slow" style={{ animationDelay: '2s' }}></div>
        </div>
        
        {/* Enhanced Hero Content - Mobile Optimized */}
        <div className="container mx-auto px-4 sm:px-6 relative z-10 flex flex-col justify-center min-h-[500px] sm:min-h-[600px] lg:min-h-screen pb-8 sm:pb-12 lg:pb-16">
          <div className="max-w-4xl animate-fade-in" style={{ animationDelay: '0.3s' }}>
            {/* Enhanced brand badge - Mobile responsive */}
            <div className="inline-flex items-center mb-4 sm:mb-6 md:mb-8">
              <div className="bg-white/10 backdrop-blur-sm border border-[#ec4899]/30 rounded-full px-4 sm:px-6 py-1.5 sm:py-2 animate-fade-blur" style={{ animationDelay: '0.2s' }}>
                <span className="text-accessible-light text-xs sm:text-sm md:text-base font-medium">OUR GALLERY</span>
                <div className="w-full h-px bg-gradient-to-r from-transparent via-[#ec4899]/70 to-transparent mt-1"></div>
              </div>
            </div>
            
            {/* Enhanced title with pink gradients - Mobile responsive */}
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl 2xl:text-8xl font-bold text-white mb-3 sm:mb-4 md:mb-6 leading-tight">
              Project <span className="relative inline-block">
                <span className="text-gradient-logo">Showcase</span>
                <div className="absolute -bottom-1 sm:-bottom-2 left-0 w-full h-0.5 sm:h-1 bg-gradient-to-r from-[#ec4899] to-[#d946ef] rounded-full animate-shimmer"></div>
                <div className="absolute -top-0.5 sm:-top-1 -right-0.5 sm:-right-1 w-2 sm:w-3 h-2 sm:h-3 bg-[#ec4899]/60 rounded-full animate-pulse"></div>
              </span>
            </h1>
            
            {/* Enhanced description - Mobile responsive */}
            <div className="relative mb-4 sm:mb-6 md:mb-8">
              <p className="text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl text-accessible-light leading-relaxed animate-slide-up" style={{ animationDelay: '0.6s' }}>
                Browse our portfolio of completed window, door, and conservatory projects across Newcastle and the North East.
              </p>
              <div className="absolute -left-2 sm:-left-4 top-0 w-0.5 sm:w-1 h-full bg-gradient-to-b from-[#ec4899] to-transparent rounded-full opacity-60"></div>
            </div>
            
            {/* Enhanced buttons - Mobile responsive */}
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 md:gap-6 mb-6 sm:mb-8 md:mb-10 animate-slide-up" style={{ animationDelay: '0.9s' }}>
              <Link href="/contact" className="btn-primary text-center text-sm sm:text-base">
                Get a Free Quote
              </Link>
              <Link href="/products-services" className="inline-block bg-white/10 backdrop-blur-sm border-2 border-white/30 text-white hover:bg-white hover:text-[#1a3a6d] font-medium py-3 sm:py-4 px-6 sm:px-8 rounded-full transition-all duration-300 text-center group text-sm sm:text-base">
                <span className="flex items-center justify-center">
                  <svg className="w-4 sm:w-5 h-4 sm:h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                  </svg>
                  Explore Our Services
                </span>
              </Link>
            </div>
            
            {/* Enhanced filter section - Mobile responsive */}
            <div className="animate-fade-in" style={{ animationDelay: '1.2s' }}>
              <p className="text-white/70 text-xs sm:text-sm md:text-base mb-3 sm:mb-4">Filter projects by type</p>
              <div className="flex flex-wrap gap-2 sm:gap-3">
                {[
                  { label: 'All Projects', active: true },
                  { label: 'Windows', active: false },
                  { label: 'Doors', active: false },
                  { label: 'Conservatories', active: false },
                  { label: 'Repairs', active: false }
                ].map((filter, index) => (
                  <button
                    key={index}
                    className={`px-3 sm:px-4 py-1.5 sm:py-2 rounded-full text-xs sm:text-sm font-medium transition-all duration-300 ${
                      filter.active 
                        ? 'bg-[#ec4899] text-white shadow-lg shadow-[#ec4899]/25' 
                        : 'bg-white/10 backdrop-blur-sm text-accessible-muted hover:bg-white/20 hover:text-white border border-white/20'
                    }`}
                  >
                    {filter.label}
                  </button>
                ))}
              </div>
            </div>
          </div>
          
          {/* Modern scroll indicator - Mobile responsive */}
          <div className="mt-auto pt-6 sm:pt-8 flex justify-center animate-bounce-slow">
            <div className="flex flex-col items-center">
              <div className="w-5 sm:w-6 h-8 sm:h-10 border-2 border-white/30 rounded-full flex justify-center">
                <div className="w-0.5 sm:w-1 h-2 sm:h-3 bg-white/60 rounded-full mt-1.5 sm:mt-2 animate-pulse"></div>
              </div>
              <span className="text-accessible-subtle text-xs mt-1 sm:mt-2 hidden sm:block">Scroll to browse</span>
            </div>
          </div>
        </div>
      </section>

      <GalleryContent />
    </main>
  );
} 