'use client';

import { useEffect, useRef } from 'react';
import type { Map as LeafletMap } from 'leaflet';

const LocationMap = () => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<LeafletMap | null>(null);

  useEffect(() => {
    // Dynamic import of leaflet to avoid SSR issues
    const loadMap = async () => {
      // Only import leaflet on the client side
      const L = await import('leaflet');
      
      // Import CSS
      import('leaflet/dist/leaflet.css');
      
      // Don't initialize the map if it's already initialized
      if (mapInstanceRef.current) return;
      
      // Newcastle upon Tyne coordinates as default
      const lat = 54.9783;
      const lng = -1.6178;
      
      // Initialize the map
      if (mapRef.current) {
        const map = L.map(mapRef.current).setView([lat, lng], 13);
        
        // Add a light-styled tile layer that fits the white background theme
        L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
          attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
          subdomains: 'abcd',
          maxZoom: 19
        }).addTo(map);
        
        // Custom icon for marker
        const customIcon = L.divIcon({
          className: 'custom-div-icon',
          html: `<div style="background-color: #ec4899; width: 16px; height: 16px; border-radius: 50%; border: 2px solid white;"></div>`,
          iconSize: [30, 30],
          iconAnchor: [8, 8],
        });
        
        // Add a marker for the business location
        const marker = L.marker([lat, lng], { icon: customIcon }).addTo(map);
        
        // Add a styled popup with the business name
        marker.bindPopup(`
          <div style="font-family: 'Arial', sans-serif; text-align: center;">
            <div style="font-weight: bold; font-size: 14px; color: #1a3a6d; margin-bottom: 4px;">Window Warriors</div>
            <div style="font-size: 12px; color: #666;">Premium UPVC Windows & Doors</div>
          </div>
        `).openPopup();
        
        // Add a circle to show the service area (approximate) with more appealing style
        L.circle([lat, lng], {
          color: '#ec4899',
          weight: 1,
          fillColor: '#ec4899',
          fillOpacity: 0.1,
          radius: 15000 // 15km radius
        }).addTo(map);
        
        // Store the map instance for cleanup
        mapInstanceRef.current = map;
      }
    };
    
    loadMap();
    
    // Cleanup function
    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
    };
  }, []);
  
  return (
    <div className="w-full h-full rounded-xl overflow-hidden border border-gray-200">
      {/* Map container */}
      <div ref={mapRef} className="w-full h-full z-0">
        {/* Map will be loaded here - Light-themed loading state */}
        <div className="flex items-center justify-center h-full bg-gray-50">
          <div className="flex items-center space-x-2">
            <svg className="animate-spin h-5 w-5 text-[#ec4899]" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span className="text-accessible-gray">Loading map...</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LocationMap; 