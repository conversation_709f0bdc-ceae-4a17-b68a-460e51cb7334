'use client';

import { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';

// Dynamically import the PromotionNotification component
const PromotionNotification = dynamic(
  () => import("@/components/PromotionNotification"),
  { ssr: false }
);

// Dynamically import the ExitIntentPopup component
const ExitIntentPopup = dynamic(
  () => import("@/components/ExitIntentPopup"),
  { ssr: false }
);

// Dynamically import the CookieConsent component
const CookieConsent = dynamic(
  () => import("@/components/CookieConsent"),
  { ssr: false }
);

// Dynamically import the AnalyticsProviderWrapper component
const AnalyticsWrapper = dynamic(
  () => import("@/components/AnalyticsProviderWrapper"),
  { ssr: false }
);

export function PromotionNotificationWrapper() {
  return <PromotionNotification />;
}

export function ExitIntentPopupWrapper() {
  return <ExitIntentPopup />;
}

export function CookieConsentWrapper() {
  return <CookieConsent privacyPolicyUrl="/privacy-policy" />;
}

export function AnalyticsProviderWrapper() {
  return <AnalyticsWrapper />;
} 