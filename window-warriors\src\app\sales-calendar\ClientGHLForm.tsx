'use client';

import { useEffect } from 'react';

const ClientGHLForm = () => {
  
  useEffect(() => {
    // Load the Go High Level form embed script
    const script = document.createElement('script');
    script.src = 'https://link.msgsndr.com/js/form_embed.js';
    script.type = 'text/javascript';
    script.async = true;
    document.body.appendChild(script);
    
    return () => {
      try {
        document.body.removeChild(script);
      } catch (e) {
        console.log('Error removing script:', e);
      }
    };
  }, []);

  return (
    <div className="w-full">
      <div className="bg-blue-50 p-4 rounded-lg mb-6 border border-blue-200">
        <div className="flex items-center mb-2">
          <svg className="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h4 className="text-sm sm:text-base font-medium text-blue-800">Client Information Submission</h4>
        </div>
        <p className="text-sm sm:text-base text-blue-700">
          This form allows clients to submit their information directly to our system. Use this when the client wants to provide their details but doesn't need to book an appointment immediately.
        </p>
      </div>
      
      {/* GHL Form Container */}
      <div className="rounded-lg overflow-hidden bg-white border border-gray-200" style={{ minHeight: '662px' }}>
        <div 
          dangerouslySetInnerHTML={{ 
            __html: `
              <iframe
                src="https://api.leadconnectorhq.com/widget/form/IUz2mNgNLJgj9d4X6vGp"
                style="width:100%;height:662px;border:none;border-radius:3px"
                id="inline-client-IUz2mNgNLJgj9d4X6vGp" 
                data-layout="{'id':'INLINE'}"
                data-trigger-type="alwaysShow"
                data-trigger-value=""
                data-activation-type="alwaysActivated"
                data-activation-value=""
                data-deactivation-type="neverDeactivate"
                data-deactivation-value=""
                data-form-name="client-sales"
                data-height="662"
                data-layout-iframe-id="inline-client-IUz2mNgNLJgj9d4X6vGp"
                data-form-id="IUz2mNgNLJgj9d4X6vGp"
                title="client-sales">
              </iframe>
              <script src="https://link.msgsndr.com/js/form_embed.js"></script>
            `
          }} 
        />
      </div>
      
      <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
        <div className="flex items-start">
          <svg className="w-5 h-5 text-green-600 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div>
            <h5 className="text-sm font-medium text-green-800 mb-1">Direct to System</h5>
            <p className="text-sm text-green-700">
              When the client submits this form, their information goes directly into the GHL system. 
              You can follow up with them later to schedule an appointment or provide a quote.
            </p>
          </div>
        </div>
      </div>

      <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <div className="flex items-start">
          <svg className="w-5 h-5 text-yellow-600 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          <div>
            <h5 className="text-sm font-medium text-yellow-800 mb-1">When to Use This Form</h5>
            <ul className="text-sm text-yellow-700 list-disc list-inside space-y-1">
              <li>Client wants to provide information but not book immediately</li>
              <li>Client needs time to think about scheduling</li>
              <li>You want to capture lead information for follow-up</li>
              <li>Client prefers to be contacted later</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClientGHLForm;
