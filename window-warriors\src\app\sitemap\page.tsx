import { Metadata } from 'next';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Sitemap | Window Warriors - UPVC Windows & Doors North East',
  description: 'Navigate our site with ease using our sitemap. Find all pages and sections of Window Warriors website for UPVC windows, doors, and related services.',
  keywords: 'sitemap, website navigation, Window Warriors sitemap, UPVC windows site directory',
  openGraph: {
    title: 'Sitemap | Window Warriors',
    description: 'Navigate our site with ease using our sitemap. Find all pages and sections of Window Warriors website.',
    url: 'https://windowwarriors.uk/sitemap',
    siteName: 'Window Warriors',
    images: [
      {
        url: 'https://windowwarriors.uk/images/old-brick-house-paved-street.jpg',
        width: 1200,
        height: 630,
        alt: 'Window Warriors Sitemap',
      },
    ],
    locale: 'en_GB',
    type: 'website',
  },
  alternates: {
    canonical: 'https://windowwarriors.uk/sitemap',
  },
};

export default function SitemapPage() {
  // Main pages structure
  const mainPages = [
    { title: 'Home', url: '/', description: 'Our main landing page with an overview of our services and offerings.' },
    { title: 'About Us', url: '/about', description: 'Learn about our company, our mission, and our team.' },
    { title: 'Products & Services', url: '/products-services', description: 'Explore our range of UPVC windows, doors, and related services.' },
    { title: 'Contact Us', url: '/contact', description: 'Get in touch with our team for inquiries and quotes.' },
    { title: 'Blog', url: '/blog', description: 'Read our latest articles and tips about windows, doors, and home improvement.' },
  ];

  // Product/service pages
  const productPages = [
    { title: 'UPVC Windows', url: '/products-services/upvc-windows', description: 'Explore our UPVC window options and styles.' },
    { title: 'UPVC Doors', url: '/products-services/upvc-doors', description: 'View our range of UPVC door designs and features.' },
    { title: 'Composite Doors', url: '/products-services/composite-doors', description: 'Discover our premium composite door collection.' },
    { title: 'Conservatories', url: '/products-services/conservatories', description: 'Find information about our conservatory solutions.' },
    { title: 'Porches', url: '/products-services/porches', description: 'Learn about our porch installation services.' },
  ];

  // Legal pages
  const legalPages = [
    { title: 'Privacy Policy', url: '/privacy-policy', description: 'Read about how we handle and protect your data.' },
    { title: 'Terms of Service', url: '/terms-of-service', description: 'Understand the terms and conditions for using our services.' },
    { title: 'Sitemap', url: '/sitemap', description: 'View a directory of all pages on our website.' },
  ];

  return (
    <main className="flex min-h-screen flex-col page-with-hero">
      {/* Enhanced Hero Section */}
      <section className="relative min-h-[60vh] sm:min-h-[70vh] md:min-h-screen py-16 sm:py-24 md:py-28 lg:py-32 xl:py-36 overflow-hidden">
        {/* Multi-layered Background */}
        <div className="absolute inset-0">
          {/* Background image */}
          <div className="absolute inset-0 bg-cover bg-center" style={{
            backgroundImage: 'url("/images/old-brick-house-paved-street.jpg")',
            backgroundPosition: 'center 30%',
          }}>
            {/* Enhanced overlay with pink accents */}
            <div className="absolute inset-0 bg-gradient-to-br from-[#1a3a6d]/95 via-[#1a3a6d]/85 to-[#ec4899]/20"></div>
            <div className="absolute inset-0 bg-gradient-to-t from-[#1a3a6d]/90 via-transparent to-transparent"></div>
          </div>
          
          {/* Floating pink elements */}
          <div className="absolute top-1/4 left-1/4 w-24 sm:w-32 md:w-64 h-24 sm:h-32 md:h-64 bg-[#ec4899]/15 rounded-full blur-3xl animate-float-slow"></div>
          <div className="absolute bottom-1/4 right-1/4 w-32 sm:w-48 md:w-96 h-32 sm:h-48 md:h-96 bg-[#d946ef]/10 rounded-full blur-3xl animate-float" style={{animationDelay: '2s'}}></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 sm:w-96 h-64 sm:h-96 bg-[#ec4899]/5 rounded-full blur-3xl animate-pulse-slow"></div>
          
          {/* Glass particles - hidden on mobile */}
          <div className="hidden md:block absolute top-[15%] right-[15%] w-4 h-4 bg-white/20 rounded-full animate-float"></div>
          <div className="hidden md:block absolute top-[25%] right-[25%] w-3 h-3 bg-[#ec4899]/30 rounded-full animate-float" style={{animationDelay: '1s'}}></div>
          <div className="hidden md:block absolute top-[35%] right-[35%] w-2 h-2 bg-white/15 rounded-full animate-float" style={{animationDelay: '2s'}}></div>
          
          {/* Geometric pink accents - hidden on small screens */}
          <div className="hidden sm:block absolute top-[20%] left-[10%] w-6 sm:w-8 h-6 sm:h-8 border border-[#ec4899]/30 rotate-45 animate-spin-slow"></div>
          <div className="hidden sm:block absolute bottom-[30%] right-[10%] w-4 sm:w-6 h-4 sm:h-6 bg-[#ec4899]/20 transform rotate-45 animate-pulse-slow"></div>
          <div className="hidden lg:block absolute top-[60%] left-[20%] w-12 h-12 border-2 border-[#d946ef]/20 rounded-full animate-bounce-slow"></div>
        </div>
        
        {/* Enhanced Hero Content */}
        <div className="container mx-auto px-4 sm:px-6 relative z-10 flex flex-col justify-center min-h-[60vh] sm:min-h-[70vh] md:min-h-screen pb-8 sm:pb-16">
          <div className="max-w-4xl animate-fade-in" style={{ animationDelay: '0.3s' }}>
            {/* Enhanced brand badge */}
            <div className="inline-flex items-center mb-4 sm:mb-6 md:mb-8">
              <div className="bg-white/10 backdrop-blur-sm border border-[#ec4899]/30 rounded-full px-4 sm:px-6 py-2 animate-fade-blur" style={{ animationDelay: '0.2s' }}>
                <span className="text-accessible-light text-xs sm:text-sm md:text-base font-medium">SITEMAP</span>
                <div className="w-full h-px bg-gradient-to-r from-transparent via-[#ec4899]/70 to-transparent mt-1"></div>
              </div>
            </div>
            
            {/* Enhanced title with pink gradients */}
            <h1 className="text-3xl sm:text-4xl md:text-6xl lg:text-7xl xl:text-8xl font-bold text-white mb-3 sm:mb-4 md:mb-6 leading-tight">
              Site <span className="relative inline-block">
                <span className="text-gradient-logo">Navigation</span>
                <div className="absolute -bottom-1 sm:-bottom-2 left-0 w-full h-0.5 sm:h-1 bg-gradient-to-r from-[#ec4899] to-[#d946ef] rounded-full animate-shimmer"></div>
                <div className="absolute -top-0.5 sm:-top-1 -right-0.5 sm:-right-1 w-2 sm:w-3 h-2 sm:h-3 bg-[#ec4899]/60 rounded-full animate-pulse"></div>
              </span>
            </h1>
            <h2 className="text-2xl sm:text-3xl md:text-5xl lg:text-6xl font-bold text-white mb-3 sm:mb-4 md:mb-6">
              Directory
            </h2>
            
            {/* Enhanced description */}
            <div className="relative mb-4 sm:mb-6 md:mb-8">
              <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-accessible-light leading-relaxed animate-slide-up" style={{ animationDelay: '0.6s' }}>
                A complete directory of our website pages for easy navigation and exploration.
              </p>
              <div className="absolute -left-2 sm:-left-4 top-0 w-0.5 sm:w-1 h-full bg-gradient-to-b from-[#ec4899] to-transparent rounded-full opacity-60"></div>
            </div>
            
            {/* Enhanced buttons */}
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 md:gap-6 mb-6 sm:mb-8 md:mb-10 animate-slide-up" style={{ animationDelay: '0.9s' }}>
              <Link href="/contact" className="btn-primary text-center text-sm sm:text-base">
                Contact Us
              </Link>
              <Link href="/" className="inline-block bg-white/10 backdrop-blur-sm border-2 border-white/30 text-white hover:bg-white hover:text-[#1a3a6d] font-medium py-3 sm:py-4 px-6 sm:px-8 rounded-full transition-all duration-300 text-center group text-sm sm:text-base">
                <span className="flex items-center justify-center">
                  <svg className="w-4 h-4 sm:w-5 sm:h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                  </svg>
                  Back to Home
                </span>
              </Link>
            </div>
            
            {/* Enhanced trust badges */}
            <div className="animate-fade-in" style={{ animationDelay: '1.2s' }}>
              <p className="text-white/70 text-xs sm:text-sm md:text-base mb-3 sm:mb-4">Easy navigation</p>
              <div className="flex flex-wrap gap-2 sm:gap-3 md:gap-4">
                <div className="flex items-center gap-1.5 sm:gap-2 bg-white/10 backdrop-blur-sm border border-white/20 px-3 sm:px-4 py-1.5 sm:py-2 rounded-full">
                  <div className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 bg-[#ec4899]/20 rounded-full flex items-center justify-center">
                    <svg className="w-2.5 h-2.5 sm:w-3 sm:h-3 md:w-4 md:h-4 text-[#ec4899]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                    </svg>
                  </div>
                  <span className="text-white text-xs sm:text-sm font-medium">Organized Structure</span>
                </div>
                <div className="flex items-center gap-1.5 sm:gap-2 bg-white/10 backdrop-blur-sm border border-white/20 px-3 sm:px-4 py-1.5 sm:py-2 rounded-full">
                  <div className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 bg-[#ec4899]/20 rounded-full flex items-center justify-center">
                    <svg className="w-2.5 h-2.5 sm:w-3 sm:h-3 md:w-4 md:h-4 text-[#ec4899]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                    </svg>
                  </div>
                  <span className="text-white text-xs sm:text-sm font-medium">Quick Access</span>
                </div>
                <div className="flex items-center gap-1.5 sm:gap-2 bg-white/10 backdrop-blur-sm border border-white/20 px-3 sm:px-4 py-1.5 sm:py-2 rounded-full">
                  <div className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 bg-[#ec4899]/20 rounded-full flex items-center justify-center">
                    <svg className="w-2.5 h-2.5 sm:w-3 sm:h-3 md:w-4 md:h-4 text-[#ec4899]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                  </div>
                  <span className="text-white text-xs sm:text-sm font-medium">Complete Directory</span>
                </div>
              </div>
            </div>
          </div>
          
          {/* Modern scroll indicator */}
          <div className="mt-auto pt-6 sm:pt-8 flex justify-center animate-bounce-slow">
            <div className="flex flex-col items-center">
              <div className="w-5 sm:w-6 h-8 sm:h-10 border-2 border-white/30 rounded-full flex justify-center">
                <div className="w-0.5 sm:w-1 h-2 sm:h-3 bg-white/60 rounded-full mt-1.5 sm:mt-2 animate-pulse"></div>
              </div>
              <span className="text-accessible-subtle text-xs mt-2 hidden sm:block">Browse sitemap</span>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content Section */}
      <section className="py-8 sm:py-12 md:py-16 lg:py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 max-w-4xl">
          <div className="prose prose-sm sm:prose-base lg:prose-lg mx-auto">
            <div className="mb-6 sm:mb-8">
              <p className="text-accessible-gray mb-4 sm:mb-6 text-sm sm:text-base leading-relaxed">
                Welcome to our sitemap. This page provides a comprehensive overview of all the sections and pages available on the Window Warriors website. 
                Use the links below to navigate directly to the content you're looking for.
              </p>
            </div>

            {/* Main Pages */}
            <div className="mb-8 sm:mb-12">
              <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-4 sm:mb-6 relative inline-block">
                Main Pages
                <span className="absolute -bottom-1 sm:-bottom-2 left-0 w-16 sm:w-20 h-0.5 sm:h-1 bg-[#ec4899]/50 rounded-full"></span>
              </h2>
              
              <div className="grid gap-4 sm:gap-6 mb-6 sm:mb-8">
                {mainPages.map((page, index) => (
                  <div key={index} className="bg-gray-50 rounded-lg p-4 sm:p-5 transition-all hover:shadow-md">
                    <h3 className="text-lg sm:text-xl font-bold text-[#1a3a6d] mb-2">
                      <Link href={page.url} className="flex items-center hover:text-[#ec4899] transition-colors text-sm sm:text-base">
                        {page.title}
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                        </svg>
                      </Link>
                    </h3>
                    <p className="text-accessible-gray">{page.description}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Product Pages */}
            <div className="mb-12">
              <h2 className="text-2xl sm:text-3xl font-bold text-[#1a3a6d] mb-6 relative inline-block">
                Products & Services
                <span className="absolute -bottom-2 left-0 w-20 h-1 bg-[#ec4899]/50 rounded-full"></span>
              </h2>
              
              <div className="grid gap-6 mb-8">
                {productPages.map((page, index) => (
                  <div key={index} className="bg-gray-50 rounded-lg p-5 transition-all hover:shadow-md">
                    <h3 className="text-xl font-bold text-[#1a3a6d] mb-2">
                      <Link href={page.url} className="flex items-center hover:text-[#ec4899] transition-colors">
                        {page.title}
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                        </svg>
                      </Link>
                    </h3>
                    <p className="text-accessible-gray">{page.description}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Legal Pages */}
            <div className="mb-12">
              <h2 className="text-2xl sm:text-3xl font-bold text-[#1a3a6d] mb-6 relative inline-block">
                Legal Information
                <span className="absolute -bottom-2 left-0 w-20 h-1 bg-[#ec4899]/50 rounded-full"></span>
              </h2>
              
              <div className="grid gap-6 mb-8">
                {legalPages.map((page, index) => (
                  <div key={index} className="bg-gray-50 rounded-lg p-5 transition-all hover:shadow-md">
                    <h3 className="text-xl font-bold text-[#1a3a6d] mb-2">
                      <Link href={page.url} className="flex items-center hover:text-[#ec4899] transition-colors">
                        {page.title}
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                        </svg>
                      </Link>
                    </h3>
                    <p className="text-accessible-gray">{page.description}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-12 sm:py-16 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-2xl sm:text-3xl font-bold text-[#1a3a6d] mb-6">Can't Find What You're Looking For?</h2>
            <p className="text-accessible-gray mb-8">
              Our team is here to help you navigate our services and answer any questions you may have.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Link href="/contact" className="btn-primary text-center">
                Contact Us
              </Link>
              <Link href="/" className="inline-block border-2 border-[#ec4899] text-[#ec4899] hover:bg-[#ec4899] hover:text-white font-medium py-3 px-8 rounded-full transition-all duration-300 text-center">
                Back to Home
              </Link>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
} 
