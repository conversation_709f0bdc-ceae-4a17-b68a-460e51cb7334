'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';

const PromotionNotification = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [temporarilyHidden, setTemporarilyHidden] = useState(false);
  const [permanentlyDismissed, setPermanentlyDismissed] = useState(false);

  useEffect(() => {
    // Check if notification was permanently dismissed (for future use)
    const dismissed = localStorage.getItem('promoNotificationPermanentlyDismissed');
    if (dismissed === 'true') {
      setPermanentlyDismissed(true);
      return;
    }

    // If notification was temporarily dismissed, check when it was closed
    const hiddenTimestamp = localStorage.getItem('promoNotificationHiddenAt');
    if (hiddenTimestamp) {
      const hiddenTime = parseInt(hiddenTimestamp, 10);
      const currentTime = new Date().getTime();
      const timeDifference = currentTime - hiddenTime;
      const tenMinutesInMs = 10 * 60 * 1000; // 10 minutes instead of 2

      if (timeDifference < tenMinutesInMs) {
        // Less than 10 minutes have passed, keep it hidden
        setTemporarilyHidden(true);

        // Set a timer to show it again after the remaining time
        const remainingTime = tenMinutesInMs - timeDifference;
        const timer = setTimeout(() => {
          setTemporarilyHidden(false);
          setIsVisible(true);
          localStorage.removeItem('promoNotificationHiddenAt');
        }, remainingTime);
        
        return () => clearTimeout(timer);
      } else {
        // More than 10 minutes have passed, show it again
        localStorage.removeItem('promoNotificationHiddenAt');
      }
    }

    // Show notification after a longer delay to avoid being intrusive
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 10000); // 10 seconds delay

    return () => clearTimeout(timer);
  }, []);

  const closeNotification = (e?: React.MouseEvent) => {
    e?.preventDefault();
    e?.stopPropagation();
    console.log('Close notification clicked'); // Debug log
    setIsVisible(false);
    setTemporarilyHidden(true);
    // Store the timestamp when notification was closed
    localStorage.setItem('promoNotificationHiddenAt', new Date().getTime().toString());
  };

  // For future use - permanent dismiss functionality
  const permanentlyDismiss = () => {
    setIsVisible(false);
    setTimeout(() => {
      setPermanentlyDismissed(true);
      localStorage.setItem('promoNotificationPermanentlyDismissed', 'true');
    }, 500);
  };

  if (permanentlyDismissed || temporarilyHidden) return null;

  return (
    <div 
      className={`fixed bottom-6 sm:bottom-10 right-0 max-w-[90%] sm:max-w-[360px] z-50 transform transition-all duration-500 ease-out ${
        isVisible ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'
      }`}
    >
      <div className="glass-card-dark p-4 mx-3 rounded-xl shadow-2xl premium-card-glow overflow-hidden relative">
        {/* Background decorative elements */}
        <div className="absolute -top-10 -left-10 w-40 h-40 bg-[#ec4899]/10 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-10 -right-10 w-40 h-40 bg-[#1a3a6d]/30 rounded-full blur-3xl"></div>
        
        {/* Background pattern */}
        <div className="absolute inset-0 bg-pattern opacity-5"></div>
        
        {/* Close button */}
        <button
          onClick={(e) => closeNotification(e)}
          className="absolute top-1 right-1 w-10 h-10 flex items-center justify-center rounded-full bg-white/20 hover:bg-white/30 transition-all duration-300 z-50 cursor-pointer"
          aria-label="Close promotion"
          type="button"
        >
          <svg className="w-5 h-5 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
        
        <div className="flex gap-3 items-start relative z-10">
          {/* Promotional image */}
          <div className="relative h-20 w-20 shrink-0 overflow-hidden rounded-lg">
            <Image
              src="/images/optimized/exterior-home.webp"
              alt="Window Warriors special offer"
              fill
              className="object-cover composite-layer"
              sizes="80px"
              loading="lazy"
            />
            {/* Badge overlay */}
            <div className="absolute -top-1 -right-1 bg-[#ec4899] text-[#0b1423] w-10 h-10 rounded-full flex items-center justify-center text-xs font-bold animate-pulse-slow">
              25%
              <span className="absolute text-[8px] bottom-1.5">OFF</span>
            </div>
          </div>
          
          {/* Content */}
          <div className="flex-1">
            <h3 className="text-white font-bold text-lg">
              <span className="text-gradient-premium">Limited Time Offer</span>
            </h3>
            <p className="text-accessible-light text-sm mb-3">
              Get 25% lifetime discount on all premium window installations!
            </p>
            <Link
              href="/offer"
              className="inline-flex items-center gap-2 px-4 py-2 bg-[#ec4899] hover:bg-[#be185d] text-[#0b1423] text-sm font-medium rounded-full transition-all duration-300 btn-premium"
            >
              <span>Claim Offer</span>
              <svg className="w-4 h-4 transform transition-all duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </Link>
          </div>
        </div>
        
        {/* Bottom message */}
        <div className="mt-3 text-[10px] text-accessible-subtle text-center">
          *Terms and conditions apply. Limited time offer.
        </div>
      </div>
    </div>
  );
};

export default PromotionNotification; 
