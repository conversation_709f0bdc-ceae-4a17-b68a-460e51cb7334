'use client';

import { useEffect, useState } from 'react';
import { hasCookieConsent, isCookieTypeAccepted } from '@/utils/cookieUtils';
import { initializeAnalytics, trackPageView } from '@/utils/analytics';
import { usePathname, useSearchParams } from 'next/navigation';

export default function AnalyticsProvider() {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [initialized, setInitialized] = useState(false);

  // Effect to initialize analytics once when component mounts
  useEffect(() => {
    if (!initialized && hasCookieConsent()) {
      initializeAnalytics();
      setInitialized(true);
    }
  }, [initialized]);

  // Effect to track page views when the pathname or search params change
  useEffect(() => {
    if (initialized && isCookieTypeAccepted('analytics')) {
      // Combine pathname and search params for full URL
      const url = searchParams.size > 0 
        ? `${pathname}?${searchParams.toString()}`
        : pathname;
        
      trackPageView(url);
    }
  }, [pathname, searchParams, initialized]);

  // Re-check consent when a storage event happens (e.g., cookies changed)
  useEffect(() => {
    const handleStorageChange = () => {
      if (hasCookieConsent() && !initialized) {
        initializeAnalytics();
        setInitialized(true);
      }
    };

    window.addEventListener('storage', handleStorageChange);
    
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [initialized]);

  // This component doesn't render anything
  return null;
} 