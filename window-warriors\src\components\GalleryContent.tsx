'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';

const GalleryContent = () => {
  // Gallery categories
  const categories = [
    { id: 'all', name: 'All Projects' },
    { id: 'windows', name: 'Windows' },
    { id: 'doors', name: 'Doors' },
    { id: 'conservatories', name: 'Conservatories' },
    { id: 'repairs', name: 'Repairs' },
  ];

  // Gallery images using real client photos
  const galleryItems = [
    {
      id: 1,
      title: 'Modern UPVC Window Installation',
      description: 'Energy-efficient UPVC windows installation for a residential property in Newcastle',
      category: 'windows',
      featured: true,
      imageSrc: '/gallery/107737930_2714639882145635_5548775699563401414_n.jpg',
      width: 'lg',
    },
    {
      id: 2,
      title: 'Contemporary Front Door',
      description: 'Stylish composite door installation with modern security features',
      category: 'doors',
      featured: true,
      imageSrc: '/gallery/399964752_788247486438733_8470154972341291903_n.jpg',
      width: 'sm',
    },
    {
      id: 3,
      title: 'Victorian Property Restoration',
      description: 'Period-appropriate window installation preserving the character of this Victorian home',
      category: 'windows',
      featured: true,
      imageSrc: '/gallery/50314388_2276698679273093_2968409064003862528_n.jpg',
      width: 'md',
    },
    {
      id: 4,
      title: 'Conservatory Installation',
      description: 'Modern conservatory with energy-efficient glass roof and UPVC framework',
      category: 'conservatories',
      featured: true,
      imageSrc: '/gallery/171676045_196756592254495_2685816906816687273_n.jpg',
      width: 'lg',
    },
    {
      id: 5,
      title: 'Residential Door Installation',
      description: 'Secure and stylish entrance door for a property in Northumberland',
      category: 'doors',
      featured: false,
      imageSrc: '/gallery/118518515_2757609127848710_4068117359392121180_n.jpg',
      width: 'md',
    },
    {
      id: 6,
      title: 'Bay Window Replacement',
      description: 'Complete bay window replacement including structural repairs',
      category: 'windows',
      featured: true,
      imageSrc: '/gallery/55439581_2321778848098409_4016135747105980416_n.jpg',
      width: 'sm',
    },
    {
      id: 7,
      title: 'Security Door Installation',
      description: 'High-security composite door with multi-point locking system',
      category: 'doors',
      featured: false,
      imageSrc: '/gallery/171007892_196756585587829_6830486126757344649_n.jpg',
      width: 'lg',
    },
    {
      id: 8,
      title: 'Orangery Extension',
      description: 'Luxury orangery installation providing additional living space',
      category: 'conservatories',
      featured: true,
      imageSrc: '/gallery/28467934_2052407361702227_2690762843913008596_n.jpg',
      width: 'md',
    },
    {
      id: 9,
      title: 'Sliding Patio Door Installation',
      description: 'Smooth-sliding UPVC patio doors with enhanced security features',
      category: 'doors',
      featured: false,
      imageSrc: '/gallery/79129539_2531714537104838_1705095243348574208_n.jpg',
      width: 'sm',
    },
    {
      id: 10,
      title: 'Bifold Door Project',
      description: 'Full-width bifold doors creating seamless indoor-outdoor living space',
      category: 'doors',
      featured: true,
      imageSrc: '/gallery/58577539_2340166859592941_8734424958354063360_n.jpg',
      width: 'lg',
    },
    {
      id: 11,
      title: 'Window Repair Service',
      description: 'Expert glass replacement and mechanism repair service',
      category: 'repairs',
      featured: false,
      imageSrc: '/gallery/46883783_2241521072790854_477739159478337536_n.jpg',
      width: 'md',
    },
    {
      id: 12,
      title: 'Traditional Property Windows',
      description: 'Complete window replacement matching the style of this traditional property',
      category: 'windows',
      featured: false,
      imageSrc: '/gallery/27073164_2038136096462687_7719985470351376872_n.jpg',
      width: 'sm',
    },
    {
      id: 13,
      title: 'Modern Apartment Windows',
      description: 'Sleek, energy-efficient windows for an apartment complex in Durham',
      category: 'windows',
      featured: false,
      imageSrc: '/gallery/17634866_1954650534811244_5329284307883941384_n.jpg',
      width: 'md',
    },
    {
      id: 14,
      title: 'Rear Patio Door Installation',
      description: 'Wide-span doors opening onto garden area for a seamless transition',
      category: 'doors',
      featured: false,
      imageSrc: '/gallery/107497696_2714640365478920_313879966201061393_n.jpg',
      width: 'md',
    },
    {
      id: 15,
      title: 'Residential Property Facelift',
      description: 'Complete window and door replacement transforming this family home',
      category: 'windows',
      featured: false,
      imageSrc: '/gallery/56196668_2326392070970420_8862400157547757568_n.jpg',
      width: 'lg',
    },
    {
      id: 16,
      title: 'Garden Room Installation',
      description: 'Bespoke garden room with large glass panels for maximum natural light',
      category: 'conservatories',
      featured: false,
      imageSrc: '/gallery/18423818_1912587215684243_517468482600756912_n.jpg',
      width: 'lg',
    },
    {
      id: 17,
      title: 'Sash Window Restoration',
      description: 'Restoration of period sash windows with modern thermal efficiency',
      category: 'repairs',
      featured: false,
      imageSrc: '/gallery/29543088_2070637379879225_817616855731859847_n.jpg',
      width: 'sm',
    },
    {
      id: 18,
      title: 'French Door Installation',
      description: 'Elegant French doors providing easy access to garden area',
      category: 'doors',
      featured: false,
      imageSrc: '/gallery/107462379_2714640428812247_2430698195984618616_n.jpg',
      width: 'md',
    }
  ];

  const [filteredItems, setFilteredItems] = useState(galleryItems);
  const [activeCategory, setActiveCategory] = useState('all');
  const [visibleCount, setVisibleCount] = useState(9);

  const handleFilter = (category: string) => {
    setActiveCategory(category);
    if (category === 'all') {
      setFilteredItems(galleryItems);
    } else {
      setFilteredItems(galleryItems.filter(item => item.category === category));
    }
    setVisibleCount(9); // Reset visible count when changing categories
  };

  const handleLoadMore = () => {
    setVisibleCount(prevCount => prevCount + 6);
  };

  return (
    <>
      {/* Gallery Section - Mobile Optimized */}
      <section className="py-12 sm:py-16 md:py-20 lg:py-24 bg-white relative">
        <div className="absolute top-0 inset-x-0 h-16 sm:h-20 md:h-24 bg-[#1a3a6d] clip-diagonal-bottom"></div>
        
        {/* Decorative elements - Mobile responsive */}
        <div className="absolute top-16 sm:top-24 md:top-32 left-0 w-32 sm:w-48 md:w-64 h-32 sm:h-48 md:h-64 bg-gradient-to-r from-[#ec4899]/20 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-16 sm:bottom-24 md:bottom-32 right-0 w-32 sm:w-48 md:w-64 h-32 sm:h-48 md:h-64 bg-gradient-to-l from-[#1a3a6d]/10 to-transparent rounded-full blur-3xl"></div>
        
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 pt-8 sm:pt-12 md:pt-16">
          <div className="text-center mb-8 sm:mb-10 md:mb-12">
            <div className="spinning-border p-0.5 inline-block rounded-full mb-3 sm:mb-4 md:mb-5">
              <span className="bg-white text-[#1a3a6d] text-xs sm:text-sm font-semibold px-3 sm:px-4 py-1 sm:py-1.5 rounded-full inline-block">
                Our Showcase
              </span>
            </div>
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-[#1a3a6d] mb-3 sm:mb-4">Quality <span className="text-gradient-accent">Craftsmanship</span></h2>
            <p className="text-accessible-light text-sm sm:text-base md:text-lg text-gray-700 max-w-2xl mx-auto mb-6 sm:mb-8">
              Browse through our portfolio of completed projects showcasing our attention to detail and premium installation quality
            </p>
            
            {/* Filter Tabs - Mobile responsive */}
            <div className="flex flex-wrap justify-center gap-2 sm:gap-3 mb-8 sm:mb-10 md:mb-12">
              {categories.map((category) => (
                <button
                  key={category.id}
                  className={`px-4 sm:px-6 py-1.5 sm:py-2 rounded-full font-medium text-xs sm:text-sm transition-all duration-300 ${
                    category.id === activeCategory 
                      ? 'bg-[#ec4899] text-white shadow-lg shadow-[#ec4899]/25' 
                      : 'bg-gray-100 text-gray-700 hover:bg-[#ec4899]/10 hover:text-[#ec4899]'
                  }`}
                  onClick={() => handleFilter(category.id)}
                >
                  {category.name}
                </button>
              ))}
            </div>
          </div>

          {/* Gallery Grid - Mobile Optimized */}
          <div className="gallery-grid grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-8 sm:mb-10 md:mb-12">
            {filteredItems.slice(0, visibleCount).map((item) => (
              <div 
                key={item.id}
                className={`gallery-item relative group overflow-hidden rounded-lg sm:rounded-xl shadow-lg optimized-transition hover:shadow-xl hover:-translate-y-1 ${
                  item.width === 'lg' ? 'sm:col-span-2' : 
                  item.width === 'md' ? 'row-span-1' : ''
                }`}
              >
                <div className={`relative image-container ${
                  item.width === 'lg' ? 'h-[250px] sm:h-[300px] md:h-[400px]' : 
                  item.width === 'md' ? 'h-[200px] sm:h-[250px] md:h-[350px]' : 'h-[180px] sm:h-[200px] md:h-[300px]'
                }`} style={{ aspectRatio: item.width === 'lg' ? '2/1' : '4/3' }}>
                  <Image 
                    src={item.imageSrc}
                    alt={item.title}
                    fill
                    className="object-cover composite-layer hover-scale"
                    loading="lazy"
                    sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
                  />
                  
                  {/* Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-[#1a3a6d] via-[#1a3a6d]/50 to-transparent opacity-0 group-hover:opacity-90 optimized-transition"></div>
                  
                  {/* Content overlay - Mobile responsive */}
                  <div className="absolute inset-0 flex flex-col justify-end p-3 sm:p-4 md:p-6 opacity-0 group-hover:opacity-100 optimized-transition">
                    <div className="transform translate-y-4 group-hover:translate-y-0 optimized-transition">
                      <h3 className="text-white font-bold text-sm sm:text-base md:text-lg mb-1 sm:mb-2">
                        {item.title}
                      </h3>
                      <p className="text-white/90 text-xs sm:text-sm">
                        {item.description}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          {/* Load more button - Mobile responsive */}
          <div className="text-center">
            {filteredItems.length > visibleCount && (
              <button className="btn-primary inline-flex items-center gap-2 text-sm sm:text-base" onClick={handleLoadMore}>
                <span>Load More Projects</span>
                <svg className="w-4 sm:w-5 h-4 sm:h-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M19 9L12 16L5 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>
            )}
          </div>
        </div>
      </section>

      {/* Customer Stories Section - Mobile Optimized */}
      <section className="py-12 sm:py-16 md:py-20 lg:py-24 bg-gray-50 relative overflow-hidden">
        {/* Decorative background elements - Mobile responsive */}
        <div className="absolute top-0 right-0 w-48 sm:w-64 md:w-80 lg:w-96 h-48 sm:h-64 md:h-80 lg:h-96 bg-gradient-to-bl from-[#ec4899]/5 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-48 sm:w-64 md:w-80 lg:w-96 h-48 sm:h-64 md:h-80 lg:h-96 bg-gradient-to-tr from-[#1a3a6d]/5 to-transparent rounded-full blur-3xl"></div>
        
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center mb-8 sm:mb-10 md:mb-12">
            <div className="inline-block mb-3 sm:mb-4">
              <div className="relative inline-block">
                <span className="absolute inset-x-0 bottom-0 h-1.5 sm:h-2 bg-gradient-to-r from-[#ec4899]/30 to-[#ec4899]/80 transform -rotate-1"></span>
                <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-[#1a3a6d] mb-2 relative z-10">What Our <span className="text-gradient-accent">Customers</span> Say</h2>
              </div>
            </div>
            <p className="text-accessible-light text-sm sm:text-base md:text-lg text-gray-700 max-w-2xl mx-auto">
              Real feedback from homeowners across the North East who have transformed their homes with Window Warriors
            </p>
          </div>
          
          {/* Featured testimonials - Mobile optimized */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 sm:gap-8 mb-8 sm:mb-10 md:mb-12">
            {[
              {
                quote: "Window Warriors transformed our Victorian property with stunningly authentic sash windows that retain the period charm while providing modern energy efficiency. The craftsmanship is exceptional.",
                name: "James W.",
                location: "Durham",
                image: "/images/ancient-window-old-building-quebec-city.jpg"
              },
              {
                quote: "From the initial consultation to the final installation, the team was professional, tidy and courteous. Our new bifold doors have completely transformed our living space and how we use our garden.",
                name: "Emma T.",
                location: "Newcastle upon Tyne",
                image: "/images/beautiful-view-blue-lake-captured-from-inside-villa.jpg"
              }
            ].map((testimonial, index) => (
              <div key={index} className="glass-card rounded-lg sm:rounded-xl overflow-hidden transition-all duration-500 hover:shadow-xl group hover-lift">
                <div className="flex flex-col md:flex-row">
                  <div className="relative w-full md:w-1/3 h-48 sm:h-56 md:h-auto">
                    <Image 
                      src={testimonial.image}
                      alt={testimonial.name}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div className="p-4 sm:p-6 md:p-8 md:w-2/3 flex flex-col justify-between">
                    <div>
                      <svg className="w-8 sm:w-10 h-8 sm:h-10 text-[#ec4899]/30 mb-3 sm:mb-4" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                        <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
                      </svg>
                      <p className="text-gray-700 text-sm sm:text-base md:text-lg leading-relaxed mb-3 sm:mb-4">{testimonial.quote}</p>
                    </div>
                    <div className="flex items-center mt-3 sm:mt-4">
                      <div className="w-0.5 sm:w-1 h-8 sm:h-10 mr-3 sm:mr-4 bg-[#ec4899]"></div>
                      <div>
                        <h4 className="font-bold text-[#1a3a6d] text-sm sm:text-base">{testimonial.name}</h4>
                        <p className="text-accessible-light text-xs sm:text-sm">{testimonial.location}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          <div className="text-center">
            <Link href="/testimonials" className="bg-transparent border-2 border-[#ec4899] text-[#ec4899] hover:bg-[#ec4899] hover:text-white font-medium rounded-full transition-all duration-300 text-center text-sm sm:text-base px-6 sm:px-8 py-3 sm:py-4 inline-flex items-center gap-2">
              <span>Read More Customer Stories</span>
              <svg className="w-4 sm:w-5 h-4 sm:h-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </Link>
          </div>
        </div>
      </section>

      {/* CTA Section - Mobile Optimized */}
      <section className="py-12 sm:py-16 md:py-20 lg:py-24 bg-white relative overflow-hidden">
        {/* Decorative elements - Mobile responsive */}
        <div className="absolute top-0 left-0 w-full h-full pointer-events-none">
          <div className="absolute top-0 right-0 w-48 sm:w-64 md:w-80 lg:w-96 h-48 sm:h-64 md:h-80 lg:h-96 bg-[#ec4899]/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 left-0 w-32 sm:w-48 md:w-64 h-32 sm:h-48 md:h-64 bg-[#ec4899]/5 rounded-full blur-3xl"></div>
          
          {/* Floating particles - Mobile responsive */}
          <div className="hidden sm:block absolute top-1/4 left-1/3 w-6 sm:w-8 h-6 sm:h-8 rounded-full bg-[#ec4899]/10"></div>
          <div className="hidden sm:block absolute top-1/2 right-1/4 w-8 sm:w-12 h-8 sm:h-12 rounded-full bg-[#ec4899]/5"></div>
          <div className="hidden sm:block absolute bottom-1/3 left-1/4 w-6 sm:w-10 h-6 sm:h-10 rounded-full bg-[#ec4899]/10"></div>
        </div>
        
        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <span className="inline-block text-[#ec4899] font-medium mb-3 sm:mb-4 text-xs sm:text-sm md:text-base">GET IN TOUCH TODAY</span>
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-[#1a3a6d] mb-3 sm:mb-4 md:mb-6">Ready to Transform Your Home?</h2>
            <p className="text-accessible-light text-sm sm:text-base md:text-lg lg:text-xl text-gray-600 mb-6 sm:mb-8 md:mb-10 lg:mb-12 max-w-2xl mx-auto">
              Contact us today for a free, no-obligation quote on our premium UPVC windows, doors or conservatories for your North East home.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-3 sm:gap-4 md:gap-6">
              <Link href="/products-services#quote" className="btn-primary text-center text-sm sm:text-base px-6 sm:px-8 py-3 sm:py-4">
                Get Your Free Quote
              </Link>
              <Link href="/about" className="inline-block bg-transparent border-2 border-[#ec4899] text-[#ec4899] hover:bg-[#ec4899] hover:text-white font-medium rounded-full transition-all duration-300 text-center text-sm sm:text-base px-6 sm:px-8 py-3 sm:py-4">
                Learn More About Us
              </Link>
            </div>
            
            {/* Trust badges - Mobile optimized */}
            <div className="mt-8 sm:mt-10 md:mt-12 lg:mt-16 grid grid-cols-2 md:grid-cols-4 gap-3 sm:gap-4 md:gap-6">
              <div className="bg-gray-50 border border-gray-200 p-3 sm:p-4 rounded-lg flex flex-col items-center hover:border-[#ec4899]/30 transition-colors duration-300">
                <svg className="w-6 sm:w-8 md:w-10 h-6 sm:h-8 md:h-10 text-[#ec4899] mb-1 sm:mb-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 12L11 14L15 10M20.618 5.984C20.846 6.887 20.846 7.897 20.846 9C20.846 14.5 17.346 18 11.846 18C6.346 18 2.846 14.5 2.846 9C2.846 3.5 6.346 0 11.846 0C12.949 0 13.959 0 14.862 0.228" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span className="text-xs sm:text-sm font-medium text-center text-gray-700">Free No-Obligation Quotes</span>
              </div>
              <div className="bg-gray-50 border border-gray-200 p-3 sm:p-4 rounded-lg flex flex-col items-center hover:border-[#ec4899]/30 transition-colors duration-300">
                <svg className="w-6 sm:w-8 md:w-10 h-6 sm:h-8 md:h-10 text-[#ec4899] mb-1 sm:mb-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M19.4 15C19.1277 15.8031 19.2292 16.6718 19.6727 17.4019C20.1162 18.132 20.8622 18.6376 21.7 18.8C20.5 21.4 18.5 22 16 22C12 22 11 20 7.00001 20C5.50001 20 4.00001 20.5 3.00001 22L2.50001 18.5C2.20001 16.5 3.40001 14 6.00001 14C8.00001 14 9.00001 16 12 16C14 16 14.5 15 19.4 15Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M12 7C9 7 7.00001 5 7.00001 3C7.00001 1.5 8.00001 1 8.00001 1C8.00001 1 9.50001 3 12 3C14.5 3 16 1 16 1C16 1 17 1.5 17 3C17 5 15 7 12 7Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span className="text-xs sm:text-sm font-medium text-center text-gray-700">Energy Efficiency Ratings</span>
              </div>
              <div className="bg-gray-50 border border-gray-200 p-3 sm:p-4 rounded-lg flex flex-col items-center hover:border-[#ec4899]/30 transition-colors duration-300">
                <svg className="w-6 sm:w-8 md:w-10 h-6 sm:h-8 md:h-10 text-[#ec4899] mb-1 sm:mb-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span className="text-xs sm:text-sm font-medium text-center text-gray-700">10 Year Guarantee</span>
              </div>
              <div className="bg-gray-50 border border-gray-200 p-3 sm:p-4 rounded-lg flex flex-col items-center hover:border-[#ec4899]/30 transition-colors duration-300">
                <svg className="w-6 sm:w-8 md:w-10 h-6 sm:h-8 md:h-10 text-[#ec4899] mb-1 sm:mb-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 12H15M9 16H15M17 21H7C5.89543 21 5 20.1046 5 19V5C5 3.89543 5.89543 3 7 3H12.5858C12.851 3 13.1054 3.10536 13.2929 3.29289L18.7071 8.70711C18.8946 8.89464 19 9.149 19 9.41421V19C19 20.1046 18.1046 21 17 21Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span className="text-xs sm:text-sm font-medium text-center text-gray-700">ASSURE Approved</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default GalleryContent; 