import Image from "next/image";
import Link from "next/link";

export const metadata = {
  title: "UPVC vs. Aluminum Windows: A Comprehensive Comparison | Window Warriors",
  description: "Compare the benefits and drawbacks of UPVC and aluminum window frames for your home. Expert guidance from Newcastle's trusted window specialists.",
  keywords: "upvc vs aluminum windows, window frame comparison, best window frames, energy efficient windows, Newcastle windows, Window Warriors",
  icons: {
    icon: '/window-warriors-logo.png',
    apple: '/window-warriors-logo.png',
  },
  openGraph: {
    title: "UPVC vs. Aluminum Windows: A Comprehensive Comparison | Window Warriors",
    description: "Compare the benefits and drawbacks of UPVC and aluminum window frames for your home.",
    url: 'https://windowwarriors.uk/blog/posts/upvc-vs-aluminum-windows-comparison',
    siteName: 'Window Warriors',
    images: [
      {
        url: 'https://windowwarriors.uk/images/beautiful-view-blue-lake-captured-from-inside-villa.jpg',
        width: 1200,
        height: 630,
        alt: 'UPVC vs Aluminum Windows Comparison - Window Warriors',
      },
    ],
    locale: 'en_GB',
    type: 'article',
  },
  twitter: {
    card: 'summary_large_image',
    title: "UPVC vs. Aluminum Windows: A Comprehensive Comparison | Window Warriors",
    description: "Compare the benefits and drawbacks of UPVC and aluminum window frames for your home.",
    images: ['https://windowwarriors.uk/images/beautiful-view-blue-lake-captured-from-inside-villa.jpg'],
  },
  alternates: {
    canonical: 'https://windowwarriors.uk/blog/posts/upvc-vs-aluminum-windows-comparison',
  },
};

export default function UpvcVsAluminumWindowsPage() {
  return (
    <main className="flex min-h-screen flex-col bg-white">
      {/* Hero Section - Following Main Website Pattern */}
      <section className="relative min-h-screen overflow-hidden bg-gradient-to-br from-[#1a3a6d] via-[#1a3a6d] to-[#ec4899]/20 pt-24 sm:pt-28 md:pt-32 lg:pt-36">
        {/* Background Image */}
        <div className="absolute inset-0 bg-cover bg-center" style={{
          backgroundImage: 'url("/images/beautiful-view-blue-lake-captured-from-inside-villa.jpg")',
          backgroundAttachment: 'scroll',
          backgroundPosition: 'center 30%',
        }}></div>
        
        {/* Multi-layered Background Overlays */}
        <div className="absolute inset-0">
          {/* Primary gradient background */}
          <div className="absolute inset-0 bg-gradient-to-br from-[#1a3a6d]/95 via-[#1a3a6d]/90 to-[#ec4899]/70"></div>
          
          {/* Animated gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-[#ec4899]/10 to-transparent animate-gradient-x"></div>
          
          {/* Noise texture */}
          <div className="absolute inset-0 opacity-[0.02] bg-noise"></div>
        </div>

        {/* Floating Pink Elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {/* Large floating blurs */}
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-[#ec4899]/20 rounded-full blur-3xl animate-float-slow"></div>
          <div className="absolute top-1/3 right-1/3 w-48 h-48 bg-[#d946ef]/15 rounded-full blur-2xl animate-float-reverse"></div>
          <div className="absolute bottom-1/4 left-1/3 w-56 h-56 bg-[#f9a8d4]/10 rounded-full blur-3xl animate-float"></div>
          
          {/* Glass particles */}
          <div className="absolute top-[20%] right-[20%] w-8 h-8 bg-white/10 rounded-full backdrop-blur-sm animate-float-slow border border-white/20"></div>
          <div className="absolute top-[60%] left-[15%] w-6 h-6 bg-[#ec4899]/20 rounded-full backdrop-blur-sm animate-float border border-[#ec4899]/30"></div>
          <div className="absolute top-[40%] right-[10%] w-4 h-4 bg-[#d946ef]/30 rounded-full animate-pulse"></div>
          <div className="absolute bottom-[40%] left-[20%] w-3 h-3 bg-white/30 rounded-full animate-ping"></div>
          
          {/* Geometric accents */}
          <div className="absolute top-[25%] left-[60%] w-12 h-12 border border-[#ec4899]/30 rotate-45 animate-spin-slow"></div>
          <div className="absolute bottom-[35%] right-[25%] w-8 h-8 border-2 border-white/20 rounded-full animate-pulse"></div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 sm:px-6 relative z-20 min-h-[60vh] flex flex-col justify-center py-16 sm:py-20">
          <div className="max-w-4xl">
            {/* Brand Tag */}
            <div className="inline-flex items-center mb-6 px-4 py-2 rounded-full border border-[#ec4899]/30 bg-white/5 backdrop-blur-sm animate-fade-in">
              <div className="w-2 h-2 bg-[#ec4899] rounded-full mr-3 animate-pulse"></div>
              <span className="text-accessible-light text-sm font-medium tracking-wider">WINDOWS • FEBRUARY 15, 2024</span>
            </div>

            {/* Main Heading */}
            <h1 className="text-4xl sm:text-6xl md:text-7xl lg:text-8xl font-bold mb-6 animate-slide-up">
              <span className="text-white block mb-2">UPVC vs Aluminum</span>
              <span className="text-gradient-logo relative inline-block">
                Windows Comparison
                <div className="absolute -bottom-2 left-0 w-full h-1 bg-gradient-to-r from-[#ec4899] to-[#d946ef] rounded-full animate-shimmer"></div>
              </span>
            </h1>

            {/* Description */}
            <p className="text-lg sm:text-xl lg:text-2xl text-accessible-muted mb-8 max-w-2xl leading-relaxed animate-fade-in" style={{ animationDelay: '0.3s' }}>
              Compare UPVC and aluminum windows to make the best choice for your home's style, performance, and budget
            </p>

            {/* Author Info */}
            <div className="flex items-center space-x-4 text-accessible-light mb-8 animate-fade-in" style={{ animationDelay: '0.6s' }}>
              <div className="flex items-center">
                <Image 
                  src="/window-warriors-logo.png" 
                  alt="Window Warriors Author" 
                  width={40} 
                  height={40}
                  className="rounded-full border-2 border-white/30 shadow-sm"
                />
                <span className="ml-2.5 font-medium">Window Warriors Team</span>
              </div>
              <span className="text-accessible-subtle">•</span>
              <span className="flex items-center">
                <svg className="w-4 h-4 mr-1.5 text-[#ec4899]" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd"></path>
                </svg>
                8 min read
              </span>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-30 animate-bounce">
          <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-[#ec4899] rounded-full mt-2 animate-pulse"></div>
          </div>
          <p className="text-accessible-subtle text-xs mt-2 text-center">Scroll</p>
        </div>
      </section>

      {/* Article Content */}
      <section className="py-12 md:py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl mx-auto">
            {/* Table of Contents */}
            <div className="bg-gray-50 rounded-xl p-6 mb-12 border border-gray-100 shadow-sm hover:shadow-md transition-shadow duration-300">
              <h2 className="text-lg font-semibold text-[#1a3a6d] mb-4 flex items-center">
                <svg className="w-5 h-5 mr-2 text-[#ec4899]" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z"></path>
                </svg>
                In this article
              </h2>
              <ul className="space-y-3">
                <li>
                  <a href="#introduction" className="text-[#1a3a6d] hover:text-[#ec4899] transition-colors flex items-center group">
                    <span className="w-6 h-6 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-3 group-hover:bg-[#ec4899]/20 transition-colors">1</span>
                    <span className="font-medium">Introduction</span>
                  </a>
                </li>
                <li>
                  <a href="#upvc-windows" className="text-[#1a3a6d] hover:text-[#ec4899] transition-colors flex items-center group">
                    <span className="w-6 h-6 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-3 group-hover:bg-[#ec4899]/20 transition-colors">2</span>
                    <span className="font-medium">UPVC Windows: Pros & Cons</span>
                  </a>
                </li>
                <li>
                  <a href="#aluminum-windows" className="text-[#1a3a6d] hover:text-[#ec4899] transition-colors flex items-center group">
                    <span className="w-6 h-6 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-3 group-hover:bg-[#ec4899]/20 transition-colors">3</span>
                    <span className="font-medium">Aluminum Windows: Pros & Cons</span>
                  </a>
                </li>
                <li>
                  <a href="#comparison" className="text-[#1a3a6d] hover:text-[#ec4899] transition-colors flex items-center group">
                    <span className="w-6 h-6 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-3 group-hover:bg-[#ec4899]/20 transition-colors">4</span>
                    <span className="font-medium">Head-to-Head Comparison</span>
                  </a>
                </li>
                <li>
                  <a href="#conclusion" className="text-[#1a3a6d] hover:text-[#ec4899] transition-colors flex items-center group">
                    <span className="w-6 h-6 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-3 group-hover:bg-[#ec4899]/20 transition-colors">5</span>
                    <span className="font-medium">Conclusion & Recommendations</span>
                  </a>
                </li>
              </ul>
            </div>
            
            {/* Introduction */}
            <div id="introduction" className="mb-16 scroll-mt-24">
              <h2 className="text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-6 pb-2 border-b border-gray-200 flex items-center">
                <span className="w-8 h-8 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-3 text-sm">1</span>
                Introduction
              </h2>
              <div className="prose prose-lg max-w-none">
                <p className="text-xl md:text-2xl text-[#1a3a6d] font-medium mb-5 leading-relaxed">
                  Choosing the right window frame material is one of the most important decisions you'll make when upgrading your home's windows.
                </p>
                
                <p className="text-accessible-gray mb-6 leading-relaxed">
                  UPVC and aluminum stand as the two most popular choices for modern window frames, each offering distinct advantages and potential drawbacks. At Window Warriors, we've installed thousands of both types across Newcastle and the North East, giving us unique insight into how they perform in our regional climate and which might be best suited for different property types and homeowner priorities.
                </p>
                
                <div className="bg-blue-50 border-l-4 border-[#1a3a6d] p-5 my-8 rounded-r-xl">
                  <h3 className="text-[#1a3a6d] font-semibold text-lg mb-2">Why Material Choice Matters</h3>
                  <p className="text-accessible-gray">Your window frame material affects everything from energy efficiency and maintenance requirements to aesthetics and longevity. Making the right choice can significantly impact your home's comfort, appearance, and running costs for decades to come.</p>
                </div>
              </div>
              
              <figure className="my-10 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300">
                <div className="relative h-[300px] md:h-[400px] overflow-hidden">
                  <Image
                    src="/images/beautiful-view-blue-lake-captured-from-inside-villa.jpg"
                    alt="Modern windows with different frame materials"
                    fill
                    sizes="(max-width: 768px) 100vw, 800px"
                    className="object-cover w-full h-full transition-transform duration-700 hover:scale-105"
                  />
                </div>
                <figcaption className="bg-gray-50 text-accessible-gray text-sm p-4 italic border-t border-gray-100">
                  Both UPVC and aluminum windows can create stunning views while offering different benefits
                </figcaption>
              </figure>
            </div>

            {/* UPVC Windows Section */}
            <div id="upvc-windows" className="mb-16 scroll-mt-24">
              <h2 className="text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-6 pb-2 border-b border-gray-200 flex items-center">
                <span className="w-8 h-8 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-3 text-sm">2</span>
                UPVC Windows: Pros & Cons
              </h2>
              <div className="prose prose-lg max-w-none">
                <p className="text-xl md:text-2xl text-[#1a3a6d] font-medium mb-5 leading-relaxed">
                  UPVC (Unplasticized Polyvinyl Chloride) windows have become the most popular choice for residential installations across the UK, accounting for over 80% of new window fittings.
                </p>
                
                <p className="text-accessible-gray mb-6 leading-relaxed">
                  This popularity isn't without reason. UPVC offers a compelling package of benefits that make it particularly well-suited to the variable British climate and the practical needs of modern homeowners. Let's examine these advantages in detail, along with some potential limitations to consider.
                </p>
              </div>
              
              <div className="bg-gray-50 rounded-xl p-6 border border-gray-100 my-8">
                <h3 className="text-xl font-bold text-[#1a3a6d] mb-4">Advantages of UPVC Windows</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-12 h-12 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-4">
                      <svg className="w-6 h-6 text-[#1a3a6d]" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-bold text-[#1a3a6d] mb-2">Excellent Thermal Efficiency</h4>
                      <p className="text-accessible-gray">
                        UPVC frames contain multiple internal chambers that create natural insulation barriers. Combined with double or triple glazing, they can reduce heat loss by up to 40% compared to older window systems, potentially saving hundreds of pounds annually on energy bills.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-12 h-12 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-4">
                      <svg className="w-6 h-6 text-[#1a3a6d]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-bold text-[#1a3a6d] mb-2">Cost-Effective</h4>
                      <p className="text-accessible-gray">
                        UPVC windows typically cost 30-50% less than equivalent aluminum options, making them the budget-friendly choice for many homeowners. Their excellent durability and low maintenance requirements also mean lower lifetime costs.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-12 h-12 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-4">
                      <svg className="w-6 h-6 text-[#1a3a6d]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-bold text-[#1a3a6d] mb-2">Low Maintenance</h4>
                      <p className="text-accessible-gray">
                        UPVC won't rot, warp, or corrode, and requires nothing more than occasional cleaning with soapy water. The color is integrated throughout the material rather than surface-applied, so it won't peel, flake, or require repainting.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-12 h-12 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-4">
                      <svg className="w-6 h-6 text-[#1a3a6d]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192l-3.536 3.536M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-bold text-[#1a3a6d] mb-2">Weather Resistant</h4>
                      <p className="text-accessible-gray">
                        Particularly well-suited to the British climate, UPVC withstands persistent rain, coastal salt air, and temperature fluctuations without degrading. This makes it an especially good choice for properties in Newcastle and other North East coastal areas.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="prose prose-lg max-w-none my-8">
                <p className="text-accessible-gray leading-relaxed">
                  While UPVC windows offer a compelling range of benefits, they aren't without limitations. It's important to consider these potential drawbacks when making your decision:
                </p>
              </div>
              
              <div className="bg-gray-50 rounded-xl p-6 border border-gray-100 my-8">
                <h3 className="text-xl font-bold text-[#1a3a6d] mb-4">Limitations of UPVC Windows</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-12 h-12 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-4">
                      <svg className="w-6 h-6 text-[#1a3a6d]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-bold text-[#1a3a6d] mb-2">Aesthetic Limitations</h4>
                      <p className="text-accessible-gray">
                        UPVC frames are typically bulkier than aluminum alternatives, which can be an issue for those seeking a sleek, minimal look. While color options have expanded greatly in recent years, they still don't offer the extensive range of finishes available with aluminum.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-12 h-12 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-4">
                      <svg className="w-6 h-6 text-[#1a3a6d]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-bold text-[#1a3a6d] mb-2">Historical Properties</h4>
                      <p className="text-accessible-gray">
                        For period properties, particularly those in conservation areas, UPVC may not be permitted or may not provide the authentic appearance required to maintain the character of the building.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-12 h-12 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-4">
                      <svg className="w-6 h-6 text-[#1a3a6d]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-bold text-[#1a3a6d] mb-2">Structural Limitations</h4>
                      <p className="text-accessible-gray">
                        For very large openings or specialized designs, UPVC frames may not provide the structural strength required. In these cases, aluminum or a composite solution might be necessary.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-12 h-12 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-4">
                      <svg className="w-6 h-6 text-[#1a3a6d]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 11.5V14m0-2.5v-6a1.5 1.5 0 113 0m-3 6a1.5 1.5 0 00-3 0v2a7.5 7.5 0 0015 0v-5a1.5 1.5 0 00-3 0m-6-3V11m0-5.5v-1a1.5 1.5 0 013 0v1m0 0V11m0-5.5a1.5 1.5 0 013 0v3m0 0V11" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-bold text-[#1a3a6d] mb-2">Environmental Considerations</h4>
                      <p className="text-accessible-gray">
                        While modern UPVC is recyclable, older UPVC products can pose disposal challenges. However, manufacturers have made significant strides in developing more environmentally friendly UPVC formulations in recent years.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              
              <figure className="my-10 rounded-xl overflow-hidden shadow-md">
                <div className="relative h-[300px] md:h-[400px] overflow-hidden">
                  <Image
                    src="/images/beautiful-view-blue-lake-captured-from-inside-villa.jpg"
                    alt="Modern UPVC windows in a contemporary home"
                    fill
                    sizes="(max-width: 768px) 100vw, 800px"
                    className="object-cover w-full h-full transition-transform duration-700 hover:scale-105"
                  />
                </div>
                <figcaption className="bg-gray-50 text-accessible-gray text-sm p-4 italic border-t border-gray-100">
                  Modern UPVC windows offer excellent thermal efficiency and come in a variety of styles and colors
                </figcaption>
              </figure>
            </div>

            {/* Aluminum Windows Section */}
            <div id="aluminum-windows" className="mb-16 scroll-mt-24">
              <h2 className="text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-6 pb-2 border-b border-gray-200 flex items-center">
                <span className="w-8 h-8 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-3 text-sm">3</span>
                Aluminum Windows: Pros & Cons
              </h2>
              <div className="prose prose-lg max-w-none">
                <p className="text-xl md:text-2xl text-[#1a3a6d] font-medium mb-5 leading-relaxed">
                  Aluminum windows have been a popular choice for window frames for many years. They offer a range of benefits, including durability, strength, and a sleek appearance.
                </p>
                
                <p className="text-accessible-gray mb-6 leading-relaxed">
                  Let's explore the advantages and disadvantages of aluminum windows in more detail.
                </p>
              </div>
              
              <div className="bg-gray-50 rounded-xl p-6 border border-gray-100 my-8">
                <h3 className="text-xl font-bold text-[#1a3a6d] mb-4">Advantages of Aluminum Windows</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-12 h-12 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-4">
                      <svg className="w-6 h-6 text-[#1a3a6d]" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-bold text-[#1a3a6d] mb-2">Excellent Thermal Efficiency</h4>
                      <p className="text-accessible-gray">
                        Aluminum windows are known for their excellent thermal efficiency. They can help regulate temperature in your home, reducing the need for heating and cooling.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-12 h-12 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-4">
                      <svg className="w-6 h-6 text-[#1a3a6d]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-bold text-[#1a3a6d] mb-2">Cost-Effective</h4>
                      <p className="text-accessible-gray">
                        Aluminum windows are generally more cost-effective than UPVC or timber windows. They are also lightweight, which can reduce installation costs.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-12 h-12 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-4">
                      <svg className="w-6 h-6 text-[#1a3a6d]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-bold text-[#1a3a6d] mb-2">Low Maintenance</h4>
                      <p className="text-accessible-gray">
                        Aluminum windows require minimal maintenance. They don't rot, warp, or corrode, and can be cleaned with a simple cloth.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-12 h-12 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-4">
                      <svg className="w-6 h-6 text-[#1a3a6d]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192l-3.536 3.536M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-bold text-[#1a3a6d] mb-2">Weather Resistant</h4>
                      <p className="text-accessible-gray">
                        Aluminum windows are highly weather-resistant. They can withstand harsh weather conditions, including rain, coastal salt air, and temperature fluctuations.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="prose prose-lg max-w-none my-8">
                <p className="text-accessible-gray leading-relaxed">
                  While aluminum windows offer a range of benefits, they also have some potential drawbacks to consider:
                </p>
              </div>
              
              <div className="bg-gray-50 rounded-xl p-6 border border-gray-100 my-8">
                <h3 className="text-xl font-bold text-[#1a3a6d] mb-4">Disadvantages of Aluminum Windows</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-12 h-12 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-4">
                      <svg className="w-6 h-6 text-[#1a3a6d]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-bold text-[#1a3a6d] mb-2">Cost</h4>
                      <p className="text-accessible-gray">
                        Aluminum windows can be more expensive than UPVC or timber windows. The initial cost is higher, but they can last a long time with proper maintenance.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-12 h-12 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-4">
                      <svg className="w-6 h-6 text-[#1a3a6d]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-bold text-[#1a3a6d] mb-2">Weight</h4>
                      <p className="text-accessible-gray">
                        Aluminum windows are heavier than UPVC or timber windows. This can make them more difficult to install and may require additional support.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-12 h-12 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-4">
                      <svg className="w-6 h-6 text-[#1a3a6d]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-bold text-[#1a3a6d] mb-2">Aesthetic Limitations</h4>
                      <p className="text-accessible-gray">
                        Aluminum windows can sometimes look less attractive than other window materials. They may not be suitable for period properties or those seeking a traditional appearance.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-12 h-12 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-4">
                      <svg className="w-6 h-6 text-[#1a3a6d]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 11.5V14m0-2.5v-6a1.5 1.5 0 113 0m-3 6a1.5 1.5 0 00-3 0v2a7.5 7.5 0 0015 0v-5a1.5 1.5 0 00-3 0m-6-3V11m0-5.5v-1a1.5 1.5 0 013 0v1m0 0V11m0-5.5a1.5 1.5 0 013 0v3m0 0V11" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-bold text-[#1a3a6d] mb-2">Environmental Considerations</h4>
                      <p className="text-accessible-gray">
                        Aluminum windows are recyclable, but the process can be more complex than for UPVC or timber windows.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              
              <figure className="my-10 rounded-xl overflow-hidden shadow-md">
                <div className="relative h-[300px] md:h-[400px] overflow-hidden">
                  <Image
                    src="/images/exterior-home.jpg"
                    alt="Aluminum windows in a modern home"
                    fill
                    sizes="(max-width: 768px) 100vw, 800px"
                    className="object-cover w-full h-full transition-transform duration-700 hover:scale-105"
                  />
                </div>
                <figcaption className="bg-gray-50 text-accessible-gray text-sm p-4 italic border-t border-gray-100">
                  Aluminum windows can create a modern and sleek appearance
                </figcaption>
              </figure>
            </div>

            {/* Comparison Section */}
            <div id="comparison" className="mb-16 scroll-mt-24">
              <h2 className="text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-6 pb-2 border-b border-gray-200 flex items-center">
                <span className="w-8 h-8 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-3 text-sm">4</span>
                Head-to-Head Comparison
              </h2>
              
              <div className="prose prose-lg max-w-none">
                <p className="text-xl md:text-2xl text-[#1a3a6d] font-medium mb-5 leading-relaxed">
                  Now that we've explored both materials in detail, let's compare UPVC and aluminum windows directly across key factors that matter most to homeowners.
                </p>
              </div>
              
              <div className="overflow-hidden rounded-xl border border-gray-200 my-8">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-[#1a3a6d]">
                    <tr>
                      <th scope="col" className="px-6 py-4 text-left text-sm font-semibold text-white">Feature</th>
                      <th scope="col" className="px-6 py-4 text-left text-sm font-semibold text-white">UPVC Windows</th>
                      <th scope="col" className="px-6 py-4 text-left text-sm font-semibold text-white">Aluminum Windows</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    <tr className="hover:bg-gray-50">
                      <td className="px-6 py-4 text-sm font-medium text-[#1a3a6d]">Cost</td>
                      <td className="px-6 py-4 text-sm text-accessible-gray">More affordable upfront (£300-£800 per window)</td>
                      <td className="px-6 py-4 text-sm text-accessible-gray">Higher initial cost (£500-£1,200 per window)</td>
                    </tr>
                    <tr className="hover:bg-gray-50">
                      <td className="px-6 py-4 text-sm font-medium text-[#1a3a6d]">Thermal Efficiency</td>
                      <td className="px-6 py-4 text-sm text-accessible-gray">Excellent (inherent insulating properties)</td>
                      <td className="px-6 py-4 text-sm text-accessible-gray">Good with thermal breaks, but naturally conductive</td>
                    </tr>
                    <tr className="hover:bg-gray-50">
                      <td className="px-6 py-4 text-sm font-medium text-[#1a3a6d]">Durability</td>
                      <td className="px-6 py-4 text-sm text-accessible-gray">20-30 years with minimal maintenance</td>
                      <td className="px-6 py-4 text-sm text-accessible-gray">30-40+ years with proper care</td>
                    </tr>
                    <tr className="hover:bg-gray-50">
                      <td className="px-6 py-4 text-sm font-medium text-[#1a3a6d]">Maintenance</td>
                      <td className="px-6 py-4 text-sm text-accessible-gray">Very low (simple cleaning only)</td>
                      <td className="px-6 py-4 text-sm text-accessible-gray">Low (occasional cleaning and hardware checks)</td>
                    </tr>
                    <tr className="hover:bg-gray-50">
                      <td className="px-6 py-4 text-sm font-medium text-[#1a3a6d]">Frame Size</td>
                      <td className="px-6 py-4 text-sm text-accessible-gray">Bulkier frames, less glass area</td>
                      <td className="px-6 py-4 text-sm text-accessible-gray">Slimmer frames, maximizing glass area</td>
                    </tr>
                    <tr className="hover:bg-gray-50">
                      <td className="px-6 py-4 text-sm font-medium text-[#1a3a6d]">Aesthetics</td>
                      <td className="px-6 py-4 text-sm text-accessible-gray">Limited color options, traditional look</td>
                      <td className="px-6 py-4 text-sm text-accessible-gray">Wide range of finishes, contemporary appearance</td>
                    </tr>
                    <tr className="hover:bg-gray-50">
                      <td className="px-6 py-4 text-sm font-medium text-[#1a3a6d]">Environmental Impact</td>
                      <td className="px-6 py-4 text-sm text-accessible-gray">Recyclable but with some limitations</td>
                      <td className="px-6 py-4 text-sm text-accessible-gray">Highly recyclable (up to 95% of material)</td>
                    </tr>
                    <tr className="hover:bg-gray-50">
                      <td className="px-6 py-4 text-sm font-medium text-[#1a3a6d]">Best For</td>
                      <td className="px-6 py-4 text-sm text-accessible-gray">Standard residential properties, budget-conscious homeowners</td>
                      <td className="px-6 py-4 text-sm text-accessible-gray">Contemporary designs, large openings, architectural projects</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            {/* Conclusion Section */}
            <div id="conclusion" className="mb-16 scroll-mt-24">
              <h2 className="text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-6 pb-2 border-b border-gray-200 flex items-center">
                <span className="w-8 h-8 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-3 text-sm">5</span>
                Conclusion & Recommendations
              </h2>
              
              <div className="prose prose-lg max-w-none">
                <p className="text-accessible-gray mb-6 leading-relaxed">
                  Choosing between UPVC and aluminum windows ultimately depends on your specific requirements, property type, and budget constraints. Based on our extensive experience installing both window types across Newcastle and the North East, here are our recommendations:
                </p>
                
                <div className="bg-blue-50 border-l-4 border-[#1a3a6d] p-5 my-8 rounded-r-xl">
                  <h3 className="text-[#1a3a6d] font-semibold text-lg mb-2">Choose UPVC Windows If:</h3>
                  <ul className="text-accessible-gray list-disc pl-5 space-y-2">
                    <li>You're working with a more limited budget</li>
                    <li>Energy efficiency is your top priority</li>
                    <li>You have a traditional property where slimline frames aren't essential</li>
                    <li>You want the most hassle-free maintenance experience</li>
                    <li>You live in a particularly cold area or coastal region</li>
                  </ul>
                </div>
                
                <div className="bg-blue-50 border-l-4 border-[#1a3a6d] p-5 my-8 rounded-r-xl">
                  <h3 className="text-[#1a3a6d] font-semibold text-lg mb-2">Choose Aluminum Windows If:</h3>
                  <ul className="text-accessible-gray list-disc pl-5 space-y-2">
                    <li>You have a contemporary property or desire a modern aesthetic</li>
                    <li>You need very large window spans or unusual shapes</li>
                    <li>Maximizing glass area and natural light is essential</li>
                    <li>Long-term durability (30+ years) is a priority</li>
                    <li>You're working on an architectural project requiring specific finishes</li>
                  </ul>
                </div>
                
                <p className="text-accessible-gray mb-6 leading-relaxed">
                  For many Newcastle homeowners, the ideal solution might involve using both materials strategically throughout their property. For example, aluminum frames might be perfect for a large, statement bifold door or picture window at the rear of the property, while UPVC could be the practical choice for standard windows elsewhere.
                </p>
                
                <p className="text-accessible-gray mb-6 leading-relaxed">
                  At Window Warriors, we're committed to providing honest, tailored advice based on your specific circumstances rather than pushing a one-size-fits-all approach. Our consultations involve a thorough assessment of your property, lifestyle needs, and aesthetic preferences to recommend the ideal window solution.
                </p>
              </div>
            </div>

            {/* Call to Action Section */}
            <div className="bg-gradient-to-br from-[#f8f9fa] to-[#e9f0f8] border border-gray-100 rounded-xl p-8 sm:p-10 my-12 shadow-md hover:shadow-lg transition-shadow duration-300">
              <h2 className="text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-5">
                Get Expert Window Advice
              </h2>
              <p className="text-accessible-gray mb-6 leading-relaxed">
                Still unsure which window material is right for your home? Our window specialists can provide personalized advice based on your property, budget, and specific requirements.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  href="/products-services#quote"
                  className="bg-[#1a3a6d] hover:bg-[#0f2d5c] text-white font-medium py-3.5 px-6 rounded-lg text-center shadow-sm hover:shadow transition-all duration-300 flex items-center justify-center"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                  </svg>
                  Request a Free Consultation
                </Link>
                <Link
                  href="/gallery"
                  className="bg-white hover:bg-gray-50 text-[#1a3a6d] border border-[#1a3a6d] font-medium py-3.5 px-6 rounded-lg text-center shadow-sm hover:shadow transition-all duration-300 flex items-center justify-center"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                  View Our Window Projects
                </Link>
              </div>
            </div>
            
            {/* Author Section */}
            <div className="bg-gray-50 rounded-xl p-6 border border-gray-100 shadow-sm mb-12 hover:shadow transition-shadow duration-300">
              <div className="flex items-start space-x-5">
                <Image 
                  src="/window-warriors-logo.png" 
                  alt="Mike Williams" 
                  width={70} 
                  height={70}
                  className="rounded-full border-2 border-gray-200 shadow-sm"
                />
                <div>
                  <h3 className="font-semibold text-lg text-[#1a3a6d] mb-2">Mike Williams</h3>
                  <p className="text-accessible-gray text-base leading-relaxed">Window materials specialist with over 12 years of experience in the window industry. Mike has helped hundreds of homeowners across the North East choose the perfect windows for their properties.</p>
                </div>
              </div>
            </div>
            
            {/* Related Articles */}
            <div className="mb-16">
              <div className="flex items-center mb-8">
                <div className="h-px flex-grow bg-gradient-to-r from-gray-200 to-transparent"></div>
                <h3 className="text-xl font-bold text-[#1a3a6d] px-4">Related Articles</h3>
                <div className="h-px flex-grow bg-gradient-to-l from-gray-200 to-transparent"></div>
              </div>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                {/* Related Article 1 */}
                <div className="group relative rounded-xl shadow-sm hover:shadow-md overflow-hidden transition-all duration-300 border border-gray-100 hover:border-gray-200 hover:-translate-y-1">
                  <div className="relative h-48 overflow-hidden">
                    <Image 
                      src="/images/beautiful-view-blue-lake-captured-from-inside-villa.jpg" 
                      alt="5 Benefits of UPVC Windows You Need to Know" 
                      fill 
                      className="object-cover group-hover:scale-105 transition-transform duration-700"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-[#1a3a6d]/80 to-transparent opacity-70"></div>
                    <div className="absolute bottom-0 left-0 p-4">
                      <span className="inline-block py-1 px-2 bg-[#ec4899] text-white text-xs font-medium rounded-md mb-2">
                        WINDOWS
                      </span>
                      <h4 className="text-white font-bold text-lg leading-tight">5 Benefits of UPVC Windows You Need to Know</h4>
                    </div>
                  </div>
                  <div className="p-4">
                    <p className="text-accessible-gray text-sm mb-4 line-clamp-3">
                      Discover why UPVC windows are the popular choice for homeowners looking for energy efficiency, durability, and style.
                    </p>
                    <Link 
                      href="/blog/posts/benefits-of-upvc-windows" 
                      className="text-[#1a3a6d] font-medium text-sm hover:text-[#ec4899] transition-colors inline-flex items-center"
                    >
                      Read Article
                      <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                      </svg>
                    </Link>
                  </div>
                </div>
                
                {/* Related Article 2 */}
                <div className="group relative rounded-xl shadow-sm hover:shadow-md overflow-hidden transition-all duration-300 border border-gray-100 hover:border-gray-200 hover:-translate-y-1">
                  <div className="relative h-48 overflow-hidden">
                    <Image 
                      src="/images/exterior-home.jpg" 
                      alt="Energy Efficient Windows: How They Benefit Your Home and Budget" 
                      fill 
                      className="object-cover group-hover:scale-105 transition-transform duration-700"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-[#1a3a6d]/80 to-transparent opacity-70"></div>
                    <div className="absolute bottom-0 left-0 p-4">
                      <span className="inline-block py-1 px-2 bg-[#ec4899] text-white text-xs font-medium rounded-md mb-2">
                        ENERGY SAVING
                      </span>
                      <h4 className="text-white font-bold text-lg leading-tight">Energy Efficiency: Reduce Your Bills</h4>
                    </div>
                  </div>
                  <div className="p-4">
                    <p className="text-accessible-gray text-sm mb-4 line-clamp-3">
                      Find out how modern window technology can significantly cut your energy bills and make your home more comfortable year-round.
                    </p>
                    <Link 
                      href="/blog/posts/energy-efficiency-windows" 
                      className="text-[#1a3a6d] font-medium text-sm hover:text-[#ec4899] transition-colors inline-flex items-center"
                    >
                      Read Article
                      <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                      </svg>
                    </Link>
                  </div>
                </div>
              </div>
              
              <div className="flex justify-center mt-8">
                <Link 
                  href="/blog" 
                  className="bg-white hover:bg-gray-50 text-[#1a3a6d] border border-[#1a3a6d]/30 font-medium py-2.5 px-5 rounded-lg transition-colors inline-flex items-center"
                >
                  View All Articles
                  <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
} 


