'use client';

import React, { useState, useEffect, useMemo } from 'react';
import Link from 'next/link';
import Image from 'next/image';

const BlogContent = () => {
  // Blog posts data
  const blogPosts = [
    {
      id: 1,
      title: "5 Benefits of UPVC Windows You Need to Know",
      description: "Discover why UPVC windows are the popular choice for homeowners looking for energy efficiency, durability, and style.",
      image: "/images/optimized/beautiful-view-blue-lake-captured-from-inside-villa.webp",
      category: "Windows",
      date: "June 15, 2023",
      featured: true,
      readTime: "5 min read",
      slug: "benefits-of-upvc-windows",
      tags: ["Windows", "UPVC", "Energy Efficiency", "Home Tips"]
    },
    {
      id: 2,
      title: "How to Choose the Perfect Front Door for Your Home",
      description: "Your front door makes a statement about your home. Learn how to choose the right style, material, and security features.",
      image: "/images/optimized/front-view-front-door-with-blue-wall.webp",
      category: "Doors",
      date: "July 3, 2023",
      featured: true,
      readTime: "7 min read",
      slug: "choosing-perfect-front-door",
      tags: ["Doors", "Security", "Installation", "Home Tips"]
    },
    {
      id: 3,
      title: "Energy Efficiency: How New Windows Can Reduce Your Bills",
      description: "Find out how modern window technology can significantly cut your energy bills and make your home more comfortable year-round.",
      image: "/images/optimized/exterior-home.webp",
      category: "Energy Saving",
      date: "August 22, 2023",
      featured: true,
      readTime: "6 min read",
      slug: "energy-efficiency-windows",
      tags: ["Energy Efficiency", "Windows", "Home Tips", "UPVC"]
    },
    {
      id: 4,
      title: "UPVC vs. Aluminum Windows: A Comprehensive Comparison",
      description: "Compare the pros and cons of UPVC and aluminum window frames to find the best option for your home's energy efficiency needs.",
      image: "/images/optimized/exterior-home.webp",
      category: "Windows",
      date: "February 15, 2024",
      featured: false,
      readTime: "10 min read",
      slug: "upvc-vs-aluminum-windows-comparison",
      tags: ["Windows", "UPVC", "Aluminum", "Comparison", "Energy Efficiency"]
    },
    {
      id: 5,
      title: "Window Styles for Period Properties: Maintaining Character",
      description: "How to upgrade your period property's windows while preserving its historical charm and character.",
      image: "/images/optimized/ancient-window-old-building-quebec-city.webp",
      category: "Windows",
      date: "October 12, 2023",
      featured: false,
      readTime: "12 min read",
      slug: "window-styles-for-period-properties",
      tags: ["Windows", "Period Properties", "Heritage", "Installation", "Home Tips"]
    },
    {
      id: 6,
      title: "Understanding Window Energy Ratings: A Buyer's Guide",
      description: "Decode window energy ratings and learn what they mean for your home's efficiency and comfort.",
      image: "/images/optimized/beautiful-view-blue-lake-captured-from-inside-villa.webp",
      category: "Energy Saving",
      date: "November 8, 2023",
      featured: true,
      readTime: "12 min read",
      slug: "understanding-window-energy-ratings",
      tags: ["Energy Efficiency", "Windows", "UPVC"]
    },
  ];

  // Categories with count
  const categories = [
    { name: "Windows", count: blogPosts.filter(post => post.category === "Windows").length },
    { name: "Doors", count: blogPosts.filter(post => post.category === "Doors").length },
    { name: "Energy Saving", count: blogPosts.filter(post => post.category === "Energy Saving").length },
    { name: "Home Improvement", count: blogPosts.filter(post => post.category === "Home Improvement").length },
    { name: "Maintenance", count: blogPosts.filter(post => post.category === "Maintenance").length },
  ].filter(category => category.count > 0);

  // Popular tags - Dynamically generated from blog posts
  const popularTags = Array.from(
    blogPosts.reduce((acc, post) => {
      post.tags.forEach(tag => {
        if (acc.has(tag)) {
          acc.set(tag, acc.get(tag) + 1);
        } else {
          acc.set(tag, 1);
        }
      });
      return acc;
    }, new Map())
  )
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10)
    .map(item => item[0]);

  // State for search, category and tag filtering
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedTag, setSelectedTag] = useState('');
  const [filteredPosts, setFilteredPosts] = useState(blogPosts);
  const [visiblePostsCount, setVisiblePostsCount] = useState(3);

  // Memoize filtered posts to avoid unnecessary recalculations
  const filteredPostsData = useMemo(() => {
    let result = blogPosts;
    
    // Filter by search term
    if (searchTerm) {
      result = result.filter(post => 
        post.title.toLowerCase().includes(searchTerm.toLowerCase()) || 
        post.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    // Filter by category
    if (selectedCategory) {
      result = result.filter(post => 
        post.category === selectedCategory
      );
    }
    
    // Filter by tag
    if (selectedTag) {
      result = result.filter(post => 
        post.tags.includes(selectedTag)
      );
    }
    
    return result;
  }, [searchTerm, selectedCategory, selectedTag]);

  // Effect to set filtered posts state
  useEffect(() => {
    setFilteredPosts(filteredPostsData);
    // Reset visible posts count when filters change
    setVisiblePostsCount(3);
  }, [filteredPostsData]);

  // Function to load more posts
  const loadMorePosts = () => {
    setVisiblePostsCount(prevCount => Math.min(prevCount + 3, filteredPosts.length));
  };

  // Effect to check URL parameters for filtering
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const params = new URLSearchParams(window.location.search);
      const categoryParam = params.get('category');
      const tagParam = params.get('tag');
      
      if (categoryParam) {
        setSelectedCategory(categoryParam);
      }
      
      if (tagParam) {
        setSelectedTag(tagParam);
      }
    }
  }, []);

  // Handler for search input
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Handler for category selection
  const handleCategoryClick = (category: string) => {
    const newCategory = selectedCategory === category ? '' : category;
    setSelectedCategory(newCategory);
    
    // Update URL
    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href);
      if (newCategory) {
        url.searchParams.set('category', newCategory);
        url.searchParams.delete('tag');
      } else {
        url.searchParams.delete('category');
      }
      window.history.pushState({}, '', url);
    }
    
    // Reset tag when category changes
    if (selectedCategory !== category) {
      setSelectedTag('');
    }
  };

  // Handler for tag selection
  const handleTagClick = (tag: string) => {
    const newTag = selectedTag === tag ? '' : tag;
    setSelectedTag(newTag);
    
    // Update URL
    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href);
      if (newTag) {
        url.searchParams.set('tag', newTag);
        url.searchParams.delete('category');
      } else {
        url.searchParams.delete('tag');
      }
      window.history.pushState({}, '', url);
    }
    
    // Reset category when tag changes
    if (selectedTag !== tag) {
      setSelectedCategory('');
    }
  };

  // Handler to reset all filters
  const resetFilters = () => {
    setSearchTerm('');
    setSelectedCategory('');
    setSelectedTag('');
    
    // Update URL
    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href);
      url.search = '';
      window.history.pushState({}, '', url);
    }
  };

  return (
    <>
      {/* Featured Posts Section */}
      <section className="py-8 sm:py-12 md:py-16 lg:py-20 relative overflow-hidden">
        {/* Decorative background elements */}
        <div className="absolute bottom-0 right-0 w-64 sm:w-96 h-64 sm:h-96 bg-gradient-to-bl from-[#ec4899]/5 to-transparent rounded-full blur-3xl"></div>
        
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-8 sm:mb-10 md:mb-14">
            <div className="inline-block mb-2">
              <div className="relative inline-block">
                <span className="absolute inset-x-0 bottom-0 h-1.5 sm:h-2 bg-gradient-to-r from-[#ec4899]/30 to-[#ec4899]/80 transform -rotate-1"></span>
                <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-[#1a3a6d] relative z-10">Featured <span className="text-gradient-logo">Articles</span></h2>
              </div>
            </div>
            <p className="text-base sm:text-lg text-gray-700 mt-2">Our most popular and informative reads</p>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
            {blogPosts.filter(post => post.featured).map((post) => (
              <div key={post.id} className="glass-card rounded-xl shadow-md overflow-hidden transition-all duration-500 hover:shadow-xl hover:-translate-y-1">
                <div className="relative img-zoom-container overflow-hidden h-40 sm:h-48 md:h-56">
                  <Image 
                    src={post.image}
                    alt={post.title}
                    fill
                    sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
                    className="object-cover img-zoom"
                    priority={post.id <= 3}
                    loading={post.id <= 3 ? "eager" : "lazy"}
                  />
                  <div className="absolute top-0 left-0 m-3 sm:m-4">
                    <span className="inline-block py-1 px-2 sm:px-3 rounded-full text-xs font-semibold bg-[#ec4899] text-white">
                      {post.category}
                    </span>
                  </div>
                </div>
                
                <div className="p-4 sm:p-6">
                  <div className="flex justify-between items-center text-xs sm:text-sm text-accessible-gray mb-2">
                    <span>{post.date}</span>
                    <span>{post.readTime}</span>
                  </div>
                  <h3 className="font-bold text-lg sm:text-xl md:text-2xl text-[#1a3a6d] mb-2 sm:mb-3 leading-tight">{post.title}</h3>
                  <p className="text-sm sm:text-base text-gray-700 mb-3 sm:mb-4 line-clamp-3">{post.description}</p>
                  <div className="flex justify-between items-center">
                    <Link 
                      href={`/blog/posts/${post.slug}`}
                      className="text-sm sm:text-base text-[#1a3a6d] font-medium hover:text-[#ec4899] transition"
                    >
                      Read More
                    </Link>
                    <span className="bg-gray-100 h-7 w-7 sm:h-8 sm:w-8 rounded-full flex items-center justify-center">
                      <svg className="w-3 h-3 sm:w-4 sm:h-4 text-[#1a3a6d]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                      </svg>
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Main Blog Content */}
      <section className="py-12 sm:py-16 md:py-20 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="lg:flex lg:gap-8 xl:gap-12">
            {/* Blog posts grid */}
            <div className="lg:w-2/3">
              {/* Active Filters Display */}
              {(searchTerm || selectedCategory || selectedTag) && (
                <div className="mb-6 sm:mb-8 p-4 sm:p-6 glass-card rounded-xl">
                  <div className="flex flex-wrap items-center gap-2 sm:gap-3">
                    <span className="text-sm sm:text-base text-[#1a3a6d] font-medium">Active Filters:</span>
                    
                    {selectedCategory && (
                      <span className="bg-[#1a3a6d] text-white px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm flex items-center">
                        Category: {selectedCategory}
                        <button 
                          onClick={() => setSelectedCategory('')}
                          className="ml-1 sm:ml-2 text-white hover:text-[#ec4899]"
                        >
                          <svg className="w-3 h-3 sm:w-4 sm:h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </span>
                    )}
                    
                    {selectedTag && (
                      <span className="bg-[#1a3a6d] text-white px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm flex items-center">
                        Tag: {selectedTag}
                        <button 
                          onClick={() => setSelectedTag('')}
                          className="ml-1 sm:ml-2 text-white hover:text-[#ec4899]"
                        >
                          <svg className="w-3 h-3 sm:w-4 sm:h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </span>
                    )}
                    
                    {searchTerm && (
                      <span className="bg-[#1a3a6d] text-white px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm flex items-center">
                        Search: {searchTerm}
                        <button 
                          onClick={() => setSearchTerm('')}
                          className="ml-1 sm:ml-2 text-white hover:text-[#ec4899]"
                        >
                          <svg className="w-3 h-3 sm:w-4 sm:h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </span>
                    )}
                    
                    <button 
                      onClick={resetFilters}
                      className="ml-auto text-xs sm:text-sm text-[#ec4899] hover:text-[#1a3a6d] font-medium"
                    >
                      Clear All Filters
                    </button>
                  </div>
                </div>
              )}
              
              <div className="mb-6 sm:mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-0">
                <div>
                  <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-2 sm:mb-4">
                    {searchTerm || selectedCategory || selectedTag ? 'Search Results' : 'Latest Articles'}
                  </h2>
                  <div className="h-0.5 sm:h-1 w-16 sm:w-24 bg-[#ec4899]"></div>
                </div>
                
                {/* Results count */}
                {(searchTerm || selectedCategory || selectedTag) && (
                  <div className="text-sm sm:text-base text-gray-600">
                    {filteredPosts.length} {filteredPosts.length === 1 ? 'result' : 'results'} found
                  </div>
                )}
              </div>
              
              {/* No results message */}
              {filteredPosts.length === 0 && (
                <div className="glass-card rounded-xl p-6 sm:p-8 text-center">
                  <div className="text-4xl sm:text-5xl mb-4">😕</div>
                  <h3 className="text-lg sm:text-xl font-bold text-[#1a3a6d] mb-2">No articles found</h3>
                  <p className="text-sm sm:text-base text-gray-600 mb-4 sm:mb-6">
                    We couldn't find any articles matching your search criteria.
                  </p>
                  <button 
                    onClick={resetFilters}
                    className="btn-primary text-sm sm:text-base px-4 sm:px-6 py-2 sm:py-3"
                  >
                    Clear Filters
                  </button>
                </div>
              )}
              
              <div className="grid gap-6 sm:gap-8">
                {filteredPosts.slice(0, visiblePostsCount).map((post) => (
                  <div key={post.id} className="glass-card rounded-xl shadow-md overflow-hidden flex flex-col sm:flex-row transition-all duration-500 hover:shadow-xl">
                    <div className="relative sm:w-1/3 h-48 sm:h-auto">
                      <Image 
                        src={post.image}
                        alt={post.title}
                        fill
                        sizes="(max-width: 640px) 100vw, 33vw"
                        className="object-cover transition-transform duration-700 hover:scale-110"
                        loading="lazy"
                      />
                    </div>
                    
                    <div className="p-4 sm:p-6 sm:w-2/3">
                      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center text-xs sm:text-sm text-accessible-gray mb-2 gap-1 sm:gap-0">
                        <button
                          onClick={() => handleCategoryClick(post.category)}
                          className={`inline-block py-1 px-2 sm:px-3 rounded-full text-xs font-semibold self-start ${
                            selectedCategory === post.category 
                              ? 'bg-[#ec4899] text-white' 
                              : 'bg-[#1a3a6d]/10 text-[#1a3a6d] hover:bg-[#1a3a6d]/20'
                          } transition-colors`}
                        >
                          {post.category}
                        </button>
                        <span className="text-xs sm:text-sm">{post.date} • {post.readTime}</span>
                      </div>
                      <h3 className="font-bold text-lg sm:text-xl text-[#1a3a6d] mb-2 sm:mb-3 leading-tight">{post.title}</h3>
                      <p className="text-sm sm:text-base text-gray-700 mb-3 sm:mb-4 line-clamp-3">{post.description}</p>
                      
                      {/* Tags */}
                      <div className="flex flex-wrap gap-1 sm:gap-2 mb-3 sm:mb-4">
                        {post.tags.map((tag, idx) => (
                          <button
                            key={idx}
                            onClick={() => handleTagClick(tag)}
                            className={`text-xs px-2 py-1 rounded-full ${
                              selectedTag === tag
                                ? 'bg-[#ec4899] text-white'
                                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                            } transition-colors`}
                          >
                            #{tag}
                          </button>
                        ))}
                      </div>
                      
                      <Link 
                        href={`/blog/posts/${post.slug}`} 
                        className="text-sm sm:text-base text-[#1a3a6d] font-medium hover:text-[#ec4899] transition"
                      >
                        Read More
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
              
              {/* Hide Load More button if we're filtering or if all posts are visible */}
              {!(searchTerm || selectedCategory || selectedTag || visiblePostsCount >= filteredPosts.length) && (
                <div className="mt-8 sm:mt-12 flex justify-center">
                  <button 
                    className="btn-primary inline-flex items-center gap-2 text-sm sm:text-base px-4 sm:px-6 py-2 sm:py-3"
                    onClick={loadMorePosts}
                  >
                    <span>Load More Articles</span>
                    <svg className="w-4 h-4 sm:w-5 sm:h-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M19 9L12 16L5 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </button>
                </div>
              )}
            </div>
            
            {/* Sidebar */}
            <div className="lg:w-1/3 mt-8 sm:mt-12 lg:mt-0">
              {/* Search - Enhanced with functionality */}
              <div className="glass-card rounded-xl p-4 sm:p-6 mb-6 sm:mb-8">
                <h3 className="font-bold text-base sm:text-lg mb-3 sm:mb-4 text-[#1a3a6d]">Search</h3>
                <div className="relative">
                  <input 
                    type="text" 
                    placeholder="Search articles..." 
                    className="w-full px-3 sm:px-4 py-2 sm:py-3 text-sm sm:text-base rounded-lg bg-gray-100 border-none focus:ring-2 focus:ring-[#ec4899]"
                    value={searchTerm}
                    onChange={handleSearchChange}
                  />
                  <button 
                    className={`absolute right-2 top-1/2 transform -translate-y-1/2 ${
                      searchTerm ? 'bg-[#1a3a6d]' : 'bg-[#ec4899]'
                    } text-white p-1.5 sm:p-2 rounded-md transition-colors`}
                    onClick={() => searchTerm ? setSearchTerm('') : null}
                    aria-label={searchTerm ? "Clear search" : "Search"}
                  >
                    {searchTerm ? (
                      <svg className="w-3 h-3 sm:w-4 sm:h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    ) : (
                      <svg className="w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                      </svg>
                    )}
                  </button>
                </div>
              </div>
              
              {/* Categories */}
              <div className="glass-card rounded-xl p-4 sm:p-6 mb-6 sm:mb-8">
                <h3 className="font-bold text-base sm:text-lg mb-3 sm:mb-4 text-[#1a3a6d]">Categories</h3>
                <ul>
                  {categories.map((category, index) => (
                    <li key={index} className="mb-1 sm:mb-2 last:mb-0">
                      <button 
                        onClick={() => handleCategoryClick(category.name)}
                        className={`w-full flex justify-between items-center p-2 rounded-lg text-sm sm:text-base ${
                          selectedCategory === category.name 
                            ? 'bg-[#1a3a6d]/10' 
                            : 'hover:bg-gray-100'
                        } transition-colors`}
                      >
                        <span className={`${
                          selectedCategory === category.name 
                            ? 'text-[#1a3a6d] font-medium' 
                            : 'text-gray-700'
                        }`}>
                          {category.name}
                        </span>
                        <span className={`${
                          selectedCategory === category.name 
                            ? 'bg-[#ec4899] text-white' 
                            : 'bg-[#1a3a6d]/10 text-[#1a3a6d]'
                        } text-xs font-semibold rounded-full px-2 py-1 transition-colors`}>
                          {category.count}
                        </span>
                      </button>
                    </li>
                  ))}
                </ul>
              </div>
              
              {/* Popular Tags */}
              <div className="glass-card rounded-xl p-4 sm:p-6 mb-6 sm:mb-8">
                <h3 className="font-bold text-base sm:text-lg mb-3 sm:mb-4 text-[#1a3a6d]">Popular Tags</h3>
                <div className="flex flex-wrap gap-1 sm:gap-2">
                  {popularTags.map((tag, index) => (
                    <button 
                      key={index}
                      onClick={() => handleTagClick(tag)}
                      className={`${
                        selectedTag === tag
                          ? 'bg-[#1a3a6d] text-white'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      } text-xs sm:text-sm px-2 sm:px-3 py-1 rounded-full transition-colors`}
                    >
                      {tag}
                    </button>
                  ))}
                </div>
              </div>
              
              {/* Newsletter */}
              <div className="glass-card rounded-xl p-4 sm:p-6 bg-gradient-to-br from-[#1a3a6d] to-[#0f172a] text-white">
                <h3 className="font-bold text-base sm:text-lg mb-2">Subscribe to our Newsletter</h3>
                <p className="text-accessible-muted text-xs sm:text-sm mb-3 sm:mb-4">Stay updated with our latest articles and window industry news</p>
                <input 
                  type="email" 
                  placeholder="Your email address" 
                  className="w-full mb-3 px-3 sm:px-4 py-2 sm:py-3 text-sm sm:text-base rounded-lg bg-white/20 backdrop-blur-sm border-none focus:ring-2 focus:ring-[#ec4899] text-white placeholder-white/60"
                />
                <button className="w-full bg-[#ec4899] text-white font-medium py-2 sm:py-3 text-sm sm:text-base rounded-lg hover:bg-[#ec4899]/90 transition-colors">
                  Subscribe
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default BlogContent; 