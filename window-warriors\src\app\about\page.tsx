import React from 'react';
import { Metadata } from 'next';
import Image from 'next/image';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'About Window Warriors | Expert UPVC Window & Door Specialists',
  description: 'Learn about Window Warriors, your trusted UPVC window and door specialists in Newcastle, Durham and the North East UK. Discover our story, our team and our commitment to quality.',
  keywords: 'window installation Newcastle, door fitters Durham, UPVC specialists North East, window company Sunderland, family-owned window business UK',
  openGraph: {
    title: 'About Window Warriors | Expert Window & Door Specialists',
    description: 'Learn about our journey as trusted UPVC window and door specialists serving Newcastle and the North East since 2008. Discover our values and commitment to quality.',
    url: 'https://windowwarriors.uk/about',
    siteName: 'Window Warriors',
    images: [
      {
        url: 'https://windowwarriors.uk/images/factory-worker-working-warehouse-handling-metal-material-productio.jpg',
        width: 1200,
        height: 630,
        alt: 'About Window Warriors - UPVC Window Specialists',
      },
    ],
    locale: 'en_GB',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'About Window Warriors | Expert Window & Door Specialists',
    description: 'Learn about our journey as trusted UPVC window and door specialists serving Newcastle and the North East since 2008.',
    images: ['https://windowwarriors.uk/images/factory-worker-working-warehouse-handling-metal-material-productio.jpg'],
  },
  alternates: {
    canonical: 'https://windowwarriors.uk/about',
  },
};

const AboutPage = () => {
  return (
    <main className="flex min-h-screen flex-col">
      {/* Hero Section - Mobile Optimized */}
      <section className="relative min-h-[500px] sm:min-h-[600px] lg:min-h-screen overflow-hidden bg-gradient-to-br from-[#1a3a6d] via-[#1a3a6d] to-[#ec4899]/20">
        {/* Background Image - Mobile Optimized */}
        <div className="absolute inset-0 bg-cover bg-center" style={{
          backgroundImage: 'url("/images/factory-worker-working-warehouse-handling-metal-material-productio.jpg")',
          backgroundAttachment: 'scroll',
          backgroundPosition: 'center 30%',
          backgroundSize: 'cover',
        }}></div>
        
        {/* Multi-layered Background Overlays */}
        <div className="absolute inset-0">
          {/* Primary gradient background */}
          <div className="absolute inset-0 bg-gradient-to-br from-[#1a3a6d]/95 via-[#1a3a6d]/90 to-[#ec4899]/70"></div>
          
          {/* Animated gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-[#ec4899]/10 to-transparent animate-gradient-x"></div>
          
          {/* Noise texture */}
          <div className="absolute inset-0 opacity-[0.02] bg-noise"></div>
        </div>

        {/* Floating Pink Elements - Mobile Responsive */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {/* Large floating blurs - Responsive sizing */}
          <div className="absolute top-1/4 left-1/4 w-32 sm:w-48 md:w-64 h-32 sm:h-48 md:h-64 bg-[#ec4899]/20 rounded-full blur-3xl animate-float-slow"></div>
          <div className="absolute top-1/3 right-1/3 w-24 sm:w-36 md:w-48 h-24 sm:h-36 md:h-48 bg-[#d946ef]/15 rounded-full blur-2xl animate-float-reverse"></div>
          <div className="absolute bottom-1/4 left-1/3 w-28 sm:w-42 md:w-56 h-28 sm:h-42 md:h-56 bg-[#f9a8d4]/10 rounded-full blur-3xl animate-float"></div>
          
          {/* Glass particles - Mobile friendly sizing */}
          <div className="absolute top-[20%] right-[20%] w-4 sm:w-6 md:w-8 h-4 sm:h-6 md:h-8 bg-white/10 rounded-full backdrop-blur-sm animate-float-slow border border-white/20"></div>
          <div className="absolute top-[60%] left-[15%] w-3 sm:w-5 md:w-6 h-3 sm:h-5 md:h-6 bg-[#ec4899]/20 rounded-full backdrop-blur-sm animate-float border border-[#ec4899]/30"></div>
          <div className="absolute top-[40%] right-[10%] w-2 sm:w-3 md:w-4 h-2 sm:h-3 md:h-4 bg-[#d946ef]/30 rounded-full animate-pulse"></div>
          <div className="absolute bottom-[40%] left-[20%] w-2 sm:w-2.5 md:w-3 h-2 sm:h-2.5 md:h-3 bg-white/30 rounded-full animate-ping"></div>
          
          {/* Geometric accents - Responsive */}
          <div className="absolute top-[25%] left-[60%] w-8 sm:w-10 md:w-12 h-8 sm:h-10 md:h-12 border border-[#ec4899]/30 rotate-45 animate-spin-slow"></div>
          <div className="absolute bottom-[35%] right-[25%] w-6 sm:w-7 md:w-8 h-6 sm:h-7 md:h-8 border-2 border-white/20 rounded-full animate-pulse"></div>
        </div>

        {/* Main Content - Mobile Optimized */}
        <div className="container mx-auto px-4 sm:px-6 relative z-20 min-h-[500px] sm:min-h-[600px] lg:min-h-screen flex flex-col justify-center pt-16 sm:pt-20 md:pt-24 lg:pt-32">
          <div className="max-w-4xl">
            {/* Brand Tag - Mobile Responsive */}
            <div className="inline-flex items-center mb-4 sm:mb-6 px-3 sm:px-4 py-1.5 sm:py-2 rounded-full border border-[#ec4899]/30 bg-white/5 backdrop-blur-sm animate-fade-in">
              <div className="w-1.5 sm:w-2 h-1.5 sm:h-2 bg-[#ec4899] rounded-full mr-2 sm:mr-3 animate-pulse"></div>
              <span className="text-accessible-light text-xs sm:text-sm font-medium tracking-wider">WINDOW WARRIORS</span>
            </div>

            {/* Main Heading - Mobile Typography */}
            <h1 className="text-3xl sm:text-4xl md:text-6xl lg:text-7xl xl:text-8xl font-bold mb-4 sm:mb-6 animate-slide-up">
              <span className="text-white block mb-1 sm:mb-2">About</span>
              <span className="text-gradient-logo relative inline-block">
                Us
                <div className="absolute -bottom-1 sm:-bottom-2 left-0 w-full h-0.5 sm:h-1 bg-gradient-to-r from-[#ec4899] to-[#d946ef] rounded-full animate-shimmer"></div>
              </span>
            </h1>

            {/* Description - Mobile Responsive */}
            <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-accessible-muted mb-6 sm:mb-8 max-w-2xl leading-relaxed animate-fade-in" style={{ animationDelay: '0.3s' }}>
              Get to know the team behind the North East's premier UPVC window and door specialists.
            </p>

            {/* CTA Buttons - Mobile Stack */}
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 mb-8 sm:mb-12 animate-slide-up" style={{ animationDelay: '0.6s' }}>
              <Link href="/contact" 
                className="group relative px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-[#ec4899] to-[#d946ef] text-white font-semibold rounded-full overflow-hidden transition-all duration-300 hover:shadow-lg hover:shadow-[#ec4899]/25 hover:scale-105 text-center text-sm sm:text-base">
                <span className="relative z-10">Get in Touch</span>
                <div className="absolute inset-0 bg-gradient-to-r from-[#d946ef] to-[#ec4899] opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </Link>
              
              <Link href="/products-services" 
                className="bg-gradient-to-r from-[#d946ef] to-[#ec4899] text-white font-semibold px-6 sm:px-8 py-3 sm:py-4 rounded-full hover:shadow-lg hover:shadow-[#d946ef]/25 transition-all duration-300 text-center text-sm sm:text-base">
                Explore Our Services
              </Link>
            </div>

            {/* Trust Badges - Mobile Layout */}
            <div className="flex flex-col sm:flex-row flex-wrap items-center gap-3 sm:gap-6 animate-fade-in" style={{ animationDelay: '0.9s' }}>
              <div className="flex items-center space-x-2 sm:space-x-3 px-3 sm:px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full border border-white/20 hover:bg-white/15 transition-all duration-300 group">
                <div className="w-6 sm:w-8 h-6 sm:h-8 bg-[#ec4899]/20 rounded-full flex items-center justify-center group-hover:bg-[#ec4899]/30 transition-colors duration-300">
                  <span className="text-white text-xs sm:text-sm font-bold">15+</span>
                </div>
                <span className="text-accessible-light text-xs sm:text-sm font-medium">Years Experience</span>
              </div>
              
              <div className="flex items-center space-x-2 sm:space-x-3 px-3 sm:px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full border border-white/20 hover:bg-white/15 transition-all duration-300 group">
                <div className="w-6 sm:w-8 h-6 sm:h-8 bg-[#d946ef]/20 rounded-full flex items-center justify-center group-hover:bg-[#d946ef]/30 transition-colors duration-300">
                  <span className="text-white text-xs sm:text-sm font-bold">1K+</span>
                </div>
                <span className="text-accessible-light text-xs sm:text-sm font-medium">Happy Customers</span>
              </div>
            </div>
          </div>
        </div>

        {/* Floating Cards - Responsive Display */}
        <div className="hidden lg:block absolute bottom-16 sm:bottom-20 right-4 sm:right-8 space-y-3 sm:space-y-4 z-10">
          <div className="w-40 sm:w-48 bg-white/10 backdrop-blur-lg rounded-lg p-3 sm:p-4 border border-white/20 animate-float">
            <div className="flex items-center space-x-2 sm:space-x-3 mb-2">
              <div className="w-6 sm:w-8 h-6 sm:h-8 bg-[#ec4899]/20 rounded-full flex items-center justify-center">
                <svg className="w-3 sm:w-4 h-3 sm:h-4 text-[#ec4899]" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              </div>
              <span className="text-white font-medium text-xs sm:text-sm">Premium Quality</span>
            </div>
            <p className="text-white/70 text-xs">UPVC windows & doors</p>
          </div>
          
          <div className="w-40 sm:w-48 bg-white/10 backdrop-blur-lg rounded-lg p-3 sm:p-4 border border-white/20 animate-float" style={{ animationDelay: '0.5s' }}>
            <div className="flex items-center space-x-2 sm:space-x-3 mb-2">
              <div className="w-6 sm:w-8 h-6 sm:h-8 bg-[#d946ef]/20 rounded-full flex items-center justify-center">
                <svg className="w-3 sm:w-4 h-3 sm:h-4 text-[#d946ef]" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <span className="text-white font-medium text-xs sm:text-sm">Fully Certified</span>
            </div>
            <p className="text-white/70 text-xs">All installations guaranteed</p>
          </div>
        </div>

        {/* Scroll Indicator - Mobile Responsive */}
        <div className="absolute bottom-3 sm:bottom-4 left-1/2 transform -translate-x-1/2 z-30 animate-bounce">
          <div className="w-5 sm:w-6 h-8 sm:h-10 border-2 border-white/30 rounded-full flex justify-center">
            <div className="w-0.5 sm:w-1 h-2 sm:h-3 bg-[#ec4899] rounded-full mt-1.5 sm:mt-2 animate-pulse"></div>
          </div>
          <p className="text-accessible-subtle text-xs mt-1 sm:mt-2 text-center">Scroll</p>
        </div>
      </section>

      {/* Our Story Section - Mobile Optimized */}
      <section className="relative py-12 sm:py-16 md:py-20 lg:py-24 bg-white">
        
        {/* Decorative elements - Mobile responsive */}
        <div className="absolute top-16 sm:top-32 left-0 w-32 sm:w-48 md:w-64 h-32 sm:h-48 md:h-64 bg-gradient-to-r from-[#ec4899]/20 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-10 sm:bottom-20 right-0 w-40 sm:w-60 md:w-80 h-40 sm:h-60 md:h-80 bg-gradient-to-l from-[#1a3a6d]/10 to-transparent rounded-full blur-3xl"></div>
        
        <div className="container relative mx-auto px-4 sm:px-6 lg:px-8 pt-8 sm:pt-12 md:pt-16">
          <div className="flex flex-col md:flex-row gap-8 sm:gap-10 md:gap-12 lg:gap-16 items-center">
            <div className="w-full md:w-1/2 relative">
              <div className="relative h-[300px] sm:h-[350px] md:h-[400px] lg:h-[500px] rounded-xl overflow-hidden shadow-xl img-zoom-container">
                <Image 
                  src="/images/optimized/male-worker-factory.webp" 
                  alt="Window Warriors team" 
                  fill
                  className="object-cover img-zoom transition-transform duration-700"
                  style={{ objectPosition: 'center 30%' }}
                  sizes="(max-width: 768px) 100vw, 50vw"
                  loading="lazy"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-[#1a3a6d]/50 to-transparent"></div>
              </div>
              <div className="absolute -bottom-4 sm:-bottom-6 -right-3 sm:-right-6 glass-card p-3 sm:p-4 md:p-6 rounded-lg max-w-[250px] sm:max-w-xs animate-float">
                <p className="text-xs sm:text-sm md:text-base font-medium text-[#1a3a6d]">
                  "We're passionate about bringing quality, security and style to homes across the North East."
                </p>
                <p className="text-right mt-1 sm:mt-2 text-xs sm:text-sm font-semibold text-[#ec4899]">— Tony F, Founder</p>
              </div>
            </div>
            
            <div className="w-full md:w-1/2 mt-8 md:mt-0">
              <div className="spinning-border p-0.5 inline-block rounded-full mb-4 sm:mb-5">
                <span className="bg-white text-[#1a3a6d] text-xs sm:text-sm font-semibold px-3 sm:px-4 py-1 sm:py-1.5 rounded-full inline-block">
                  Est. 2008
                </span>
              </div>
              <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-[#1a3a6d] mb-4 sm:mb-6">Our Journey as <span className="text-gradient-accent">Window Specialists</span></h2>
              <div className="space-y-4 sm:space-y-5 text-gray-700">
                <p className="text-sm sm:text-base md:text-lg leading-relaxed">
                  Window Warriors began as a small family operation in Newcastle upon Tyne in 2008. What started as a father-son project has grown into one of the North East's most trusted UPVC window and door specialists.
                </p>
                <p className="text-sm sm:text-base md:text-lg leading-relaxed">
                  Over the past 15 years, we've expanded our services to cover Newcastle, Durham, Sunderland and surrounding areas, building a reputation for exceptional craftsmanship and customer care.
                </p>
                <p className="text-sm sm:text-base md:text-lg leading-relaxed">
                  We remain a family-owned business with strong values at our core - quality products, precise installation, and honest pricing. Our team has grown, but our commitment to personalized service hasn't changed.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Values - Mobile Optimized */}
      <section className="py-12 sm:py-16 md:py-20 lg:py-24 bg-gradient-to-b from-white to-gray-50 relative overflow-hidden">
        {/* Decorative background elements - Mobile responsive */}
        <div className="absolute top-0 right-0 w-32 sm:w-48 md:w-64 h-32 sm:h-48 md:h-64 bg-gradient-to-bl from-[#ec4899]/10 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-40 sm:w-60 md:w-80 h-40 sm:h-60 md:h-80 bg-gradient-to-tr from-[#1a3a6d]/5 to-transparent rounded-full blur-3xl"></div>
        
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center max-w-3xl mx-auto mb-10 sm:mb-12 md:mb-16">
            <div className="inline-block mb-3 sm:mb-4">
              <div className="relative inline-block">
                <span className="absolute inset-x-0 bottom-0 h-1.5 sm:h-2 bg-gradient-to-r from-[#ec4899]/30 to-[#ec4899]/80 transform -rotate-1"></span>
                <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-[#1a3a6d] mb-2 relative z-10">Our <span className="text-gradient-accent">Values</span></h2>
              </div>
            </div>
            <p className="text-base sm:text-lg text-gray-700">The core principles that guide everything we do at Window Warriors</p>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 lg:gap-10 xl:gap-12">
            {[
              {
                icon: (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 sm:h-10 md:h-12 w-8 sm:w-10 md:w-12 mb-3 sm:mb-4 md:mb-5 text-[#ec4899]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                ),
                title: 'Quality Craftsmanship',
                description: 'We use only the finest UPVC materials and hardware, sourced from trusted UK suppliers. Every installation is handled with precision and attention to detail.'
              },
              {
                icon: (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 sm:h-10 md:h-12 w-8 sm:w-10 md:w-12 mb-3 sm:mb-4 md:mb-5 text-[#ec4899]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                ),
                title: 'Reliability',
                description: 'We show up when we say we will, complete projects on time, and stand behind our work with comprehensive guarantees. No excuses, just results.'
              },
              {
                icon: (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 sm:h-10 md:h-12 w-8 sm:w-10 md:w-12 mb-3 sm:mb-4 md:mb-5 text-[#ec4899]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                ),
                title: 'Personal Service',
                description: 'We treat every home as if it were our own, listening carefully to your needs and providing tailored solutions that fit your style, budget and requirements.'
              }
            ].map((value, index) => (
              <div 
                key={index} 
                className="glass-card relative p-4 sm:p-6 md:p-8 rounded-xl transition-all duration-500 hover:shadow-xl group border border-white/30 hover:border-[#ec4899]/30 sm:col-span-2 lg:col-span-1 last:sm:col-span-1 last:sm:mx-auto last:lg:mx-0"
              >
                <div className="absolute -top-3 sm:-top-5 -right-3 sm:-right-5 w-16 sm:w-20 h-16 sm:h-20 bg-[#ec4899]/10 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
                <div className="absolute -bottom-3 sm:-bottom-5 -left-3 sm:-left-5 w-16 sm:w-20 h-16 sm:h-20 bg-[#1a3a6d]/10 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
                
                <div className="absolute -top-6 sm:-top-8 left-4 sm:left-6 md:left-8 rounded-full p-3 sm:p-4 bg-gradient-to-br from-white to-gray-100 shadow-md group-hover:shadow-lg group-hover:-translate-y-1 transition-all duration-500 z-10">
                  {value.icon}
                </div>
                <div className="pt-10 sm:pt-12 md:pt-14">
                  <h3 className="text-lg sm:text-xl md:text-2xl font-bold text-[#1a3a6d] mb-3 sm:mb-4 group-hover:text-gradient-accent transition-all duration-500">{value.title}</h3>
                  <p className="text-sm sm:text-base text-gray-600 group-hover:text-gray-700 transition-colors duration-500 leading-relaxed">{value.description}</p>
                </div>
                
                <div className="mt-4 sm:mt-6 h-0.5 sm:h-1 w-12 sm:w-16 bg-gradient-to-r from-[#ec4899]/50 to-[#ec4899] rounded-full transform origin-left scale-0 group-hover:scale-100 transition-transform duration-500"></div>
              </div>
            ))}
          </div>
          
          <div className="mt-10 sm:mt-12 md:mt-16 flex justify-center">
            <div className="glass-card px-4 sm:px-6 py-3 sm:py-4 rounded-lg border border-white/30 inline-flex items-center shadow-lg">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 sm:h-6 w-5 sm:w-6 text-[#ec4899] mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <span className="text-[#1a3a6d] font-medium text-sm sm:text-base">Driven by values, delivered with excellence</span>
            </div>
          </div>
        </div>
      </section>

      {/* Local Service Areas - Mobile Optimized */}
      <section className="py-12 sm:py-16 md:py-20 lg:py-24 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col lg:flex-row gap-8 sm:gap-10 md:gap-12 lg:gap-16 items-center">
            <div className="w-full lg:w-1/2">
              <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-[#1a3a6d] mb-4 sm:mb-6">Serving the <span className="text-gradient-accent">North East</span></h2>
              <p className="text-base sm:text-lg text-gray-700 mb-5 sm:mb-6">
                We're proud to be your local window and door specialists, serving communities across the North East of England including:
              </p>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 mb-6 sm:mb-8">
                {[
                  'Newcastle upon Tyne', 'Durham', 'Sunderland', 'Gateshead',
                  'North Shields', 'South Shields', 'Hexham', 'Morpeth',
                  'Alnwick', 'Whitley Bay', 'Tynemouth', 'Chester-le-Street'
                ].map((area, index) => (
                  <div key={index} className="flex items-center py-1">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 sm:h-5 w-4 sm:w-5 text-[#ec4899] mr-2 sm:mr-3 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                    </svg>
                    <span className="text-gray-700 text-sm sm:text-base">{area}</span>
                  </div>
                ))}
              </div>
              
              <Link 
                href="/contact" 
                className="bg-gradient-to-r from-[#ec4899] to-[#d946ef] hover:from-[#d946ef] hover:to-[#ec4899] text-white font-medium px-5 sm:px-6 py-3 rounded-full inline-flex items-center transition-all duration-300 shadow-lg hover:shadow-xl text-sm sm:text-base"
              >
                <span>Check if we serve your area</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 sm:h-5 w-4 sm:w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </Link>
            </div>
            
            <div className="w-full lg:w-1/2 relative mt-8 lg:mt-0">
              <div className="relative rounded-xl overflow-hidden h-[300px] sm:h-[350px] md:h-[400px] shadow-xl img-zoom-container">
                <Image 
                  src="/images/old-brick-house-paved-street.jpg" 
                  alt="Map of our service areas in North East England" 
                  fill
                  className="object-cover img-zoom"
                  sizes="(max-width: 1024px) 100vw, 50vw"
                />
                <div className="absolute inset-0 bg-[#1a3a6d]/30"></div>
                
                <div className="absolute inset-0 flex items-center justify-center p-4">
                  <div className="glass-card p-3 sm:p-4 rounded-lg max-w-xs">
                    <h3 className="text-[#1a3a6d] font-bold text-lg sm:text-xl mb-2">Local Expertise</h3>
                    <p className="text-gray-700 text-xs sm:text-sm leading-relaxed">
                      As a North East based company, we understand the unique architectural styles and weather challenges of the region, providing solutions perfectly suited to local homes.
                    </p>
                  </div>
                </div>
              </div>
              
              {/* Floating badges - Mobile responsive */}
              <div className="absolute -top-3 sm:-top-5 -left-3 sm:-left-5 bg-[#ec4899] text-white font-bold py-1.5 sm:py-2 px-3 sm:px-4 rounded-full shadow-lg animate-float text-xs sm:text-sm">
                15+ Years Local Service
              </div>
              <div className="absolute -bottom-3 sm:-bottom-5 -right-3 sm:-right-5 bg-gradient-to-r from-[#d946ef] to-[#ec4899] text-white font-bold py-1.5 sm:py-2 px-3 sm:px-4 rounded-full shadow-lg animate-float-delayed text-xs sm:text-sm">
                5000+ Installations
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section - Mobile Optimized */}
      <section className="py-12 sm:py-16 md:py-20 lg:py-24 bg-white relative overflow-hidden">
        {/* Decorative elements - Mobile responsive */}
        <div className="absolute top-0 left-0 w-full h-full pointer-events-none">
          <div className="absolute top-0 right-0 w-48 sm:w-64 md:w-80 lg:w-96 h-48 sm:h-64 md:h-80 lg:h-96 bg-[#ec4899]/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 left-0 w-32 sm:w-48 md:w-64 h-32 sm:h-48 md:h-64 bg-[#ec4899]/5 rounded-full blur-3xl"></div>
          
          {/* Floating particles - responsive display */}
          <div className="hidden sm:block absolute top-1/4 left-1/3 w-6 sm:w-8 h-6 sm:h-8 rounded-full bg-[#ec4899]/10"></div>
          <div className="hidden sm:block absolute top-1/2 right-1/4 w-8 sm:w-10 md:w-12 h-8 sm:h-10 md:h-12 rounded-full bg-[#ec4899]/5"></div>
          <div className="hidden sm:block absolute bottom-1/3 left-1/4 w-7 sm:w-9 md:w-10 h-7 sm:h-9 md:h-10 rounded-full bg-[#ec4899]/10"></div>
        </div>
        
        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <span className="inline-block text-[#ec4899] font-medium mb-3 sm:mb-4 text-xs sm:text-sm md:text-base">GET IN TOUCH TODAY</span>
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 sm:mb-6 text-[#1a3a6d]">Ready to Transform Your Home?</h2>
            <p className="text-sm sm:text-base md:text-lg lg:text-xl text-gray-600 mb-6 sm:mb-8 md:mb-10 lg:mb-12 max-w-2xl mx-auto leading-relaxed">
              Join the thousands of satisfied homeowners across the North East who have trusted Window Warriors for their UPVC windows, doors and conservatories.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-3 sm:gap-4 md:gap-6">
              <Link href="/products-services#quote"
                className="bg-gradient-to-r from-[#ec4899] to-[#d946ef] text-white font-semibold px-5 sm:px-6 md:px-8 py-3 sm:py-4 rounded-full hover:shadow-lg hover:shadow-[#ec4899]/25 transition-all duration-300 text-center text-sm sm:text-base">
                Get Your Free Quote
              </Link>
              <Link href="/products-services" 
                className="border-2 border-[#ec4899] text-[#ec4899] font-semibold px-5 sm:px-6 md:px-8 py-3 sm:py-4 rounded-full hover:bg-[#ec4899] hover:text-white transition-all duration-300 text-center text-sm sm:text-base">
                Explore Our Services
              </Link>
            </div>
            
            {/* Trust badges - Mobile responsive grid */}
            <div className="mt-8 sm:mt-10 md:mt-12 lg:mt-16 grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 md:gap-6">
              <div className="bg-gray-50 p-3 sm:p-4 rounded-lg flex flex-col items-center border border-gray-100 hover:border-[#ec4899]/30 transition-colors duration-300">
                <svg className="w-6 sm:w-8 md:w-10 h-6 sm:h-8 md:h-10 text-[#ec4899] mb-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 12L11 14L15 10M20.618 5.984C20.846 6.887 20.846 7.897 20.846 9C20.846 14.5 17.346 18 11.846 18C6.346 18 2.846 14.5 2.846 9C2.846 3.5 6.346 0 11.846 0C12.949 0 13.959 0 14.862 0.228" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span className="text-xs sm:text-sm font-medium text-center text-gray-700 leading-tight">Free No-Obligation Quotes</span>
              </div>
              <div className="bg-gray-50 p-3 sm:p-4 rounded-lg flex flex-col items-center border border-gray-100 hover:border-[#ec4899]/30 transition-colors duration-300">
                <svg className="w-6 sm:w-8 md:w-10 h-6 sm:h-8 md:h-10 text-[#ec4899] mb-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M19.4 15C19.1277 15.8031 19.2292 16.6718 19.6727 17.4019C20.1162 18.132 20.8622 18.6376 21.7 18.8C20.5 21.4 18.5 22 16 22C12 22 11 20 7.00001 20C5.50001 20 4.00001 20.5 3.00001 22L2.50001 18.5C2.20001 16.5 3.40001 14 6.00001 14C8.00001 14 9.00001 16 12 16C14 16 14.5 15 19.4 15Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M12 7C9 7 7.00001 5 7.00001 3C7.00001 1.5 8.00001 1 8.00001 1C8.00001 1 9.50001 3 12 3C14.5 3 16 1 16 1C16 1 17 1.5 17 3C17 5 15 7 12 7Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span className="text-xs sm:text-sm font-medium text-center text-gray-700 leading-tight">Energy Efficiency Ratings</span>
              </div>
              <div className="bg-gray-50 p-3 sm:p-4 rounded-lg flex flex-col items-center border border-gray-100 hover:border-[#ec4899]/30 transition-colors duration-300">
                <svg className="w-6 sm:w-8 md:w-10 h-6 sm:h-8 md:h-10 text-[#ec4899] mb-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span className="text-xs sm:text-sm font-medium text-center text-gray-700 leading-tight">10 Year Guarantee</span>
              </div>
              <div className="bg-gray-50 p-3 sm:p-4 rounded-lg flex flex-col items-center border border-gray-100 hover:border-[#ec4899]/30 transition-colors duration-300">
                <svg className="w-6 sm:w-8 md:w-10 h-6 sm:h-8 md:h-10 text-[#ec4899] mb-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 12H15M9 16H15M17 21H7C5.89543 21 5 20.1046 5 19V5C5 3.89543 5.89543 3 7 3H12.5858C12.851 3 13.1054 3.10536 13.2929 3.29289L18.7071 8.70711C18.8946 8.89464 19 9.149 19 9.41421V19C19 20.1046 18.1046 21 17 21Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span className="text-xs sm:text-sm font-medium text-center text-gray-700 leading-tight">ASSURE Approved</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}

export default AboutPage; 