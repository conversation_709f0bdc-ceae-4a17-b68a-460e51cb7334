import Image from "next/image";
import Link from "next/link";

export const metadata = {
  title: "Window Styles for Period Properties: Maintaining Character | Window Warriors",
  description: "Learn how to upgrade your period property's windows while preserving its historical charm and character. Expert advice from Newcastle's trusted window specialists.",
  keywords: "period property windows, traditional window styles, sash windows, heritage windows, conservation windows, Newcastle windows, Window Warriors",
  icons: {
    icon: '/window-warriors-logo.png',
    apple: '/window-warriors-logo.png',
  },
  openGraph: {
    title: "Window Styles for Period Properties: Maintaining Character | Window Warriors",
    description: "Learn how to upgrade your period property's windows while preserving its historical charm and character.",
    url: 'https://windowwarriors.uk/blog/posts/window-styles-for-period-properties',
    siteName: 'Window Warriors',
    images: [
      {
        url: 'https://windowwarriors.uk/images/ancient-window-old-building-quebec-city.jpg',
        width: 1200,
        height: 630,
        alt: 'Period Property Windows - Window Warriors',
      },
    ],
    locale: 'en_GB',
    type: 'article',
  },
  twitter: {
    card: 'summary_large_image',
    title: "Window Styles for Period Properties: Maintaining Character | Window Warriors",
    description: "Learn how to upgrade your period property's windows while preserving its historical charm and character.",
    images: ['https://windowwarriors.uk/images/ancient-window-old-building-quebec-city.jpg'],
  },
  alternates: {
    canonical: 'https://windowwarriors.uk/blog/posts/window-styles-for-period-properties',
  },
};

export default function WindowStylesForPeriodPropertiesPage() {
  return (
    <main className="flex min-h-screen flex-col bg-white">
      {/* Hero Section - Following Main Website Pattern */}
      <section className="relative min-h-screen overflow-hidden bg-gradient-to-br from-[#1a3a6d] via-[#1a3a6d] to-[#ec4899]/20 pt-24 sm:pt-28 md:pt-32 lg:pt-36">
        {/* Background image with parallax effect */}
        <div className="absolute inset-0 bg-cover bg-center" style={{
          backgroundImage: 'url("/images/old-brick-house-paved-street.jpg")',
          backgroundAttachment: 'scroll',
          backgroundPosition: 'center 30%',
        }}>
          {/* Multi-layered pink background with floating elements */}
          <div className="absolute inset-0 bg-gradient-to-br from-[#ec4899]/95 via-[#d946ef]/85 to-[#be185d]/90"></div>
        </div>
        
        {/* Glass particles effect */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute top-[20%] left-[10%] w-2 h-2 bg-white/40 rounded-full animate-pulse"></div>
          <div className="absolute top-[60%] left-[20%] w-1 h-1 bg-white/60 rounded-full animate-pulse" style={{animationDelay: '1s'}}></div>
          <div className="absolute top-[40%] right-[15%] w-3 h-3 bg-white/30 rounded-full animate-pulse" style={{animationDelay: '2s'}}></div>
          <div className="absolute bottom-[30%] right-[25%] w-1.5 h-1.5 bg-white/50 rounded-full animate-pulse" style={{animationDelay: '0.5s'}}></div>
        </div>
        
        {/* Floating gradient orbs */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/4 left-1/4 w-32 sm:w-64 h-32 sm:h-64 bg-[#f9a8d4]/20 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-48 sm:w-96 h-48 sm:h-96 bg-[#ec4899]/10 rounded-full blur-3xl animate-pulse" style={{animationDelay: '2s'}}></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-20 sm:w-40 h-20 sm:h-40 bg-[#d946ef]/15 rounded-full blur-2xl animate-pulse" style={{animationDelay: '1s'}}></div>
        </div>
        
        {/* Geometric accent elements */}
        <div className="absolute inset-0 overflow-hidden opacity-20">
          <div className="absolute top-[15%] right-[10%] w-24 h-24 border border-white/30 rounded-lg rotate-45 animate-spin-slow"></div>
          <div className="absolute bottom-[20%] left-[8%] w-16 h-16 border border-white/20 rounded-full animate-bounce-subtle"></div>
          
          {/* Floating elements - Mobile friendly */}
          <div className="hidden md:block absolute top-[20%] right-[20%] w-24 h-24 circle-decoration opacity-40"></div>
          <div className="hidden md:block absolute bottom-[30%] left-[15%] w-16 h-16 circle-decoration opacity-30" style={{animationDelay: '1s'}}></div>
        </div>
        
        {/* Main Content */}
        <div className="container mx-auto px-4 sm:px-6 relative z-20 min-h-[60vh] flex flex-col justify-center py-16 sm:py-20">
          <div className="max-w-3xl animate-fade-in" style={{ animationDelay: '0.3s' }}>
            <span className="inline-block text-accessible-muted text-base sm:text-xl mb-3 sm:mb-4 border-b-2 border-[#f9a8d4]/60 pb-1 animate-fade-blur" style={{ animationDelay: '0.2s' }}>WINDOWS • OCTOBER 12, 2023</span>
            <h1 className="text-3xl sm:text-5xl md:text-6xl lg:text-7xl font-bold text-white mb-4 sm:mb-6">
              Window Styles for <span className="text-gradient-premium relative">
                Period Properties
                <span className="absolute bottom-0 left-0 w-full h-1 bg-[#f9a8d4]/70 rounded-full animate-shimmer"></span>
              </span>
            </h1>
            <p className="text-base sm:text-xl text-accessible-light mb-6 sm:mb-8 animate-slide-up" style={{ animationDelay: '0.6s' }}>
              Discover how to choose authentic window styles that enhance your period property while meeting modern performance standards
            </p>
            
            <div className="flex items-center space-x-4 text-accessible-light mt-5 animate-fade-in" style={{ animationDelay: '0.9s' }}>
              <div className="flex items-center">
                <Image 
                  src="/window-warriors-logo.png" 
                  alt="Window Warriors Author" 
                  width={40} 
                  height={40}
                  className="rounded-full border-2 border-white/30 shadow-sm"
                />
                <span className="ml-2.5 font-medium">Window Warriors Team</span>
              </div>
              <span className="text-accessible-subtle">•</span>
              <span className="flex items-center">
                <svg className="w-4 h-4 mr-1.5 text-[#f9a8d4]" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd"></path>
                </svg>
                10 min read
              </span>
            </div>
          </div>
          
          {/* Decorative elements - Enhance for mobile */}
          <div className="absolute bottom-8 sm:bottom-12 left-0 right-0 flex justify-center animate-bounce-slow">
            <svg width="30" height="30" className="sm:w-40 sm:h-40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 4V20M12 20L18 14M12 20L6 14" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>
        </div>
        
        {/* Modern scroll indicator */}
        <div className="absolute bottom-8 right-8 hidden lg:block">
          <div className="flex flex-col items-center space-y-2 text-accessible-subtle">
            <div className="w-px h-20 bg-gradient-to-b from-transparent via-white/40 to-transparent"></div>
            <div className="w-2 h-2 rounded-full bg-[#f9a8d4] animate-pulse"></div>
          </div>
        </div>
      </section>

      {/* Article Content */}
      <section className="py-12 md:py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl mx-auto">
            {/* Table of Contents */}
            <div className="bg-gray-50 rounded-xl p-6 mb-12 border border-gray-100 shadow-sm hover:shadow-md transition-shadow duration-300">
              <h2 className="text-lg font-semibold text-[#1a3a6d] mb-4 flex items-center">
                <svg className="w-5 h-5 mr-2 text-[#ec4899]" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z"></path>
                </svg>
                In this article
              </h2>
              <ul className="space-y-3">
                <li>
                  <a href="#introduction" className="text-[#1a3a6d] hover:text-[#ec4899] transition-colors flex items-center group">
                    <span className="w-6 h-6 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-3 group-hover:bg-[#ec4899]/20 transition-colors">1</span>
                    <span className="font-medium">Introduction</span>
                  </a>
                </li>
                <li>
                  <a href="#period-styles" className="text-[#1a3a6d] hover:text-[#ec4899] transition-colors flex items-center group">
                    <span className="w-6 h-6 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-3 group-hover:bg-[#ec4899]/20 transition-colors">2</span>
                    <span className="font-medium">Understanding Period Window Styles</span>
                  </a>
                </li>
                <li>
                  <a href="#authentic-replacements" className="text-[#1a3a6d] hover:text-[#ec4899] transition-colors flex items-center group">
                    <span className="w-6 h-6 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-3 group-hover:bg-[#ec4899]/20 transition-colors">3</span>
                    <span className="font-medium">Authentic Replacement Options</span>
                  </a>
                </li>
                <li>
                  <a href="#conservation-areas" className="text-[#1a3a6d] hover:text-[#ec4899] transition-colors flex items-center group">
                    <span className="w-6 h-6 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-3 group-hover:bg-[#ec4899]/20 transition-colors">4</span>
                    <span className="font-medium">Conservation Areas and Listed Buildings</span>
                  </a>
                </li>
                <li>
                  <a href="#energy-efficiency" className="text-[#1a3a6d] hover:text-[#ec4899] transition-colors flex items-center group">
                    <span className="w-6 h-6 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-3 group-hover:bg-[#ec4899]/20 transition-colors">5</span>
                    <span className="font-medium">Balancing Character and Energy Efficiency</span>
                  </a>
                </li>
                <li>
                  <a href="#conclusion" className="text-[#1a3a6d] hover:text-[#ec4899] transition-colors flex items-center group">
                    <span className="w-6 h-6 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-3 group-hover:bg-[#ec4899]/20 transition-colors">6</span>
                    <span className="font-medium">Conclusion & Recommendations</span>
                  </a>
                </li>
              </ul>
            </div>
            
            {/* Introduction */}
            <div id="introduction" className="mb-16 scroll-mt-24">
              <h2 className="text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-6 pb-2 border-b border-gray-200 flex items-center">
                <span className="w-8 h-8 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-3 text-sm">1</span>
                Introduction
              </h2>
              <div className="prose prose-lg max-w-none">
                <p className="text-xl md:text-2xl text-[#1a3a6d] font-medium mb-5 leading-relaxed">
                  Period properties hold a special place in Britain's architectural heritage, with their distinctive character and historical charm. Windows are arguably the most defining feature of these buildings, often referred to as the "eyes" of a property.
                </p>
                
                <p className="text-accessible-gray mb-6 leading-relaxed">
                  At Window Warriors, we understand the delicate balance between preserving a period property's authentic character and upgrading to meet modern standards of comfort, security, and energy efficiency. Across Newcastle and the North East, we've helped countless homeowners of Georgian, Victorian, Edwardian, and other period properties enhance their windows while maintaining their distinctive architectural integrity.
                </p>
                
                <div className="bg-blue-50 border-l-4 border-[#1a3a6d] p-5 my-8 rounded-r-xl">
                  <h3 className="text-[#1a3a6d] font-semibold text-lg mb-2">The Challenge of Period Windows</h3>
                  <p className="text-accessible-gray">Original windows in period properties often suffer from issues like drafts, poor insulation, noise penetration, and operational problems. However, replacing them requires careful consideration to avoid compromising the property's character or potentially breaching conservation area regulations.</p>
                </div>
              </div>
              
              <figure className="my-10 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300">
                <div className="relative h-[300px] md:h-[400px] overflow-hidden">
                  <Image
                    src="/images/ancient-window-old-building-quebec-city.jpg"
                    alt="Traditional sash windows in a period property"
                    fill
                    sizes="(max-width: 768px) 100vw, 800px"
                    className="object-cover w-full h-full transition-transform duration-700 hover:scale-105"
                  />
                </div>
                <figcaption className="bg-gray-50 text-accessible-gray text-sm p-4 italic border-t border-gray-100">
                  Traditional windows significantly contribute to a period property's distinctive character and curb appeal
                </figcaption>
              </figure>
            </div>

            {/* Understanding Period Window Styles */}
            <div id="period-styles" className="mb-16 scroll-mt-24">
              <h2 className="text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-6 pb-2 border-b border-gray-200 flex items-center">
                <span className="w-8 h-8 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-3 text-sm">2</span>
                Understanding Period Window Styles
              </h2>
              <div className="prose prose-lg max-w-none">
                <p className="text-accessible-gray mb-6 leading-relaxed">
                  To make informed decisions about period window replacements or restorations, it's essential to understand the distinctive styles that define different architectural eras. Here's a guide to the most common window styles found in period properties across Newcastle and the North East:
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-8 my-8">
                  <div className="bg-gray-50 rounded-xl p-6 border border-gray-100 shadow-sm hover:shadow-md transition-shadow duration-300">
                    <h3 className="text-xl font-bold text-[#1a3a6d] mb-4">Georgian (1714-1837)</h3>
                    <div className="relative h-40 overflow-hidden rounded-lg mb-4">
                      <Image
                        src="/images/ancient-window-old-building-quebec-city.jpg"
                        alt="Georgian sash windows"
                        fill
                        className="object-cover"
                      />
                    </div>
                    <ul className="text-accessible-gray space-y-2">
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#ec4899] mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                        </svg>
                        <span><strong>Sash windows</strong> with six-over-six pane configuration</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#ec4899] mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                        </svg>
                        <span>Thin, delicate glazing bars</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#ec4899] mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                        </svg>
                        <span>Proportionate design with windows becoming shorter on upper floors</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#ec4899] mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                        </svg>
                        <span>Common in Newcastle's Grainger Town and Jesmond areas</span>
                      </li>
                    </ul>
                  </div>
                  
                  <div className="bg-gray-50 rounded-xl p-6 border border-gray-100 shadow-sm hover:shadow-md transition-shadow duration-300">
                    <h3 className="text-xl font-bold text-[#1a3a6d] mb-4">Victorian (1837-1901)</h3>
                    <div className="relative h-40 overflow-hidden rounded-lg mb-4">
                      <Image
                        src="/images/old-brick-house-paved-street.jpg"
                        alt="Victorian bay windows"
                        fill
                        className="object-cover"
                      />
                    </div>
                    <ul className="text-accessible-gray space-y-2">
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#ec4899] mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                        </svg>
                        <span><strong>Sash windows</strong> with two-over-two pane configuration</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#ec4899] mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                        </svg>
                        <span>Introduction of bay windows</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#ec4899] mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                        </svg>
                        <span>Gothic influences with arched tops in some designs</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#ec4899] mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                        </svg>
                        <span>Prevalent in Heaton, Gosforth, and parts of Jesmond</span>
                      </li>
                    </ul>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-8 my-8">
                  <div className="bg-gray-50 rounded-xl p-6 border border-gray-100 shadow-sm hover:shadow-md transition-shadow duration-300">
                    <h3 className="text-xl font-bold text-[#1a3a6d] mb-4">Edwardian (1901-1910)</h3>
                    <div className="relative h-40 overflow-hidden rounded-lg mb-4">
                      <Image
                        src="/images/exterior-home.jpg"
                        alt="Edwardian casement windows"
                        fill
                        className="object-cover"
                      />
                    </div>
                    <ul className="text-accessible-gray space-y-2">
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#ec4899] mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                        </svg>
                        <span>Mix of <strong>sash and casement windows</strong></span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#ec4899] mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                        </svg>
                        <span>Simpler designs with fewer, larger panes</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#ec4899] mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                        </svg>
                        <span>Six-over-one or six-over-two configurations common</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#ec4899] mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                        </svg>
                        <span>Found throughout Newcastle suburbs</span>
                      </li>
                    </ul>
                  </div>
                  
                  <div className="bg-gray-50 rounded-xl p-6 border border-gray-100 shadow-sm hover:shadow-md transition-shadow duration-300">
                    <h3 className="text-xl font-bold text-[#1a3a6d] mb-4">Art Deco (1920s-1930s)</h3>
                    <div className="relative h-40 overflow-hidden rounded-lg mb-4">
                      <Image
                        src="/images/beautiful-view-blue-lake-captured-from-inside-villa.jpg"
                        alt="Art Deco windows"
                        fill
                        className="object-cover"
                      />
                    </div>
                    <ul className="text-accessible-gray space-y-2">
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#ec4899] mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                        </svg>
                        <span><strong>Steel-framed windows</strong> with horizontal emphasis</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#ec4899] mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                        </svg>
                        <span>Distinctive curved glass in some designs</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#ec4899] mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                        </svg>
                        <span>Geometric patterns and decorative elements</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#ec4899] mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                        </svg>
                        <span>Less common but found in specific parts of Newcastle</span>
                      </li>
                    </ul>
                  </div>
                </div>

                <p className="text-accessible-gray mb-6 leading-relaxed">
                  Identifying your property's architectural period is the first step in ensuring any window replacements remain true to its historical character. In Newcastle and the North East, we often see a mix of these styles, as many properties were built during transitional periods or have undergone modifications over the centuries.
                </p>
              </div>
            </div>

            {/* Authentic Replacement Options */}
            <div id="authentic-replacements" className="mb-16 scroll-mt-24">
              <h2 className="text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-6 pb-2 border-b border-gray-200 flex items-center">
                <span className="w-8 h-8 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-3 text-sm">3</span>
                Authentic Replacement Options
              </h2>
              <div className="prose prose-lg max-w-none">
                <p className="text-accessible-gray mb-6 leading-relaxed">
                  When it comes to replacing windows in period properties, homeowners typically have several options. The best choice depends on your property's specific requirements, conservation constraints, budget, and personal preferences:
                </p>

                <div className="bg-gray-50 rounded-xl p-6 border border-gray-100 my-8">
                  <h3 className="text-xl font-bold text-[#1a3a6d] mb-4">1. Like-for-Like Timber Replacements</h3>
                  
                  <div className="flex flex-col md:flex-row gap-6">
                    <div className="md:flex-1">
                      <p className="text-accessible-gray mb-4">
                        Timber windows crafted to match the original designs represent the most authentic replacement option for period properties. This approach is often required for listed buildings and strongly preferred in conservation areas.
                      </p>
                      
                      <div className="flex items-start mb-2">
                        <svg className="w-5 h-5 text-green-600 mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                        </svg>
                        <span className="text-accessible-gray"><strong>Pros:</strong> Perfect historical match, typically meets all conservation requirements, adds value to period properties, excellent lifespan when properly maintained</span>
                      </div>
                      
                      <div className="flex items-start">
                        <svg className="w-5 h-5 text-red-600 mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd"></path>
                        </svg>
                        <span className="text-accessible-gray"><strong>Cons:</strong> Higher initial cost, requires regular maintenance, potentially lower thermal efficiency without modifications</span>
                      </div>
                    </div>
                    
                    <div className="md:w-1/3">
                      <div className="relative h-48 overflow-hidden rounded-lg">
                        <Image 
                          src="/images/ancient-window-old-building-quebec-city.jpg"
                          alt="Traditional timber sash windows"
                          fill
                          className="object-cover"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 rounded-xl p-6 border border-gray-100 my-8">
                  <h3 className="text-xl font-bold text-[#1a3a6d] mb-4">2. Timber-Alternative UPVC Windows</h3>
                  
                  <div className="flex flex-col md:flex-row gap-6">
                    <div className="md:flex-1">
                      <p className="text-accessible-gray mb-4">
                        Modern UPVC windows designed specifically to replicate period timber windows offer a balance between authentic appearance and modern performance. These products have improved dramatically in recent years, with realistic woodgrain finishes and authentic detailing.
                      </p>
                      
                      <div className="flex items-start mb-2">
                        <svg className="w-5 h-5 text-green-600 mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                        </svg>
                        <span className="text-accessible-gray"><strong>Pros:</strong> Lower cost than timber, excellent thermal efficiency, minimal maintenance, increasingly acceptable visual match for non-listed buildings</span>
                      </div>
                      
                      <div className="flex items-start">
                        <svg className="w-5 h-5 text-red-600 mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd"></path>
                        </svg>
                        <span className="text-accessible-gray"><strong>Cons:</strong> Not acceptable for listed buildings, may be restricted in strict conservation areas, subtle differences in appearance compared to original windows</span>
                      </div>
                    </div>
                    
                    <div className="md:w-1/3">
                      <div className="relative h-48 overflow-hidden rounded-lg">
                        <Image 
                          src="/images/beautiful-view-blue-lake-captured-from-inside-villa.jpg"
                          alt="Heritage-style UPVC windows"
                          fill
                          className="object-cover"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 rounded-xl p-6 border border-gray-100 my-8">
                  <h3 className="text-xl font-bold text-[#1a3a6d] mb-4">3. Composite Windows</h3>
                  
                  <div className="flex flex-col md:flex-row gap-6">
                    <div className="md:flex-1">
                      <p className="text-accessible-gray mb-4">
                        Composite windows offer the best of both worlds: a real timber interior with an aluminum or weather-resistant exterior coating. These provide authentic appearance from both inside and outside while delivering excellent performance.
                      </p>
                      
                      <div className="flex items-start mb-2">
                        <svg className="w-5 h-5 text-green-600 mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                        </svg>
                        <span className="text-accessible-gray"><strong>Pros:</strong> Excellent thermal performance, authentic appearance, reduced maintenance compared to pure timber, potentially acceptable in some conservation areas</span>
                      </div>
                      
                      <div className="flex items-start">
                        <svg className="w-5 h-5 text-red-600 mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd"></path>
                        </svg>
                        <span className="text-accessible-gray"><strong>Cons:</strong> Higher cost than UPVC, still requires some maintenance, not suitable for all listed buildings</span>
                      </div>
                    </div>
                    
                    <div className="md:w-1/3">
                      <div className="relative h-48 overflow-hidden rounded-lg">
                        <Image 
                          src="/images/exterior-home.jpg"
                          alt="Composite windows in a period property"
                          fill
                          className="object-cover"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-blue-50 border-l-4 border-[#1a3a6d] p-5 my-8 rounded-r-xl">
                  <h3 className="text-[#1a3a6d] font-semibold text-lg mb-2">Window Warriors Expert Tip</h3>
                  <p className="text-accessible-gray">When replacing period windows, it's not just the frame material that matters. Pay close attention to details like glazing bar profiles, sightlines, opening mechanisms, and hardware. These subtle elements often have the most significant impact on maintaining your property's authentic character.</p>
                </div>
              </div>
            </div>

            {/* Conservation Areas and Listed Buildings */}
            <div id="conservation-areas" className="mb-16 scroll-mt-24">
              <h2 className="text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-6 pb-2 border-b border-gray-200 flex items-center">
                <span className="w-8 h-8 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-3 text-sm">4</span>
                Conservation Areas and Listed Buildings
              </h2>
              <div className="prose prose-lg max-w-none">
                <p className="text-accessible-gray mb-6 leading-relaxed">
                  Newcastle and the North East are home to numerous conservation areas and listed buildings. If your property falls into either category, there are specific considerations and often legal requirements regarding window replacements:
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 my-8">
                  <div className="bg-gray-50 rounded-xl p-6 border border-gray-100 shadow-sm hover:shadow-md transition-shadow duration-300">
                    <h3 className="text-xl font-bold text-[#1a3a6d] mb-4">Conservation Areas</h3>
                    
                    <p className="text-accessible-gray mb-4">
                      Properties within conservation areas may be subject to Article 4 Directions, which remove permitted development rights and require planning permission for window replacements.
                    </p>
                    
                    <ul className="text-accessible-gray space-y-3">
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#ec4899] mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                        </svg>
                        <span>Newcastle conservation areas include Jesmond Dene, Gosforth, Northumberland Gardens, and parts of the city center</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#ec4899] mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                        </svg>
                        <span>Planning officials typically prefer like-for-like replacements, but high-quality timber alternatives may be acceptable</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#ec4899] mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                        </svg>
                        <span>Planning applications should include detailed drawings of existing and proposed windows</span>
                      </li>
                    </ul>
                  </div>
                  
                  <div className="bg-gray-50 rounded-xl p-6 border border-gray-100 shadow-sm hover:shadow-md transition-shadow duration-300">
                    <h3 className="text-xl font-bold text-[#1a3a6d] mb-4">Listed Buildings</h3>
                    
                    <p className="text-accessible-gray mb-4">
                      Listed buildings have the strictest requirements, with any window replacements requiring Listed Building Consent.
                    </p>
                    
                    <ul className="text-accessible-gray space-y-3">
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#ec4899] mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                        </svg>
                        <span>Original windows should be retained and repaired wherever possible rather than replaced</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#ec4899] mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                        </svg>
                        <span>When replacement is necessary, exact like-for-like timber replacements are typically required</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="w-5 h-5 text-[#ec4899] mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                        </svg>
                        <span>Secondary glazing is often the preferred solution for improving thermal efficiency</span>
                      </li>
                    </ul>
                  </div>
                </div>

                <div className="my-8 p-6 bg-[#1a3a6d]/5 rounded-xl">
                  <h3 className="text-xl font-bold text-[#1a3a6d] mb-4">The Planning Process</h3>
                  
                  <div className="space-y-4">
                    <div className="flex">
                      <div className="flex-shrink-0 w-10 h-10 rounded-full bg-[#1a3a6d] text-white flex items-center justify-center font-bold mr-4">1</div>
                      <div>
                        <h4 className="font-bold text-[#1a3a6d] mb-1">Check Your Property's Status</h4>
                        <p className="text-accessible-gray">Confirm whether your property is listed or in a conservation area by checking with Newcastle City Council or your local authority</p>
                      </div>
                    </div>
                    
                    <div className="flex">
                      <div className="flex-shrink-0 w-10 h-10 rounded-full bg-[#1a3a6d] text-white flex items-center justify-center font-bold mr-4">2</div>
                      <div>
                        <h4 className="font-bold text-[#1a3a6d] mb-1">Pre-Application Consultation</h4>
                        <p className="text-accessible-gray">Consider a pre-application consultation with conservation officers to understand specific requirements for your property</p>
                      </div>
                    </div>
                    
                    <div className="flex">
                      <div className="flex-shrink-0 w-10 h-10 rounded-full bg-[#1a3a6d] text-white flex items-center justify-center font-bold mr-4">3</div>
                      <div>
                        <h4 className="font-bold text-[#1a3a6d] mb-1">Detailed Application</h4>
                        <p className="text-accessible-gray">Submit a detailed application including window specifications, materials, and technical drawings</p>
                      </div>
                    </div>
                    
                    <div className="flex">
                      <div className="flex-shrink-0 w-10 h-10 rounded-full bg-[#1a3a6d] text-white flex items-center justify-center font-bold mr-4">4</div>
                      <div>
                        <h4 className="font-bold text-[#1a3a6d] mb-1">Expert Installation</h4>
                        <p className="text-accessible-gray">Once approved, ensure installation by specialists experienced with period properties and conservation requirements</p>
                      </div>
                    </div>
                  </div>
                </div>

                <figure className="my-10 rounded-xl overflow-hidden shadow-md">
                  <div className="relative h-[300px] overflow-hidden">
                    <Image
                      src="/images/old-brick-house-paved-street.jpg"
                      alt="Period property in a Newcastle conservation area"
                      fill
                      sizes="(max-width: 768px) 100vw, 800px"
                      className="object-cover w-full h-full transition-transform duration-700 hover:scale-105"
                    />
                  </div>
                  <figcaption className="bg-gray-50 text-accessible-gray text-sm p-4 italic border-t border-gray-100">
                    Properties in conservation areas must maintain their historical character with appropriate window styles
                  </figcaption>
                </figure>
              </div>
            </div>

            {/* Balancing Character and Energy Efficiency */}
            <div id="energy-efficiency" className="mb-16 scroll-mt-24">
              <h2 className="text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-6 pb-2 border-b border-gray-200 flex items-center">
                <span className="w-8 h-8 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-3 text-sm">5</span>
                Balancing Character and Energy Efficiency
              </h2>
              <div className="prose prose-lg max-w-none">
                <p className="text-accessible-gray mb-6 leading-relaxed">
                  One of the biggest challenges when upgrading period windows is balancing historical authenticity with modern energy efficiency requirements. Here are effective strategies to achieve both goals:
                </p>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 my-8">
                  <div className="bg-gray-50 rounded-xl p-6 border border-gray-100 shadow-sm hover:shadow-md transition-all duration-300">
                    <div className="w-12 h-12 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mb-4">
                      <svg className="w-6 h-6 text-[#1a3a6d]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-bold text-[#1a3a6d] mb-2">Secondary Glazing</h3>
                    <p className="text-accessible-gray">
                      Installing a discrete secondary glazing system behind original windows provides excellent thermal and acoustic insulation while preserving the external appearance. This is particularly suitable for listed buildings.
                    </p>
                  </div>
                  
                  <div className="bg-gray-50 rounded-xl p-6 border border-gray-100 shadow-sm hover:shadow-md transition-all duration-300">
                    <div className="w-12 h-12 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mb-4">
                      <svg className="w-6 h-6 text-[#1a3a6d]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-bold text-[#1a3a6d] mb-2">Slim Double Glazing</h3>
                    <p className="text-accessible-gray">
                      Modern slim-profile double glazing units (10-14mm) can often be fitted into existing timber frames or replica frames, providing improved insulation while maintaining authentic proportions.
                    </p>
                  </div>
                  
                  <div className="bg-gray-50 rounded-xl p-6 border border-gray-100 shadow-sm hover:shadow-md transition-all duration-300">
                    <div className="w-12 h-12 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mb-4">
                      <svg className="w-6 h-6 text-[#1a3a6d]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-bold text-[#1a3a6d] mb-2">Draught-Proofing</h3>
                    <p className="text-accessible-gray">
                      Professional draught-proofing of existing windows can significantly improve thermal efficiency while being completely invisible, making it ideal for listed buildings.
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 my-8">
                  <div className="bg-gray-50 rounded-xl p-6 border border-gray-100 shadow-sm hover:shadow-md transition-all duration-300">
                    <div className="w-12 h-12 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mb-4">
                      <svg className="w-6 h-6 text-[#1a3a6d]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-bold text-[#1a3a6d] mb-2">Thermal Blinds and Curtains</h3>
                    <p className="text-accessible-gray">
                      High-quality thermal blinds or lined curtains can significantly reduce heat loss through windows while maintaining period aesthetics. These can be particularly effective when combined with other measures.
                    </p>
                  </div>
                  
                  <div className="bg-gray-50 rounded-xl p-6 border border-gray-100 shadow-sm hover:shadow-md transition-all duration-300">
                    <div className="w-12 h-12 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mb-4">
                      <svg className="w-6 h-6 text-[#1a3a6d]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-bold text-[#1a3a6d] mb-2">Low-E Glass</h3>
                    <p className="text-accessible-gray">
                      Low-emissivity glass can be incorporated into traditional window designs to improve thermal performance. This special coating reflects heat back into the room while allowing sunlight to pass through.
                    </p>
                  </div>
                </div>

                <div className="bg-[#1a3a6d]/5 p-8 rounded-xl my-10">
                  <h3 className="text-xl font-bold text-[#1a3a6d] mb-4">Case Study: Victorian Townhouse in Jesmond</h3>
                  
                  <div className="flex flex-col md:flex-row gap-6">
                    <div className="md:w-1/3">
                      <div className="relative h-48 md:h-full overflow-hidden rounded-lg">
                        <Image 
                          src="/images/old-brick-house-paved-street.jpg"
                          alt="Victorian townhouse with restored sash windows"
                          fill
                          className="object-cover"
                        />
                      </div>
                    </div>
                    
                    <div className="md:w-2/3">
                      <p className="text-accessible-gray mb-4">
                        For a Victorian townhouse in Jesmond conservation area, we recently implemented a comprehensive window upgrade that balanced heritage requirements with modern energy efficiency needs:
                      </p>
                      
                      <ul className="text-accessible-gray space-y-2">
                        <li className="flex items-start">
                          <svg className="w-5 h-5 text-[#ec4899] mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                          </svg>
                          <span>Retained and restored original box sash frames where possible</span>
                        </li>
                        <li className="flex items-start">
                          <svg className="w-5 h-5 text-[#ec4899] mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                          </svg>
                          <span>Replaced damaged sashes with like-for-like timber replicas</span>
                        </li>
                        <li className="flex items-start">
                          <svg className="w-5 h-5 text-[#ec4899] mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                          </svg>
                          <span>Installed 12mm slim-profile double glazing with period-appropriate glazing bars</span>
                        </li>
                        <li className="flex items-start">
                          <svg className="w-5 h-5 text-[#ec4899] mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                          </svg>
                          <span>Applied comprehensive draught-proofing throughout</span>
                        </li>
                        <li className="flex items-start">
                          <svg className="w-5 h-5 text-[#ec4899] mr-2 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                          </svg>
                          <span>Installed secondary glazing on street-facing windows for noise reduction</span>
                        </li>
                      </ul>
                      
                      <p className="text-accessible-gray mt-4">
                        The result: A 40% reduction in heating costs while maintaining complete historical authenticity that satisfied conservation area requirements.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Conclusion & Recommendations */}
            <div id="conclusion" className="mb-16 scroll-mt-24">
              <h2 className="text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-6 pb-2 border-b border-gray-200 flex items-center">
                <span className="w-8 h-8 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-3 text-sm">6</span>
                Conclusion & Recommendations
              </h2>
              <div className="prose prose-lg max-w-none">
                <p className="text-accessible-gray mb-6 leading-relaxed">
                  Upgrading windows in period properties requires a careful balance between preserving architectural character and incorporating modern performance standards. By understanding the specific historical features of your property and working with specialists who appreciate both heritage requirements and contemporary techniques, you can achieve excellent results.
                </p>

                <div className="bg-blue-50 border-l-4 border-[#1a3a6d] p-5 my-8 rounded-r-xl">
                  <h3 className="text-[#1a3a6d] font-semibold text-lg mb-2">Our Recommendations</h3>
                  <ul className="text-accessible-gray list-disc pl-5 space-y-2">
                    <li>Begin by researching your property's historical period and identifying the authentic window styles</li>
                    <li>Check if your property is in a conservation area or is listed – this will determine what options are available</li>
                    <li>Consider restoration over replacement where original windows can be salvaged</li>
                    <li>For replacements, choose the highest quality you can afford – remember, these are long-term investments</li>
                    <li>Work with specialists experienced in period properties – generic window installers often lack the necessary skills</li>
                    <li>Request detailed specifications and samples before committing to any work</li>
                  </ul>
                </div>
                
                <p className="text-accessible-gray mb-6 leading-relaxed">
                  At Window Warriors, we specialize in sensitive window replacements for period properties across Newcastle and the North East. Our team combines traditional craftsmanship with modern techniques to deliver windows that honor your property's heritage while providing contemporary performance.
                </p>
                
                <p className="text-accessible-gray mb-6 leading-relaxed">
                  For personalized advice on your period property's windows, contact our heritage window specialists for a consultation.
                </p>
              </div>
            </div>

            {/* Call to Action Section */}
            <div className="bg-gradient-to-br from-[#f8f9fa] to-[#e9f0f8] border border-gray-100 rounded-xl p-8 sm:p-10 my-12 shadow-md hover:shadow-lg transition-shadow duration-300">
              <h2 className="text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-5">
                Get Expert Advice for Your Period Property
              </h2>
              <p className="text-accessible-gray mb-6 leading-relaxed">
                Looking to enhance your period property's windows while preserving its character? Our window specialists can provide personalized advice based on your property's architectural style, conservation requirements, and your specific needs.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link 
                  href="/contact"
                  className="bg-[#1a3a6d] hover:bg-[#0f2d5c] text-white font-medium py-3.5 px-6 rounded-lg text-center shadow-sm hover:shadow transition-all duration-300 flex items-center justify-center"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                  </svg>
                  Request a Free Consultation
                </Link>
                <Link
                  href="/gallery"
                  className="bg-white hover:bg-gray-50 text-[#1a3a6d] border border-[#1a3a6d] font-medium py-3.5 px-6 rounded-lg text-center shadow-sm hover:shadow transition-all duration-300 flex items-center justify-center"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                  View Our Period Property Projects
                </Link>
              </div>
            </div>
            
            {/* Author Section */}
            <div className="bg-gray-50 rounded-xl p-6 border border-gray-100 shadow-sm mb-12 hover:shadow transition-shadow duration-300">
              <div className="flex items-start space-x-5">
                <Image 
                  src="/window-warriors-logo.png" 
                  alt="Sarah Thompson" 
                  width={70} 
                  height={70}
                  className="rounded-full border-2 border-gray-200 shadow-sm"
                />
                <div>
                  <h3 className="font-semibold text-lg text-[#1a3a6d] mb-2">Sarah T.</h3>
                  <p className="text-accessible-gray text-base leading-relaxed">Heritage window specialist with over 15 years of experience in restoring and replacing windows in period properties. Sarah has worked on numerous conservation projects across Newcastle and the North East.</p>
                </div>
              </div>
            </div>
            
            {/* Related Articles */}
            <div className="mb-16">
              <div className="flex items-center mb-8">
                <div className="h-px flex-grow bg-gradient-to-r from-gray-200 to-transparent"></div>
                <h3 className="text-xl font-bold text-[#1a3a6d] px-4">Related Articles</h3>
                <div className="h-px flex-grow bg-gradient-to-l from-gray-200 to-transparent"></div>
              </div>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                {/* Related Article 1 */}
                <div className="group relative rounded-xl shadow-sm hover:shadow-md overflow-hidden transition-all duration-300 border border-gray-100 hover:border-gray-200 hover:-translate-y-1">
                  <div className="relative h-48 overflow-hidden">
                    <Image 
                      src="/images/beautiful-view-blue-lake-captured-from-inside-villa.jpg" 
                      alt="5 Benefits of UPVC Windows You Need to Know" 
                      fill 
                      className="object-cover group-hover:scale-105 transition-transform duration-700"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-[#1a3a6d]/80 to-transparent opacity-70"></div>
                    <div className="absolute bottom-0 left-0 p-4">
                      <span className="inline-block py-1 px-2 bg-[#ec4899] text-white text-xs font-medium rounded-md mb-2">
                        WINDOWS
                      </span>
                      <h4 className="text-white font-bold text-lg leading-tight">5 Benefits of UPVC Windows You Need to Know</h4>
                    </div>
                  </div>
                  <div className="p-4">
                    <p className="text-accessible-gray text-sm mb-4 line-clamp-3">
                      Discover why UPVC windows are the popular choice for homeowners looking for energy efficiency, durability, and style.
                    </p>
                    <Link 
                      href="/blog/posts/benefits-of-upvc-windows" 
                      className="text-[#1a3a6d] font-medium text-sm hover:text-[#ec4899] transition-colors inline-flex items-center"
                    >
                      Read Article
                      <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                      </svg>
                    </Link>
                  </div>
                </div>
                
                {/* Related Article 2 */}
                <div className="group relative rounded-xl shadow-sm hover:shadow-md overflow-hidden transition-all duration-300 border border-gray-100 hover:border-gray-200 hover:-translate-y-1">
                  <div className="relative h-48 overflow-hidden">
                    <Image 
                      src="/images/beautiful-view-blue-lake-captured-from-inside-villa.jpg" 
                      alt="Understanding Window Energy Ratings: A Buyer's Guide" 
                      fill 
                      className="object-cover group-hover:scale-105 transition-transform duration-700"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-[#1a3a6d]/80 to-transparent opacity-70"></div>
                    <div className="absolute bottom-0 left-0 p-4">
                      <span className="inline-block py-1 px-2 bg-[#ec4899] text-white text-xs font-medium rounded-md mb-2">
                        ENERGY SAVING
                      </span>
                      <h4 className="text-white font-bold text-lg leading-tight">Understanding Window Energy Ratings: A Buyer's Guide</h4>
                    </div>
                  </div>
                  <div className="p-4">
                    <p className="text-accessible-gray text-sm mb-4 line-clamp-3">
                      Decode window energy ratings and learn what they mean for your home's efficiency and comfort.
                    </p>
                    <Link 
                      href="/blog/posts/understanding-window-energy-ratings" 
                      className="text-[#1a3a6d] font-medium text-sm hover:text-[#ec4899] transition-colors inline-flex items-center"
                    >
                      Read Article
                      <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                      </svg>
                    </Link>
                  </div>
                </div>
              </div>
              
              <div className="flex justify-center mt-8">
                <Link 
                  href="/blog" 
                  className="bg-white hover:bg-gray-50 text-[#1a3a6d] border border-[#1a3a6d]/30 font-medium py-2.5 px-5 rounded-lg transition-colors inline-flex items-center"
                >
                  View All Articles
                  <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
} 


