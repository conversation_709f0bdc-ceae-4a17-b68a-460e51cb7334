'use client';

import { useEffect } from 'react';

const SalesCalendar = () => {
  
  useEffect(() => {
    // Load the Go High Level form embed script
    const script = document.createElement('script');
    script.src = 'https://link.msgsndr.com/js/form_embed.js';
    script.type = 'text/javascript';
    script.async = true;
    document.body.appendChild(script);
    
    return () => {
      // Clean up
      try {
        document.body.removeChild(script);
      } catch (e) {
        console.log('Error removing script:', e);
      }
    };
  }, []);

  return (
    <div className="w-full">
      <div className="bg-gray-100 p-4 rounded-lg mb-6 border border-gray-200">
        <div className="flex items-center mb-2">
          <svg className="w-5 h-5 text-[#ec4899] mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h4 className="text-sm sm:text-base font-medium text-[#1a3a6d]">Sales Rep Calendar</h4>
        </div>
        <p className="text-sm sm:text-base text-gray-700">
          This calendar is for sales representatives to book appointments with clients during home visits or follow-up meetings.
        </p>
      </div>
      
      {/* Calendar container with your sales calendar embed code */}
      <div className="rounded-lg overflow-hidden bg-white min-h-[600px]">
        <div 
          dangerouslySetInnerHTML={{ 
            __html: `
              <iframe 
                src="https://api.leadconnectorhq.com/widget/booking/mDigyG1BeQOYTm7nifJR" 
                style="width: 100%;border:none;overflow: hidden;min-height: 600px;" 
                scrolling="no" 
                id="mDigyG1BeQOYTm7nifJR_1753826218971">
              </iframe>
              <script src="https://link.msgsndr.com/js/form_embed.js" type="text/javascript"></script>
            `
          }} 
        />
      </div>
      
      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-start">
          <svg className="w-5 h-5 text-blue-600 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div>
            <h5 className="text-sm font-medium text-blue-800 mb-1">For Sales Team Use Only</h5>
            <p className="text-sm text-blue-700">
              This calendar is designed for internal use by Window Warriors sales representatives. 
              Use this when meeting with clients to schedule follow-up appointments, installations, or consultations.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SalesCalendar;
