import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const formData = await request.json();
    
    // Split name into first name and last name - exactly like QuoteForm
    const firstName = formData.name.split(' ')[0] || '';
    const lastName = formData.name.split(' ').slice(1).join(' ') || '';
    
    // Create contact payload for GHL API - exactly matching QuoteForm structure
    const ghlPayload = {
      email: formData.email,
      phone: formData.phone,
      firstName: firstName,
      lastName: lastName,
      locationId: "19udETR4MIdmB0qYX3sD",
      // Include customField exactly as in QuoteForm
      customField: {
        subject: formData.subject,
        message: formData.message,
        service_interest: formData.serviceInterest
      },
      // Add the service interest as a tag in addition to get-free-quote
      tags: ["get-free-quote", formData.serviceInterest ? `service-${formData.serviceInterest.toLowerCase().replace(/\s+/g, '-')}` : null].filter(Boolean)
    };
    
    console.log('Submitting to GHL with service tags:', ghlPayload);
    
    // Use the exact same endpoint and headers as QuoteForm
    const response = await fetch('https://rest.gohighlevel.com/v1/contacts/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2NhdGlvbl9pZCI6IjE5dWRFVFI0TUlkbUIwcVlYM3NEIiwidmVyc2lvbiI6MSwiaWF0IjoxNzQ0MjAxNzAwNDE2LCJzdWIiOiJWT2U0M2FRaXB0OFRRd1Y3OXE0QSJ9.Q0EGpAtCYyAmNgV_6OwLP3f4O24C6JQwjYoXP4QWMZ0'
      },
      body: JSON.stringify(ghlPayload)
    });
    
    const responseText = await response.text();
    console.log('GHL API Raw Response:', responseText);
    
    if (!response.ok) {
      console.error('GoHighLevel API error response:', response.status, responseText);
      return NextResponse.json(
        { error: `GoHighLevel API error: ${response.status}`, details: responseText }, 
        { status: response.status }
      );
    }
    
    // Parse the response only if it contains valid JSON
    let data;
    try {
      data = JSON.parse(responseText);
    } catch (e) {
      console.log('Response is not JSON, using text response');
      data = { message: 'Success', text: responseText };
    }
    
    return NextResponse.json({ success: true, data });
  } catch (error) {
    console.error('Error in contact API route:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : String(error) }, 
      { status: 500 }
    );
  }
} 