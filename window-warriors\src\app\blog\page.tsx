import type { Metadata } from "next";
import Link from "next/link";
import Image from "next/image";
import dynamic from 'next/dynamic';

// Dynamically import BlogContent with loading fallback for better performance
const BlogContent = dynamic(() => import("@/components/BlogContent"), {
  loading: () => (
    <div className="py-12 sm:py-16 md:py-20 flex justify-center items-center">
      <div className="animate-pulse flex flex-col items-center">
        <div className="rounded-md bg-slate-200 h-4 sm:h-6 w-16 sm:w-24 mb-3 sm:mb-4"></div>
        <div className="h-24 w-24 sm:h-32 sm:w-32 rounded-full bg-slate-200"></div>
      </div>
    </div>
  ),
  ssr: true,
});

export const metadata: Metadata = {
  title: "Blog | Window Warriors - UPVC Windows & Doors in Newcastle",
  description: "Read our latest articles about UPVC windows, doors, energy efficiency, and home improvement tips from Window Warriors, Newcastle's trusted window and door specialists.",
  keywords: "window warriors blog, upvc windows blog, door installation tips, energy efficiency, home improvement, Newcastle windows blog",
  icons: {
    icon: '/window-warriors-logo.png',
    apple: '/window-warriors-logo.png',
    shortcut: '/window-warriors-logo.png',
  },
  openGraph: {
    title: "Window Warriors Blog | UPVC Windows & Doors Insights",
    description: "Expert articles on UPVC windows, doors, energy efficiency, and home improvement tips from Window Warriors, Newcastle's trusted window specialists.",
    url: 'https://windowwarriors.uk/blog',
    siteName: 'Window Warriors',
    images: [
      {
        url: 'https://windowwarriors.uk/images/beautiful-view-blue-lake-captured-from-inside-villa.jpg',
        width: 1200,
        height: 630,
        alt: 'Window Warriors Blog - UPVC Windows & Doors',
      },
    ],
    locale: 'en_GB',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "Window Warriors Blog | UPVC Windows & Doors Insights",
    description: "Expert articles and tips about UPVC windows, doors and energy efficiency from Newcastle's trusted window specialists.",
    images: ['https://windowwarriors.uk/images/beautiful-view-blue-lake-captured-from-inside-villa.jpg'],
  },
  alternates: {
    canonical: 'https://windowwarriors.uk/blog',
  },
};

export default function Blog() {
  return (
    <main className="flex min-h-screen flex-col">
      {/* Enhanced Hero Section */}
      <section className="relative min-h-[500px] sm:min-h-[600px] lg:min-h-screen pt-20 sm:pt-24 md:pt-28 lg:pt-32 pb-12 sm:pb-16 md:pb-20 lg:pb-24 overflow-hidden">
        {/* Optimized background image */}
        <div className="absolute inset-0">
          <Image
            src="/images/optimized/ancient-window-old-building-quebec-city.webp"
            alt="Beautiful window architecture and design inspiration"
            fill
            priority
            className="object-cover object-center"
            sizes="(max-width: 768px) 100vw, 100vw"
            quality={80}
            placeholder="blur"
            blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
          />
          {/* Enhanced gradient overlay with pink accent */}
          <div className="absolute inset-0 bg-gradient-to-br from-[#1a3a6d]/90 via-[#1a3a6d]/80 to-[#ec4899]/30"></div>
          <div className="absolute inset-0 bg-gradient-to-t from-[#0f172a]/60 via-transparent to-transparent"></div>
        </div>
        
        {/* Simplified decorative elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/6 left-1/6 w-32 sm:w-48 md:w-64 lg:w-80 h-32 sm:h-48 md:h-64 lg:h-80 bg-[#ec4899]/20 rounded-full blur-3xl animate-float-slow"></div>
          <div className="absolute bottom-1/6 right-1/6 w-40 sm:w-64 md:w-80 lg:w-[30rem] h-40 sm:h-64 md:h-80 lg:h-[30rem] bg-[#d946ef]/15 rounded-full blur-3xl animate-float-slow" style={{ animationDelay: '2s' }}></div>
        </div>
        
        {/* Enhanced Hero Content */}
        <div className="container mx-auto px-4 sm:px-6 relative z-10 flex flex-col justify-center min-h-[400px] sm:min-h-[500px] lg:min-h-[calc(100vh-8rem)]">
          <div className="max-w-4xl animate-fade-in" style={{ animationDelay: '0.3s' }}>
            {/* Enhanced brand badge */}
            <div className="inline-flex items-center mb-4 sm:mb-6 md:mb-8">
              <div className="bg-white/10 backdrop-blur-sm border border-[#ec4899]/30 rounded-full px-4 sm:px-6 py-1.5 sm:py-2 animate-fade-blur" style={{ animationDelay: '0.2s' }}>
                <span className="text-accessible-light text-xs sm:text-sm md:text-base font-medium">OUR BLOG</span>
                <div className="w-full h-px bg-gradient-to-r from-transparent via-[#ec4899]/70 to-transparent mt-1"></div>
              </div>
            </div>
            
            {/* Enhanced title with pink gradients - Mobile optimized */}
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl 2xl:text-8xl font-bold text-white mb-3 sm:mb-4 md:mb-6 leading-tight">
              Insights & <span className="relative inline-block">
                <span className="text-gradient-logo">Tips</span>
                <div className="absolute -bottom-1 sm:-bottom-2 left-0 w-full h-0.5 sm:h-1 bg-gradient-to-r from-[#ec4899] to-[#d946ef] rounded-full animate-shimmer"></div>
                <div className="absolute -top-0.5 sm:-top-1 -right-0.5 sm:-right-1 w-2 sm:w-3 h-2 sm:h-3 bg-[#ec4899]/60 rounded-full animate-pulse"></div>
              </span>
            </h1>
            
            {/* Enhanced description - Mobile responsive */}
            <div className="relative mb-4 sm:mb-6 md:mb-8">
              <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-accessible-light leading-relaxed animate-slide-up" style={{ animationDelay: '0.6s' }}>
                Expert advice on windows, doors, and home improvement from Newcastle's trusted specialists
              </p>
              <div className="absolute -left-2 sm:-left-4 top-0 w-0.5 sm:w-1 h-full bg-gradient-to-b from-[#ec4899] to-transparent rounded-full opacity-60"></div>
            </div>
            
            {/* Enhanced popular tags - Mobile responsive */}
            <div className="animate-fade-in" style={{ animationDelay: '1.2s' }}>
              <p className="text-white/70 text-xs sm:text-sm md:text-base mb-3 sm:mb-4">Popular topics</p>
              <div className="flex flex-wrap gap-2 sm:gap-3">
                <Link href="/blog?category=Windows" className="bg-white/10 backdrop-blur-sm border border-white/20 text-white hover:bg-[#ec4899] hover:border-[#ec4899] px-3 sm:px-4 py-1.5 sm:py-2 rounded-full text-xs sm:text-sm transition-all duration-300">
                  Windows
                </Link>
                <Link href="/blog?category=Energy+Saving" className="bg-white/10 backdrop-blur-sm border border-white/20 text-white hover:bg-[#ec4899] hover:border-[#ec4899] px-3 sm:px-4 py-1.5 sm:py-2 rounded-full text-xs sm:text-sm transition-all duration-300">
                  Energy Saving
                </Link>
                <Link href="/blog?category=Doors" className="bg-white/10 backdrop-blur-sm border border-white/20 text-white hover:bg-[#ec4899] hover:border-[#ec4899] px-3 sm:px-4 py-1.5 sm:py-2 rounded-full text-xs sm:text-sm transition-all duration-300">
                  Doors
                </Link>
                <Link href="/blog?tag=UPVC" className="bg-white/10 backdrop-blur-sm border border-white/20 text-white hover:bg-[#ec4899] hover:border-[#ec4899] px-3 sm:px-4 py-1.5 sm:py-2 rounded-full text-xs sm:text-sm transition-all duration-300">
                  UPVC
                </Link>
              </div>
            </div>
          </div>
          
          {/* Modern scroll indicator - Mobile responsive */}
          <div className="mt-auto pt-6 sm:pt-8 flex justify-center animate-bounce-slow">
            <div className="flex flex-col items-center">
              <div className="w-5 sm:w-6 h-8 sm:h-10 border-2 border-white/30 rounded-full flex justify-center">
                <div className="w-0.5 sm:w-1 h-2 sm:h-3 bg-white/60 rounded-full mt-1.5 sm:mt-2 animate-pulse"></div>
              </div>
              <span className="text-accessible-subtle text-xs mt-1 sm:mt-2 hidden sm:block">Read articles</span>
            </div>
          </div>
        </div>
      </section>

      <BlogContent />
    </main>
  );
} 