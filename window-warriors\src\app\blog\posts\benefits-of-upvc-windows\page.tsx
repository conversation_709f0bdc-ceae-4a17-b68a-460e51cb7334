import Image from "next/image";
import Link from "next/link";

export const metadata = {
  title: "5 Benefits of UPVC Windows You Need to Know | Window Warriors",
  description: "Discover why UPVC windows are the popular choice for homeowners looking for energy efficiency, durability, and style. Learn how they can transform your Newcastle home.",
  keywords: "UPVC windows, energy efficient windows, window installation Newcastle, durable windows, window styles, Window Warriors",
  icons: {
    icon: '/window-warriors-logo.png',
    apple: '/window-warriors-logo.png',
  },
  openGraph: {
    title: "5 Benefits of UPVC Windows You Need to Know | Window Warriors",
    description: "Discover why UPVC windows are the popular choice for homeowners looking for energy efficiency, durability, and style.",
    url: 'https://windowwarriors.uk/blog/posts/benefits-of-upvc-windows',
    siteName: 'Window Warriors',
    images: [
      {
        url: 'https://windowwarriors.uk/images/beautiful-view-blue-lake-captured-from-inside-villa.jpg',
        width: 1200,
        height: 630,
        alt: 'Benefits of UPVC Windows - Window Warriors',
      },
    ],
    locale: 'en_GB',
    type: 'article',
  },
  twitter: {
    card: 'summary_large_image',
    title: "5 Benefits of UPVC Windows You Need to Know | Window Warriors",
    description: "Discover why UPVC windows are the popular choice for homeowners looking for energy efficiency, durability, and style.",
    images: ['https://windowwarriors.uk/images/beautiful-view-blue-lake-captured-from-inside-villa.jpg'],
  },
  alternates: {
    canonical: 'https://windowwarriors.uk/blog/posts/benefits-of-upvc-windows',
  },
};

export default function BenefitsOfUpvcWindowsPage() {
  return (
    <div className="flex min-h-screen flex-col bg-white">
      {/* Hero Section - Following Main Website Pattern - Mobile Optimized */}
      <section className="relative min-h-[60vh] sm:min-h-[70vh] md:min-h-screen overflow-hidden bg-gradient-to-br from-[#1a3a6d] via-[#1a3a6d] to-[#ec4899]/20 pt-24 sm:pt-28 md:pt-32 lg:pt-36">
        {/* Background Image */}
        <div className="absolute inset-0 bg-cover bg-center" style={{
          backgroundImage: 'url("/images/beautiful-view-blue-lake-captured-from-inside-villa.jpg")',
          backgroundAttachment: 'scroll',
          backgroundPosition: 'center 30%',
        }}></div>
        
        {/* Multi-layered Background Overlays */}
        <div className="absolute inset-0">
          {/* Primary gradient background */}
          <div className="absolute inset-0 bg-gradient-to-br from-[#1a3a6d]/95 via-[#1a3a6d]/90 to-[#ec4899]/70"></div>
          
          {/* Animated gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-[#ec4899]/10 to-transparent animate-gradient-x"></div>
          
          {/* Noise texture */}
          <div className="absolute inset-0 opacity-[0.02] bg-noise"></div>
        </div>

        {/* Floating Pink Elements - Mobile Responsive */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {/* Large floating blurs - Mobile responsive sizing */}
          <div className="absolute top-1/4 left-1/4 w-32 sm:w-48 md:w-64 h-32 sm:h-48 md:h-64 bg-[#ec4899]/20 rounded-full blur-3xl animate-float-slow"></div>
          <div className="absolute top-1/3 right-1/3 w-24 sm:w-36 md:w-48 h-24 sm:h-36 md:h-48 bg-[#d946ef]/15 rounded-full blur-2xl animate-float-reverse"></div>
          <div className="absolute bottom-1/4 left-1/3 w-28 sm:w-42 md:w-56 h-28 sm:h-42 md:h-56 bg-[#f9a8d4]/10 rounded-full blur-3xl animate-float"></div>
          
          {/* Glass particles - Hidden on mobile */}
          <div className="hidden sm:block absolute top-[20%] right-[20%] w-6 sm:w-8 h-6 sm:h-8 bg-white/10 rounded-full backdrop-blur-sm animate-float-slow border border-white/20"></div>
          <div className="hidden sm:block absolute top-[60%] left-[15%] w-4 sm:w-6 h-4 sm:h-6 bg-[#ec4899]/20 rounded-full backdrop-blur-sm animate-float border border-[#ec4899]/30"></div>
          <div className="hidden md:block absolute top-[40%] right-[10%] w-3 sm:w-4 h-3 sm:h-4 bg-[#d946ef]/30 rounded-full animate-pulse"></div>
          <div className="hidden md:block absolute bottom-[40%] left-[20%] w-2 sm:w-3 h-2 sm:h-3 bg-white/30 rounded-full animate-ping"></div>
          
          {/* Geometric accents - Hidden on mobile */}
          <div className="hidden sm:block absolute top-[25%] left-[60%] w-8 sm:w-10 md:w-12 h-8 sm:h-10 md:h-12 border border-[#ec4899]/30 rotate-45 animate-spin-slow"></div>
          <div className="hidden sm:block absolute bottom-[35%] right-[25%] w-6 sm:w-8 h-6 sm:h-8 border-2 border-white/20 rounded-full animate-pulse"></div>
        </div>

        {/* Main Content - Mobile Optimized */}
        <div className="container mx-auto px-4 sm:px-6 relative z-20 min-h-[50vh] sm:min-h-[60vh] flex flex-col justify-center">
          <div className="max-w-4xl">
            {/* Brand Tag - Mobile responsive */}
            <div className="inline-flex items-center mb-4 sm:mb-6 px-3 sm:px-4 py-1.5 sm:py-2 rounded-full border border-[#ec4899]/30 bg-white/5 backdrop-blur-sm animate-fade-in">
              <div className="w-1.5 sm:w-2 h-1.5 sm:h-2 bg-[#ec4899] rounded-full mr-2 sm:mr-3 animate-pulse"></div>
              <span className="text-accessible-light text-xs sm:text-sm font-medium tracking-wider">WINDOWS • JUNE 15, 2023</span>
            </div>

            {/* Main Heading - Mobile responsive */}
            <h1 className="text-2xl sm:text-3xl md:text-5xl lg:text-6xl xl:text-7xl 2xl:text-8xl font-bold mb-4 sm:mb-6 animate-slide-up">
              <span className="text-white block mb-1 sm:mb-2">5 Benefits of</span>
              <span className="text-gradient-logo relative inline-block">
                UPVC Windows
                <div className="absolute -bottom-1 sm:-bottom-2 left-0 w-full h-0.5 sm:h-1 bg-gradient-to-r from-[#ec4899] to-[#d946ef] rounded-full animate-shimmer"></div>
              </span>
            </h1>

            {/* Description - Mobile responsive */}
            <p className="text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl text-accessible-muted mb-6 sm:mb-8 max-w-2xl leading-relaxed animate-fade-in" style={{ animationDelay: '0.3s' }}>
              Discover why UPVC windows are the popular choice for homeowners looking for energy efficiency, durability, and style
            </p>

            {/* Author Info - Mobile responsive */}
            <div className="flex items-center space-x-3 sm:space-x-4 text-accessible-light mb-6 sm:mb-8 animate-fade-in" style={{ animationDelay: '0.6s' }}>
              <div className="flex items-center">
                <Image 
                  src="/window-warriors-logo.png" 
                  alt="Window Warriors Author" 
                  width={32} 
                  height={32}
                  className="sm:w-10 sm:h-10 rounded-full border-2 border-white/30 shadow-sm"
                />
                <span className="ml-2 sm:ml-2.5 font-medium text-sm sm:text-base">Window Warriors Team</span>
              </div>
              <span className="text-accessible-subtle text-sm sm:text-base">•</span>
              <span className="flex items-center text-sm sm:text-base">
                <svg className="w-3 sm:w-4 h-3 sm:h-4 mr-1 sm:mr-1.5 text-[#ec4899]" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd"></path>
                </svg>
                5 min read
              </span>
            </div>
          </div>
        </div>

        {/* Scroll Indicator - Mobile responsive */}
        <div className="absolute bottom-3 sm:bottom-4 left-1/2 transform -translate-x-1/2 z-30 animate-bounce">
          <div className="w-5 sm:w-6 h-8 sm:h-10 border-2 border-white/30 rounded-full flex justify-center">
            <div className="w-0.5 sm:w-1 h-2 sm:h-3 bg-[#ec4899] rounded-full mt-1.5 sm:mt-2 animate-pulse"></div>
          </div>
          <p className="text-accessible-subtle text-xs mt-1 sm:mt-2 text-center hidden sm:block">Scroll</p>
        </div>
      </section>

      {/* Article Content - Enhanced - Mobile Optimized */}
      <section className="py-8 sm:py-12 md:py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl mx-auto">
            {/* Table of Contents - Improved - Mobile Optimized */}
            <div className="bg-gray-50 rounded-lg sm:rounded-xl p-4 sm:p-6 mb-8 sm:mb-12 border border-gray-100 shadow-sm hover:shadow-md transition-shadow duration-300">
              <h2 className="text-base sm:text-lg font-semibold text-[#1a3a6d] mb-3 sm:mb-4 flex items-center">
                <svg className="w-4 sm:w-5 h-4 sm:h-5 mr-2 text-[#ec4899]" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z"></path>
                </svg>
                In this article
              </h2>
              <ul className="space-y-2 sm:space-y-3">
                <li>
                  <a href="#introduction" className="text-[#1a3a6d] hover:text-[#ec4899] transition-colors flex items-center group text-sm sm:text-base">
                    <span className="w-5 sm:w-6 h-5 sm:h-6 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-2 sm:mr-3 group-hover:bg-[#ec4899]/20 transition-colors text-xs sm:text-sm">1</span>
                    <span className="font-medium">Introduction</span>
                  </a>
                </li>
                <li>
                  <a href="#energy-efficiency" className="text-[#1a3a6d] hover:text-[#ec4899] transition-colors flex items-center group text-sm sm:text-base">
                    <span className="w-5 sm:w-6 h-5 sm:h-6 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-2 sm:mr-3 group-hover:bg-[#ec4899]/20 transition-colors text-xs sm:text-sm">2</span>
                    <span className="font-medium">Superior Energy Efficiency</span>
                  </a>
                </li>
                <li>
                  <a href="#durability" className="text-[#1a3a6d] hover:text-[#ec4899] transition-colors flex items-center group text-sm sm:text-base">
                    <span className="w-5 sm:w-6 h-5 sm:h-6 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-2 sm:mr-3 group-hover:bg-[#ec4899]/20 transition-colors text-xs sm:text-sm">3</span>
                    <span className="font-medium">Exceptional Durability</span>
                  </a>
                </li>
                <li>
                  <a href="#low-maintenance" className="text-[#1a3a6d] hover:text-[#ec4899] transition-colors flex items-center group text-sm sm:text-base">
                    <span className="w-5 sm:w-6 h-5 sm:h-6 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-2 sm:mr-3 group-hover:bg-[#ec4899]/20 transition-colors text-xs sm:text-sm">4</span>
                    <span className="font-medium">Low Maintenance</span>
                  </a>
                </li>
                <li>
                  <a href="#noise-reduction" className="text-[#1a3a6d] hover:text-[#ec4899] transition-colors flex items-center group text-sm sm:text-base">
                    <span className="w-5 sm:w-6 h-5 sm:h-6 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-2 sm:mr-3 group-hover:bg-[#ec4899]/20 transition-colors text-xs sm:text-sm">5</span>
                    <span className="font-medium">Noise Reduction</span>
                  </a>
                </li>
                <li>
                  <a href="#conclusion" className="text-[#1a3a6d] hover:text-[#ec4899] transition-colors flex items-center group text-sm sm:text-base">
                    <span className="w-5 sm:w-6 h-5 sm:h-6 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-2 sm:mr-3 group-hover:bg-[#ec4899]/20 transition-colors text-xs sm:text-sm">6</span>
                    <span className="font-medium">Conclusion & Next Steps</span>
                  </a>
                </li>
              </ul>
            </div>
            
            {/* Introduction - Enhanced - Mobile Optimized */}
            <div id="introduction" className="mb-12 sm:mb-16 scroll-mt-24">
              <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-4 sm:mb-6 pb-2 border-b border-gray-200 flex items-center">
                <span className="w-6 sm:w-8 h-6 sm:h-8 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mr-2 sm:mr-3 text-xs sm:text-sm">1</span>
                Introduction
              </h2>
              <div className="prose prose-lg max-w-none">
                <p className="text-lg sm:text-xl md:text-2xl text-[#1a3a6d] font-medium mb-4 sm:mb-5 leading-relaxed">
                  When it comes to upgrading your home's windows, few options offer the perfect balance of performance, aesthetics, and value quite like UPVC windows. At Window Warriors, we've helped countless Newcastle homeowners transform their properties with premium UPVC installations.
                </p>
                
                <p className="text-accessible-gray mb-4 sm:mb-6 leading-relaxed text-sm sm:text-base">
                  From breathtaking coastal homes in Tynemouth to elegant period properties in Jesmond, we've seen how the right UPVC windows can completely revitalize a property's appearance while delivering exceptional practical advantages. Let's explore the five key benefits that make UPVC windows the preferred choice for discerning homeowners across the North East.
                </p>
                
                <div className="bg-blue-50 border-l-4 border-[#1a3a6d] p-5 my-8 rounded-r-xl">
                  <h3 className="text-[#1a3a6d] font-semibold text-lg mb-2">Why Choose UPVC Windows?</h3>
                  <p className="text-accessible-gray">UPVC (Unplasticized Polyvinyl Chloride) has become the material of choice for modern window frames, offering an unmatched combination of thermal efficiency, durability, and design versatility. Unlike traditional timber or aluminum alternatives, UPVC windows require minimal maintenance while providing superior performance in the UK's variable climate.</p>
                </div>
              </div>
              
              <figure className="my-10 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300">
                <div className="relative h-[300px] md:h-[400px] overflow-hidden">
                  <Image
                    src="/images/beautiful-view-blue-lake-captured-from-inside-villa.jpg"
                    alt="Modern UPVC windows in a stylish home"
                    fill
                    sizes="(max-width: 768px) 100vw, 800px"
                    className="object-cover w-full h-full transition-transform duration-700 hover:scale-105"
                  />
                </div>
                <figcaption className="bg-gray-50 text-accessible-gray text-sm p-4 italic border-t border-gray-100">
                  UPVC windows provide excellent views while maintaining superior energy efficiency
                </figcaption>
              </figure>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 mt-8">
                <div className="bg-[#1a3a6d]/5 p-5 rounded-lg hover:bg-[#1a3a6d]/10 transition-colors">
                  <div className="flex items-center mb-3">
                    <svg className="w-5 h-5 text-[#ec4899] mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z"></path>
                    </svg>
                    <h3 className="font-semibold text-[#1a3a6d]">Did You Know?</h3>
                  </div>
                  <p className="text-accessible-gray text-sm">Over 80% of new residential window installations in the UK now use UPVC frames, making them the most popular choice for modern homes.</p>
                </div>
                <div className="bg-[#1a3a6d]/5 p-5 rounded-lg hover:bg-[#1a3a6d]/10 transition-colors">
                  <div className="flex items-center mb-3">
                    <svg className="w-5 h-5 text-[#ec4899] mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd"></path>
                    </svg>
                    <h3 className="font-semibold text-[#1a3a6d]">Expert Tip</h3>
                  </div>
                  <p className="text-accessible-gray text-sm">When considering UPVC windows, look for those with multi-chambered frames for maximum thermal efficiency and strength.</p>
                </div>
              </div>
            </div>

            {/* Benefit 1 - Energy Efficiency - Same as before */}
            <div id="energy-efficiency" className="mb-16 scroll-mt-24">
              <div className="flex items-center mb-6">
                <div className="flex items-center justify-center w-10 h-10 rounded-full bg-[#1a3a6d]/10 text-[#1a3a6d] font-bold mr-4">
                  2
                </div>
                <h2 className="text-2xl md:text-3xl font-bold text-[#1a3a6d]">Superior Energy Efficiency</h2>
              </div>
              
              <div className="prose prose-lg max-w-none">
                <p className="mb-5 leading-relaxed">
                  In today's climate of rising energy costs, the exceptional thermal performance of UPVC windows represents perhaps their most compelling advantage. The multi-chambered design of UPVC frames creates natural insulation barriers, dramatically reducing heat transfer between your home and the outside environment.
                </p>
                
                <p className="mb-5 leading-relaxed">
                  When paired with energy-efficient double or triple glazing, UPVC windows can help:
                </p>
                
                <ul className="space-y-4 mb-6 ml-0 list-none">
                  {[
                    "Reduce your heating bills by up to 25%",
                    "Eliminate uncomfortable cold spots near windows",
                    "Achieve superior energy ratings (often A or A+)",
                    "Lower your home's carbon footprint significantly"
                  ].map((item, index) => (
                    <li key={index} className="flex items-start p-3 bg-[#1a3a6d]/5 rounded-lg hover:bg-[#1a3a6d]/10 transition-colors">
                      <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-[#1a3a6d]/20 text-[#1a3a6d] mr-3 mt-0.5 flex-shrink-0">
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                        </svg>
                      </span>
                      <span className="text-accessible-gray">{item}</span>
                    </li>
                  ))}
                </ul>
                
                <p className="leading-relaxed">
                  The exceptional thermal properties of our UPVC windows mean your heating system works less, your energy bills decrease, and your home maintains a consistently comfortable temperature throughout the year.
                </p>
              </div>
              
              <div className="bg-blue-50 border-l-4 border-[#1a3a6d] p-6 my-8 rounded-r-xl shadow-sm">
                <div className="flex items-start">
                  <svg className="w-6 h-6 text-[#1a3a6d] mr-3 mt-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd"></path>
                  </svg>
                  <div>
                    <p className="text-[#1a3a6d] font-medium text-lg">Energy Efficiency Fact</p>
                    <p className="text-accessible-gray mt-1">Modern UPVC windows can reduce heat loss by up to 50% compared to older single-glazed alternatives, potentially saving hundreds of pounds on annual energy bills.</p>
                  </div>
                </div>
              </div>
              
              <figure className="my-10 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300">
                <div className="relative h-[300px] md:h-[400px] overflow-hidden">
                  <Image
                    src="/images/exterior-home.jpg"
                    alt="Energy efficient home with UPVC windows"
                    fill
                    sizes="(max-width: 768px) 100vw, 800px"
                    className="object-cover w-full h-full transition-transform duration-700 hover:scale-105"
                  />
                </div>
                <figcaption className="bg-gray-50 text-accessible-gray text-sm p-4 italic border-t border-gray-100">
                  UPVC windows enhance both the appearance and energy efficiency of your home
                </figcaption>
              </figure>
            </div>

            {/* Benefit 2 - Durability - New Section */}
            <div id="durability" className="mb-16 scroll-mt-24">
              <div className="flex items-center mb-6">
                <div className="flex items-center justify-center w-10 h-10 rounded-full bg-[#1a3a6d]/10 text-[#1a3a6d] font-bold mr-4">
                  3
                </div>
                <h2 className="text-2xl md:text-3xl font-bold text-[#1a3a6d]">Exceptional Durability & Longevity</h2>
              </div>
              
              <div className="prose prose-lg max-w-none">
                <p className="mb-5 leading-relaxed">
                  When investing in new windows, longevity is a critical consideration. UPVC windows excel in this area, offering extraordinary durability that significantly outperforms many alternative materials. With proper installation and minimal maintenance, quality UPVC windows can maintain their performance and appearance for 20-30 years.
                </p>
                
                <div className="bg-amber-50 p-6 my-8 rounded-xl border border-amber-100">
                  <h3 className="text-lg font-bold text-[#1a3a6d] mb-3 flex items-center">
                    <svg className="w-5 h-5 mr-2 text-[#ec4899]" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd"></path>
                    </svg>
                    Durability Advantages of UPVC Windows
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                    <div className="flex items-start p-3 bg-white rounded-lg shadow-sm">
                      <span className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-[#ec4899]/20 text-[#ec4899] mr-3 mt-0.5 flex-shrink-0">
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                      </span>
                      <div>
                        <h4 className="font-medium text-[#1a3a6d]">Weather Resistant</h4>
                        <p className="text-sm text-accessible-gray mt-1">Won't rot, warp, or corrode even in Newcastle's variable coastal climate</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start p-3 bg-white rounded-lg shadow-sm">
                      <span className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-[#ec4899]/20 text-[#ec4899] mr-3 mt-0.5 flex-shrink-0">
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                      </span>
                      <div>
                        <h4 className="font-medium text-[#1a3a6d]">UV Stable</h4>
                        <p className="text-sm text-accessible-gray mt-1">Advanced formulations resist fading and discoloration from sunlight</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start p-3 bg-white rounded-lg shadow-sm">
                      <span className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-[#ec4899]/20 text-[#ec4899] mr-3 mt-0.5 flex-shrink-0">
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                      </span>
                      <div>
                        <h4 className="font-medium text-[#1a3a6d]">Impact Resistant</h4>
                        <p className="text-sm text-accessible-gray mt-1">Strong enough to withstand everyday knocks and impacts</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start p-3 bg-white rounded-lg shadow-sm">
                      <span className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-[#ec4899]/20 text-[#ec4899] mr-3 mt-0.5 flex-shrink-0">
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                      </span>
                      <div>
                        <h4 className="font-medium text-[#1a3a6d]">Fire Resistant</h4>
                        <p className="text-sm text-accessible-gray mt-1">Inherently flame retardant - won't ignite or spread flames</p>
                      </div>
                    </div>
                  </div>
                </div>
                
                <p className="leading-relaxed">
                  For Newcastle homeowners, this durability translates to exceptional value. While timber windows might require extensive maintenance or even replacement after just 10-15 years in our coastal climate, UPVC continues performing year after year with minimal intervention.
                </p>
                
                <p className="leading-relaxed">
                  Modern UPVC formulations also address historical concerns about brittleness in extreme cold. Today's premium UPVC window systems maintain their structural integrity and operational performance even in the harshest winter conditions.
                </p>
              </div>
              
              <div className="relative my-10 rounded-xl overflow-hidden">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <figure className="rounded-xl overflow-hidden shadow-md">
                    <div className="relative h-[250px] overflow-hidden">
                      <Image
                        src="/images/beautiful-view-blue-lake-captured-from-inside-villa.jpg"
                        alt="Durable UPVC windows lasting for decades"
                        fill
                        sizes="(max-width: 768px) 100vw, 400px"
                        className="object-cover w-full h-full transition-transform duration-700 hover:scale-105"
                      />
                    </div>
                    <figcaption className="bg-gray-50 text-accessible-gray text-sm p-3 italic border-t border-gray-100">
                      UPVC windows maintain their appearance for decades
                    </figcaption>
                  </figure>
                  
                  <div className="bg-[#1a3a6d] text-white p-6 rounded-xl flex flex-col justify-center">
                    <h3 className="text-xl font-bold mb-4 flex items-center">
                      <svg className="w-5 h-5 mr-2 text-[#ec4899]" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                      </svg>
                      Window Warriors Quality Guarantee
                    </h3>
                    <p className="text-accessible-light mb-4">
                      All our UPVC windows come with a comprehensive 10-year guarantee covering frames, glazing, and hardware—a testament to the exceptional durability of these premium products.
                    </p>
                    <div className="mt-2 inline-block">
                      <Link 
                        href="/contact" 
                        className="text-white font-medium hover:text-[#ec4899] transition-colors flex items-center"
                      >
                        Learn more about our guarantees
                        <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Benefit 3 - Low Maintenance - New Section */}
            <div id="low-maintenance" className="mb-16 scroll-mt-24">
              <div className="flex items-center mb-6">
                <div className="flex items-center justify-center w-10 h-10 rounded-full bg-[#1a3a6d]/10 text-[#1a3a6d] font-bold mr-4">
                  4
                </div>
                <h2 className="text-2xl md:text-3xl font-bold text-[#1a3a6d]">Effortless Maintenance</h2>
              </div>
              
              <div className="prose prose-lg max-w-none">
                <p className="mb-5 leading-relaxed">
                  In our busy modern lives, the thought of spending weekends sanding, painting, and maintaining timber windows holds little appeal. One of the most appreciated benefits of UPVC windows is their remarkably low maintenance requirements, saving you both time and money over their lifetime.
                </p>
                
                <div className="flex flex-col md:flex-row gap-6 my-8">
                  <div className="md:w-1/2 bg-gray-50 p-6 rounded-xl border border-gray-100">
                    <h3 className="text-[#1a3a6d] font-bold text-lg mb-4">Traditional Windows Maintenance</h3>
                    <ul className="space-y-3">
                      {[
                        "Regular sanding and repainting every 3-5 years",
                        "Treatment with preservatives and fungicides",
                        "Repair of rot, warping, and weather damage",
                        "Replacement of deteriorated sections",
                        "Professional servicing of moving parts and hinges",
                        "Extensive cleaning to remove built-up dirt"
                      ].map((item, index) => (
                        <li key={index} className="flex items-start">
                          <svg className="w-5 h-5 text-red-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd"></path>
                          </svg>
                          <span className="text-accessible-gray text-sm">{item}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div className="md:w-1/2 bg-[#1a3a6d]/5 p-6 rounded-xl border border-[#1a3a6d]/10">
                    <h3 className="text-[#1a3a6d] font-bold text-lg mb-4">UPVC Windows Maintenance</h3>
                    <ul className="space-y-3">
                      {[
                        "Simple cleaning with soapy water twice yearly",
                        "Occasional lubrication of moving parts",
                        "No painting or treatments required ever",
                        "Wipe-clean surfaces that don't harbor dirt",
                        "No risk of rot, rust or corrosion",
                        "Never needs sanding, scraping or refinishing"
                      ].map((item, index) => (
                        <li key={index} className="flex items-start">
                          <svg className="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                          </svg>
                          <span className="text-accessible-gray text-sm">{item}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
                
                <p className="leading-relaxed">
                  The non-porous surface of UPVC repels dirt and grime, making cleaning quick and simple. Unlike timber, UPVC won't absorb moisture, so there's no risk of swelling, warping, or rotting. The color is integrated throughout the material rather than applied as paint, so it won't flake, peel, or require repainting.
                </p>
                
                <div className="bg-blue-50 border-l-4 border-[#1a3a6d] p-5 my-6 rounded-r-lg">
                  <h4 className="text-[#1a3a6d] font-medium mb-2">Maintenance Tip</h4>
                  <p className="text-accessible-gray text-sm">
                    For optimal performance, we recommend a simple wipe-down of your UPVC frames with mild soapy water twice a year and applying a small amount of lubricant to handles and hinges. This minimal routine will keep your windows operating smoothly for decades.
                  </p>
                </div>
              </div>
              
              <figure className="my-8 rounded-xl overflow-hidden shadow-md">
                <div className="relative h-[300px] overflow-hidden">
                  <Image
                    src="/images/beautiful-view-blue-lake-captured-from-inside-villa.jpg"
                    alt="Easy to maintain UPVC windows"
                    fill
                    sizes="(max-width: 768px) 100vw, 800px"
                    className="object-cover w-full h-full transition-transform duration-700 hover:scale-105"
                  />
                </div>
                <figcaption className="bg-gray-50 text-accessible-gray text-sm p-4 italic border-t border-gray-100">
                  UPVC windows stay looking fresh with minimal maintenance for decades
                </figcaption>
              </figure>
              
              <div className="bg-gradient-to-r from-[#1a3a6d]/10 to-transparent p-6 rounded-xl">
                <h3 className="text-[#1a3a6d] font-bold text-lg mb-3">What Our Customers Say</h3>
                <blockquote className="text-accessible-gray italic border-l-4 border-[#ec4899] pl-4 py-1">
                  "After years of spending every spring maintaining our old wooden windows, our UPVC replacements have been a revelation. Five years in and they still look brand new with almost no effort on our part!"
                </blockquote>
                <p className="text-right text-sm text-accessible-gray mt-2">— Sarah J, Jesmond</p>
              </div>
            </div>

            {/* Benefit 4 - Noise Reduction - New Section */}
            <div id="noise-reduction" className="mb-16 scroll-mt-24">
              <div className="flex items-center mb-6">
                <div className="flex items-center justify-center w-10 h-10 rounded-full bg-[#1a3a6d]/10 text-[#1a3a6d] font-bold mr-4">
                  5
                </div>
                <h2 className="text-2xl md:text-3xl font-bold text-[#1a3a6d]">Superior Noise Reduction</h2>
              </div>
              
              <div className="prose prose-lg max-w-none">
                <p className="mb-5 leading-relaxed">
                  In today's increasingly noisy world, creating a peaceful home environment is more important than ever. Modern UPVC windows offer exceptional acoustic insulation properties, significantly reducing the impact of external noise pollution on your living spaces.
                </p>
                
                <div className="relative my-8 rounded-xl overflow-hidden bg-[#1a3a6d]/5 p-6">
                  <div className="flex flex-col md:flex-row items-center gap-6">
                    <div className="md:w-2/5">
                      <h3 className="text-[#1a3a6d] font-bold text-lg mb-4 flex items-center">
                        <svg className="w-5 h-5 mr-2 text-[#ec4899]" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 3a1 1 0 00-1.447-.894L8.763 6H5a3 3 0 000 6h.28l1.771 5.316A1 1 0 008 18h1a1 1 0 001-1v-4.382l6.553 3.276A1 1 0 0018 15V3z" clipRule="evenodd" />
                        </svg>
                        Sound Reduction Benefits
                      </h3>
                      <p className="text-accessible-gray text-base mb-4">
                        UPVC windows with quality double glazing can reduce external noise by up to 40 decibels, transforming your home into a peaceful haven regardless of your surroundings.
                      </p>
                      <p className="text-accessible-gray text-base">
                        This sound insulation is particularly valuable for homes in these locations:
                      </p>
                    </div>
                    
                    <div className="md:w-3/5">
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                        <div className="bg-white rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow">
                          <div className="flex items-center mb-2">
                            <svg className="w-5 h-5 text-[#1a3a6d] mr-2" fill="currentColor" viewBox="0 0 20 20">
                              <path d="M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z" />
                              <path d="M3 4a1 1 0 00-1 1v10a1 1 0 001 1h1.05a2.5 2.5 0 014.9 0H10a1 1 0 001-1v-5h2a1 1 0 00.9-.5l3-5A1 1 0 0016 3H4a1 1 0 00-1 1z" />
                            </svg>
                            <h4 className="font-medium text-[#1a3a6d]">Near Busy Roads</h4>
                          </div>
                          <p className="text-sm text-accessible-gray">Protection from traffic noise, including heavy vehicles and emergency sirens</p>
                        </div>
                        
                        <div className="bg-white rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow">
                          <div className="flex items-center mb-2">
                            <svg className="w-5 h-5 text-[#1a3a6d] mr-2" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                            </svg>
                            <h4 className="font-medium text-[#1a3a6d]">City Centres</h4>
                          </div>
                          <p className="text-sm text-accessible-gray">Shield from urban noise like pedestrian traffic, nightlife, and construction</p>
                        </div>
                        
                        <div className="bg-white rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow">
                          <div className="flex items-center mb-2">
                            <svg className="w-5 h-5 text-[#1a3a6d] mr-2" fill="currentColor" viewBox="0 0 20 20">
                              <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
                            </svg>
                            <h4 className="font-medium text-[#1a3a6d]">Flight Paths</h4>
                          </div>
                          <p className="text-sm text-accessible-gray">Significant reduction in aircraft noise for properties under flight routes</p>
                        </div>
                        
                        <div className="bg-white rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow">
                          <div className="flex items-center mb-2">
                            <svg className="w-5 h-5 text-[#1a3a6d] mr-2" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                            </svg>
                            <h4 className="font-medium text-[#1a3a6d]">Family Homes</h4>
                          </div>
                          <p className="text-sm text-accessible-gray">Create quiet spaces for children to study, sleep, and develop without disturbance</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="my-8 p-6 bg-white rounded-xl shadow-sm border border-gray-100">
                  <h3 className="text-[#1a3a6d] font-bold text-lg mb-4">How UPVC Windows Reduce Noise</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-5 mt-4">
                    <div className="flex flex-col items-center text-center">
                      <div className="w-16 h-16 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mb-4">
                        <svg className="w-8 h-8 text-[#1a3a6d]" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm3 1h6v4H7V5zm8 8v2h1v1H4v-1h1v-2a1 1 0 011-1h8a1 1 0 011 1z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <h4 className="font-medium text-[#1a3a6d] mb-2">Multi-Chamber Design</h4>
                      <p className="text-sm text-accessible-gray">The hollow chambers within UPVC frames trap and absorb sound waves rather than transmitting them</p>
                    </div>
                    
                    <div className="flex flex-col items-center text-center">
                      <div className="w-16 h-16 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mb-4">
                        <svg className="w-8 h-8 text-[#1a3a6d]" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                        </svg>
                      </div>
                      <h4 className="font-medium text-[#1a3a6d] mb-2">Double/Triple Glazing</h4>
                      <p className="text-sm text-accessible-gray">Multiple glass panes with gas-filled gaps create barriers that break up sound wave transmission</p>
                    </div>
                    
                    <div className="flex flex-col items-center text-center">
                      <div className="w-16 h-16 rounded-full bg-[#1a3a6d]/10 flex items-center justify-center mb-4">
                        <svg className="w-8 h-8 text-[#1a3a6d]" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <h4 className="font-medium text-[#1a3a6d] mb-2">Precision Sealing</h4>
                      <p className="text-sm text-accessible-gray">Advanced weatherstripping and gaskets eliminate gaps where sound could infiltrate</p>
                    </div>
                  </div>
                </div>
                
                <p className="leading-relaxed">
                  For many of our Newcastle customers, the noise reduction properties of UPVC windows have proven to be an unexpected but highly valued benefit. Homeowners regularly report significant improvements in sleep quality, concentration, and overall wellbeing after installation, particularly in properties near busy roads or in central locations.
                </p>
              </div>
              
              <div className="bg-gray-50 border border-gray-100 rounded-xl p-6 my-8">
                <div className="flex items-start">
                  <svg className="w-12 h-12 text-[#ec4899] mr-4 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd"></path>
                  </svg>
                  <div>
                    <h3 className="text-[#1a3a6d] font-bold text-lg mb-2">Did You Know?</h3>
                    <p className="text-accessible-gray">
                      Sound intensity is measured on a logarithmic scale, meaning a reduction of 10 decibels represents a 10-fold decrease in acoustic energy. This makes the 30-40 decibel reduction provided by quality UPVC windows tremendously significant in real-world terms—reducing the perceived noise level to approximately 1/1000th of the original intensity.
                    </p>
                  </div>
                </div>
              </div>
              
              <figure className="my-10 rounded-xl overflow-hidden shadow-md">
                <div className="relative h-[300px] md:h-[400px] overflow-hidden">
                  <Image
                    src="/images/beautiful-view-blue-lake-captured-from-inside-villa.jpg"
                    alt="Peaceful interior with noise-reducing UPVC windows"
                    fill
                    sizes="(max-width: 768px) 100vw, 800px"
                    className="object-cover w-full h-full transition-transform duration-700 hover:scale-105"
                  />
                </div>
                <figcaption className="bg-gray-50 text-accessible-gray text-sm p-4 italic border-t border-gray-100">
                  Create a peaceful sanctuary with the superior acoustic insulation of UPVC windows
                </figcaption>
              </figure>
            </div>

            {/* CTA Section - Keep the same as before */}
            <div id="conclusion" className="mb-16 scroll-mt-24">
              <div className="bg-gradient-to-br from-[#f8f9fa] to-[#e9f0f8] border border-gray-100 rounded-xl p-8 sm:p-10 mb-12 shadow-md hover:shadow-lg transition-shadow duration-300">
                <div className="flex items-center mb-6">
                  <div className="flex items-center justify-center w-10 h-10 rounded-full bg-[#1a3a6d]/10 text-[#1a3a6d] font-bold mr-4">
                    6
                  </div>
                  <h2 className="text-2xl md:text-3xl font-bold text-[#1a3a6d]">
                    Experience the UPVC Difference
                  </h2>
                </div>
                
                <p className="text-accessible-gray mb-5 leading-relaxed">
                  Ready to transform your home with premium UPVC windows that offer superior energy efficiency, durability, security, noise reduction, and aesthetic appeal? Our expert team is here to help you select the perfect windows for your Newcastle home.
                </p>
                
                <div className="flex flex-col sm:flex-row gap-4 mt-8">
                  <Link
                    href="/products-services#quote"
                    className="bg-[#1a3a6d] hover:bg-[#0f2d5c] text-white font-medium py-3.5 px-6 rounded-lg text-center shadow-sm hover:shadow transition-all duration-300 flex items-center justify-center"
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                    Get Your Free Quote
                  </Link>
                  <Link
                    href="/gallery"
                    className="bg-white hover:bg-gray-50 text-[#1a3a6d] border border-[#1a3a6d] font-medium py-3.5 px-6 rounded-lg text-center shadow-sm hover:shadow transition-all duration-300 flex items-center justify-center"
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    View Our Window Gallery
                  </Link>
                </div>
              </div>
            </div>
            
            {/* Author Section - Same as before */}
            <div className="bg-gray-50 rounded-xl p-6 border border-gray-100 shadow-sm mb-12 hover:shadow transition-shadow duration-300">
              <div className="flex items-start space-x-5">
                <Image 
                  src="/window-warriors-logo.png" 
                  alt="Window Warriors Author" 
                  width={70} 
                  height={70}
                  className="rounded-full border-2 border-gray-200 shadow-sm"
                />
                <div>
                  <h3 className="font-semibold text-lg text-[#1a3a6d] mb-2">Window Warriors Team</h3>
                  <p className="text-accessible-gray text-base leading-relaxed">Our team of window specialists has over 15 years of experience installing premium windows throughout Newcastle and the North East. We're passionate about helping homeowners improve their properties with energy-efficient, stylish solutions.</p>
                </div>
              </div>
            </div>
            
            {/* Related Articles - Enhanced */}
            <div className="mb-16">
              <div className="flex items-center mb-8">
                <div className="h-px flex-grow bg-gradient-to-r from-gray-200 to-transparent"></div>
                <h3 className="text-xl font-bold text-[#1a3a6d] px-4">Related Articles</h3>
                <div className="h-px flex-grow bg-gradient-to-l from-gray-200 to-transparent"></div>
              </div>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                {/* Related Article 1 */}
                <div className="group relative rounded-xl shadow-sm hover:shadow-md overflow-hidden transition-all duration-300 border border-gray-100 hover:border-gray-200 hover:-translate-y-1">
                  <div className="relative h-48 overflow-hidden">
                    <Image 
                      src="/images/front-view-front-door-with-blue-wall.jpg" 
                      alt="How to Choose the Perfect Front Door for Your Home" 
                      fill 
                      className="object-cover group-hover:scale-105 transition-transform duration-700"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-[#1a3a6d]/80 to-transparent opacity-70"></div>
                    <div className="absolute bottom-0 left-0 p-4">
                      <span className="inline-block py-1 px-2 bg-[#ec4899] text-white text-xs font-medium rounded-md mb-2">
                        DOORS
                      </span>
                      <h4 className="text-white font-bold text-lg leading-tight">How to Choose the Perfect Front Door</h4>
                    </div>
                  </div>
                  <div className="p-4">
                    <p className="text-accessible-gray text-sm mb-4 line-clamp-3">
                      Your front door makes a statement about your home. Learn how to choose the right style, material, and security features.
                    </p>
                    <Link 
                      href="/blog/posts/choosing-perfect-front-door" 
                      className="text-[#1a3a6d] font-medium text-sm hover:text-[#ec4899] transition-colors inline-flex items-center"
                    >
                      Read Article
                      <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                      </svg>
                    </Link>
                  </div>
                </div>
                
                {/* Related Article 2 */}
                <div className="group relative rounded-xl shadow-sm hover:shadow-md overflow-hidden transition-all duration-300 border border-gray-100 hover:border-gray-200 hover:-translate-y-1">
                  <div className="relative h-48 overflow-hidden">
                    <Image 
                      src="/images/exterior-home.jpg" 
                      alt="Energy Efficiency: How New Windows Can Reduce Your Bills" 
                      fill 
                      className="object-cover group-hover:scale-105 transition-transform duration-700"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-[#1a3a6d]/80 to-transparent opacity-70"></div>
                    <div className="absolute bottom-0 left-0 p-4">
                      <span className="inline-block py-1 px-2 bg-[#ec4899] text-white text-xs font-medium rounded-md mb-2">
                        ENERGY SAVING
                      </span>
                      <h4 className="text-white font-bold text-lg leading-tight">Energy Efficiency: Reduce Your Bills</h4>
                    </div>
                  </div>
                  <div className="p-4">
                    <p className="text-accessible-gray text-sm mb-4 line-clamp-3">
                      Find out how modern window technology can significantly cut your energy bills and make your home more comfortable year-round.
                    </p>
                    <Link 
                      href="/blog/posts/energy-efficiency-windows" 
                      className="text-[#1a3a6d] font-medium text-sm hover:text-[#ec4899] transition-colors inline-flex items-center"
                    >
                      Read Article
                      <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                      </svg>
                    </Link>
                  </div>
                </div>
              </div>
              
              <div className="flex justify-center mt-8">
                <Link 
                  href="/blog" 
                  className="bg-white hover:bg-gray-50 text-[#1a3a6d] border border-[#1a3a6d]/30 font-medium py-2.5 px-5 rounded-lg transition-colors inline-flex items-center"
                >
                  View All Articles
                  <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                </Link>
              </div>
            </div>
            
            {/* Social Share Buttons */}
            <div className="sticky bottom-8 z-30 flex items-center justify-center">
              <div className="bg-white py-2.5 px-5 rounded-full shadow-lg border border-gray-100 flex space-x-2">
                <span className="text-sm text-accessible-gray mr-1 hidden sm:inline">Share:</span>
                <button className="text-[#1a3a6d] hover:text-[#ec4899] transition-colors p-1.5 rounded-full hover:bg-gray-50">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"></path>
                  </svg>
                </button>
                <button className="text-[#1a3a6d] hover:text-[#ec4899] transition-colors p-1.5 rounded-full hover:bg-gray-50">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"></path>
                  </svg>
                </button>
                <button className="text-[#1a3a6d] hover:text-[#ec4899] transition-colors p-1.5 rounded-full hover:bg-gray-50">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M19 0h-14c-2.761 0-5 2.239-5 5v10a2 2 0 002 2h12a2 2 0 002-2V5c0-2.761-2.239-5-5-5zm-4 16h-4v-4h4v4zm0-6h-4v-4h4v4zm0-6h-4v-4h4v4z"></path>
                  </svg>
                </button>
                <button className="text-[#1a3a6d] hover:text-[#ec4899] transition-colors p-1.5 rounded-full hover:bg-gray-50">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 4.5v15c0 .85-.65 1.5-1.5 1.5H21V7.387l-9 6.463-9-6.463V21H1.5C.649 21 0 20.35 0 19.5v-15c0-.425.162-.8.431-1.068C.7 3.16 1.076 3 1.5 3H2l10 7.25L22 3h.5c.425 0 .8.162 1.069.432.27.268.431.643.431 1.068z"></path>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}


