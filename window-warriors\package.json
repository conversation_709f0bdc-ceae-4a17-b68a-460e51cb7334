{"name": "window-warriors", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "optimize-images": "node optimize-images.js", "analyze": "cross-env ANALYZE=true next build", "bundle-analyzer": "cross-env ANALYZE=true next build", "performance-test": "npm run build && npm run start", "check-images": "node -e \"console.log('Checking optimized images...'); const fs = require('fs'); const path = './public/images/optimized'; if (fs.existsSync(path)) { const files = fs.readdirSync(path); console.log(`Found ${files.length} optimized images:`); files.forEach(f => console.log('✓', f)); } else { console.log('No optimized images found. Run: npm run optimize-images'); }\"", "check-bundle": "npx @next/bundle-analyzer", "lighthouse": "lighthouse https://windowwarriors.uk --only-categories=performance,accessibility --output=json --output-path=./lighthouse-report.json", "performance-audit": "npm run build && npm run lighthouse"}, "dependencies": {"@types/leaflet": "^1.9.17", "leaflet": "^1.9.4", "next": "15.2.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/bundle-analyzer": "^15.2.4", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "15.2.4", "sharp": "^0.33.1", "tailwindcss": "^4", "typescript": "^5"}}