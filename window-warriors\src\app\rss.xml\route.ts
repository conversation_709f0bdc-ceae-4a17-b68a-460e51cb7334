import { NextResponse } from 'next/server';

// Base URL for the website
const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://windowwarriors.uk';

// Create mock blog posts for the RSS feed
// In a real app, these would come from a CMS or database
const blogPosts = [
  {
    id: 'energy-efficient-windows-benefits',
    title: 'Energy Efficient Windows: How They Benefit Your Home and Budget',
    excerpt: 'Discover how energy-efficient windows can reduce your utility bills and help the environment.',
    content: 'Energy-efficient windows play a crucial role in maintaining your home\'s temperature and reducing energy costs...',
    date: '2024-03-15',
    author: '<PERSON>',
    slug: 'energy-efficient-windows-benefits',
  },
  {
    id: 'choosing-the-right-windows-for-your-home',
    title: 'How to Choose the Right Windows for Your Home\'s Style',
    excerpt: 'A guide to selecting windows that complement your home\'s architecture and improve curb appeal.',
    content: 'When selecting new windows, consider your home\'s architectural style, climate considerations, and functional needs...',
    date: '2024-03-01',
    author: '<PERSON>',
    slug: 'choosing-the-right-windows-for-your-home',
  },
  {
    id: 'upvc-vs-aluminum-windows-comparison',
    title: 'UPVC vs. Aluminum Windows: A Comprehensive Comparison',
    excerpt: 'Compare the benefits and drawbacks of UPVC and aluminum window frames for your home.',
    content: 'Both UPVC and aluminum windows offer distinct advantages. UPVC provides better insulation while aluminum offers sleeker aesthetics...',
    date: '2024-02-15',
    author: 'Mike Williams',
    slug: 'upvc-vs-aluminum-windows-comparison',
  },
  {
    id: 'window-maintenance-tips-for-homeowners',
    title: 'Essential Window Maintenance Tips Every Homeowner Should Know',
    excerpt: 'Learn how to keep your windows functioning properly and looking great for years to come.',
    content: 'Regular window maintenance is key to extending the lifespan of your windows and maintaining their efficiency...',
    date: '2024-02-01',
    author: 'Emily Parker',
    slug: 'window-maintenance-tips-for-homeowners',
  },
  {
    id: 'how-to-improve-home-security-with-new-doors',
    title: 'How to Improve Your Home\'s Security with New Doors',
    excerpt: 'Discover how modern door technologies can enhance your home\'s security features.',
    content: 'Modern doors offer advanced security features that can significantly improve your home\'s protection against intruders...',
    date: '2024-01-15',
    author: 'David Thompson',
    slug: 'how-to-improve-home-security-with-new-doors',
  },
];

// Generate the RSS XML content
function generateRSSFeed() {
  const feedItems = blogPosts.map(post => `
    <item>
      <title><![CDATA[${post.title}]]></title>
      <link>${baseUrl}/blog/${post.slug}</link>
      <guid>${baseUrl}/blog/${post.slug}</guid>
      <pubDate>${new Date(post.date).toUTCString()}</pubDate>
      <description><![CDATA[${post.excerpt}]]></description>
      <author>${post.author}</author>
    </item>
  `).join('');

  return `<?xml version="1.0" encoding="UTF-8"?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title>Window Warriors Blog</title>
    <link>${baseUrl}</link>
    <description>Latest news and articles about windows, doors, and home improvement from Window Warriors</description>
    <language>en-us</language>
    <lastBuildDate>${new Date().toUTCString()}</lastBuildDate>
    <atom:link href="${baseUrl}/rss.xml" rel="self" type="application/rss+xml" />
    ${feedItems}
  </channel>
</rss>`;
}

// Route handler for serving the RSS feed
export async function GET() {
  return new NextResponse(generateRSSFeed(), {
    headers: {
      'Content-Type': 'application/xml',
      'Cache-Control': 'public, max-age=3600, stale-while-revalidate=86400',
    },
  });
} 