import { MetadataRoute } from 'next';

// Define base URL
const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://windowwarriors.uk';

// Define the changeFrequency type to match Next.js expectations
type ChangeFrequency = 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';

// Main pages with their lastModified and changeFrequency
const mainPages = [
  {
    url: '/',
    lastModified: new Date(),
    changeFrequency: 'weekly' as ChangeFrequency,
    priority: 1.0,
  },
  {
    url: '/about',
    lastModified: new Date(),
    changeFrequency: 'monthly' as ChangeFrequency,
    priority: 0.8,
  },
  {
    url: '/products-services',
    lastModified: new Date(),
    changeFrequency: 'monthly' as ChangeFrequency,
    priority: 0.9,
  },
  {
    url: '/gallery',
    lastModified: new Date(),
    changeFrequency: 'monthly' as ChangeFrequency,
    priority: 0.7,
  },
  {
    url: '/testimonials',
    lastModified: new Date(),
    changeFrequency: 'monthly' as ChangeFrequency,
    priority: 0.7,
  },
  {
    url: '/blog',
    lastModified: new Date(),
    changeFrequency: 'weekly' as ChangeFrequency,
    priority: 0.8,
  },
  {
    url: '/contact',
    lastModified: new Date(),
    changeFrequency: 'monthly' as ChangeFrequency,
    priority: 0.8,
  },
  {
    url: '/privacy-policy',
    lastModified: new Date(),
    changeFrequency: 'monthly' as ChangeFrequency,
    priority: 0.5,
  },
  {
    url: '/terms-of-service',
    lastModified: new Date(),
    changeFrequency: 'monthly' as ChangeFrequency,
    priority: 0.5,
  },
  {
    url: '/sitemap',
    lastModified: new Date(),
    changeFrequency: 'monthly' as ChangeFrequency,
    priority: 0.4,
  },
];

// In a real application, we would fetch blog entries from a CMS or database
// For now, we'll use our actual blog posts
const blogPosts = [
  {
    slug: 'benefits-of-upvc-windows',
    lastModified: new Date('2023-06-15'),
  },
  {
    slug: 'choosing-perfect-front-door',
    lastModified: new Date('2023-07-03'),
  },
  {
    slug: 'energy-efficiency-windows',
    lastModified: new Date('2023-08-22'),
  },
  {
    slug: 'upvc-vs-aluminum-windows-comparison',
    lastModified: new Date('2024-02-15'),
  },
  {
    slug: 'window-styles-for-period-properties',
    lastModified: new Date('2023-10-12'),
  },
  {
    slug: 'understanding-window-energy-ratings',
    lastModified: new Date('2023-11-08'),
  },
];

export default function sitemap(): MetadataRoute.Sitemap {
  // Generate sitemap entries for main pages
  const mainRoutes = mainPages.map(({ url, lastModified, changeFrequency, priority }) => ({
    url: `${baseUrl}${url}`,
    lastModified,
    changeFrequency,
    priority,
  }));

  // Generate sitemap entries for blog posts
  const blogRoutes = blogPosts.map(({ slug, lastModified }) => ({
    url: `${baseUrl}/blog/posts/${slug}`,
    lastModified,
    changeFrequency: 'monthly' as ChangeFrequency,
    priority: 0.6,
  }));

  // Combine all routes
  return [...mainRoutes, ...blogRoutes];
} 