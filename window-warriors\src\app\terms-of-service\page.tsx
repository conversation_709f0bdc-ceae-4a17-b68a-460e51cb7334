import React from 'react';
import { Metadata } from 'next';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Terms of Service | Window Warriors - UPVC Windows & Doors North East',
  description: 'Window Warriors terms of service agreement outlines the conditions for using our website and services. Read our legal terms before engaging with our UPVC windows and doors services.',
  keywords: 'terms of service, terms and conditions, legal terms, Window Warriors terms, UPVC windows terms',
  openGraph: {
    title: 'Terms of Service | Window Warriors',
    description: 'Our terms of service agreement outlines the conditions for using our website and services.',
    url: 'https://windowwarriors.uk/terms-of-service',
    siteName: 'Window Warriors',
    images: [
      {
        url: 'https://windowwarriors.uk/images/beautiful-hotel-insights-details.jpg',
        width: 1200,
        height: 630,
        alt: 'Window Warriors Terms of Service',
      },
    ],
    locale: 'en_GB',
    type: 'website',
  },
  alternates: {
    canonical: 'https://windowwarriors.uk/terms-of-service',
  },
};

export default function TermsOfServicePage() {
  return (
    <main className="flex min-h-screen flex-col page-with-hero">
      {/* Enhanced Hero Section */}
      <section className="relative min-h-[60vh] sm:min-h-[70vh] md:min-h-screen py-16 sm:py-24 md:py-28 lg:py-32 xl:py-36 overflow-hidden">
        {/* Simplified background */}
        <div className="absolute inset-0 bg-gradient-to-br from-[#1a3a6d] to-[#ec4899]/30"></div>
          
          {/* Simplified decorative elements */}
          <div className="hidden md:block absolute inset-0">
            <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-[#ec4899]/10 rounded-full blur-xl"></div>
          </div>
        
        {/* Enhanced Hero Content */}
        <div className="container mx-auto px-4 sm:px-6 relative z-10 flex flex-col justify-center min-h-[60vh] sm:min-h-[70vh] md:min-h-screen pb-8 sm:pb-16">
          <div className="max-w-4xl animate-fade-in" style={{ animationDelay: '0.3s' }}>
            {/* Enhanced brand badge */}
            <div className="inline-flex items-center mb-4 sm:mb-6 md:mb-8">
              <div className="bg-white/10 backdrop-blur-sm border border-[#ec4899]/30 rounded-full px-4 sm:px-6 py-2 animate-fade-blur" style={{ animationDelay: '0.2s' }}>
                <span className="text-accessible-light text-xs sm:text-sm md:text-base font-medium">TERMS OF SERVICE</span>
                <div className="w-full h-px bg-gradient-to-r from-transparent via-[#ec4899]/70 to-transparent mt-1"></div>
              </div>
            </div>
            
            {/* Enhanced title with pink gradients */}
            <h1 className="text-3xl sm:text-4xl md:text-6xl lg:text-7xl xl:text-8xl font-bold text-white mb-3 sm:mb-4 md:mb-6 leading-tight">
              Legal <span className="relative inline-block">
                <span className="text-gradient-logo">Terms</span>
                <div className="absolute -bottom-1 sm:-bottom-2 left-0 w-full h-0.5 sm:h-1 bg-gradient-to-r from-[#ec4899] to-[#d946ef] rounded-full animate-shimmer"></div>
                <div className="absolute -top-0.5 sm:-top-1 -right-0.5 sm:-right-1 w-2 sm:w-3 h-2 sm:h-3 bg-[#ec4899]/60 rounded-full animate-pulse"></div>
              </span>
            </h1>
            <h2 className="text-2xl sm:text-3xl md:text-5xl lg:text-6xl font-bold text-white mb-3 sm:mb-4 md:mb-6">
              & Conditions
            </h2>
            
            {/* Enhanced description */}
            <div className="relative mb-4 sm:mb-6 md:mb-8">
              <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-accessible-light leading-relaxed animate-slide-up" style={{ animationDelay: '0.6s' }}>
                The legal terms and conditions governing the use of our services with complete transparency.
              </p>
              <div className="absolute -left-2 sm:-left-4 top-0 w-0.5 sm:w-1 h-full bg-gradient-to-b from-[#ec4899] to-transparent rounded-full opacity-60"></div>
            </div>
            
            {/* Enhanced buttons */}
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 md:gap-6 mb-6 sm:mb-8 md:mb-10 animate-slide-up" style={{ animationDelay: '0.9s' }}>
              <Link href="/contact" className="btn-primary text-center text-sm sm:text-base">
                Contact Us
              </Link>
              <Link href="/" className="inline-block bg-white/10 backdrop-blur-sm border-2 border-white/30 text-white hover:bg-white hover:text-[#1a3a6d] font-medium py-3 sm:py-4 px-6 sm:px-8 rounded-full transition-all duration-300 text-center group text-sm sm:text-base">
                <span className="flex items-center justify-center">
                  <svg className="w-4 h-4 sm:w-5 sm:h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                  </svg>
                  Back to Home
                </span>
              </Link>
            </div>
            
            {/* Enhanced trust badges */}
            <div className="animate-fade-in" style={{ animationDelay: '1.2s' }}>
              <p className="text-white/70 text-xs sm:text-sm md:text-base mb-3 sm:mb-4">Legal protection</p>
              <div className="flex flex-wrap gap-2 sm:gap-3 md:gap-4">
                <div className="flex items-center gap-1.5 sm:gap-2 bg-white/10 backdrop-blur-sm border border-white/20 px-3 sm:px-4 py-1.5 sm:py-2 rounded-full">
                  <div className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 bg-[#ec4899]/20 rounded-full flex items-center justify-center">
                    <svg className="w-2.5 h-2.5 sm:w-3 sm:h-3 md:w-4 md:h-4 text-[#ec4899]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                  </div>
                  <span className="text-white text-xs sm:text-sm font-medium">Fair Terms</span>
                </div>
                <div className="flex items-center gap-1.5 sm:gap-2 bg-white/10 backdrop-blur-sm border border-white/20 px-3 sm:px-4 py-1.5 sm:py-2 rounded-full">
                  <div className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 bg-[#ec4899]/20 rounded-full flex items-center justify-center">
                    <svg className="w-2.5 h-2.5 sm:w-3 sm:h-3 md:w-4 md:h-4 text-[#ec4899]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3" />
                    </svg>
                  </div>
                  <span className="text-white text-xs sm:text-sm font-medium">Clear Rights</span>
                </div>
                <div className="flex items-center gap-1.5 sm:gap-2 bg-white/10 backdrop-blur-sm border border-white/20 px-3 sm:px-4 py-1.5 sm:py-2 rounded-full">
                  <div className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 bg-[#ec4899]/20 rounded-full flex items-center justify-center">
                    <svg className="w-2.5 h-2.5 sm:w-3 sm:h-3 md:w-4 md:h-4 text-[#ec4899]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <span className="text-white text-xs sm:text-sm font-medium">Full Disclosure</span>
                </div>
              </div>
            </div>
          </div>
          
          {/* Modern scroll indicator */}
          <div className="mt-auto pt-6 sm:pt-8 flex justify-center animate-bounce-slow">
            <div className="flex flex-col items-center">
              <div className="w-5 sm:w-6 h-8 sm:h-10 border-2 border-white/30 rounded-full flex justify-center">
                <div className="w-0.5 sm:w-1 h-2 sm:h-3 bg-white/60 rounded-full mt-1.5 sm:mt-2 animate-pulse"></div>
              </div>
              <span className="text-accessible-subtle text-xs mt-2 hidden sm:block">Read our terms</span>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content Section */}
      <section className="py-8 sm:py-12 md:py-16 lg:py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 max-w-4xl">
          <div className="prose prose-sm sm:prose-base lg:prose-lg mx-auto">
            <div className="mb-8 sm:mb-12">
              <p className="text-accessible-gray mb-3 sm:mb-4 text-sm sm:text-base">
                Last Updated: {new Date().toLocaleDateString('en-GB', { day: 'numeric', month: 'long', year: 'numeric' })}
              </p>
              <p className="mb-4 sm:mb-6 text-accessible-gray text-sm sm:text-base leading-relaxed">
                Welcome to Window Warriors. These Terms of Service ("Terms") govern your use of our website and services. 
                By accessing our website or using our services, you agree to be bound by these Terms. 
                If you do not agree to these Terms, please do not use our website or services.
              </p>
              <p className="mb-4 sm:mb-6 text-accessible-gray text-sm sm:text-base leading-relaxed">
                Please read these Terms carefully before engaging with our services. They contain important information about your legal rights, remedies, and obligations.
              </p>
            </div>

            <div className="mb-8 sm:mb-12">
              <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-3 sm:mb-4 relative inline-block">
                Acceptance of Terms
                <span className="absolute -bottom-1 sm:-bottom-2 left-0 w-12 sm:w-16 h-0.5 sm:h-1 bg-[#ec4899]/50 rounded-full"></span>
              </h2>
              <p className="mb-4 sm:mb-6 text-accessible-gray text-sm sm:text-base leading-relaxed">
                By accessing or using our website and services, you acknowledge that you have read, understood, and agree to be bound by these Terms, 
                including any additional guidelines, policies, or rules referenced herein. If you are using our services on behalf of a business or organization, 
                you represent that you have the authority to bind that entity to these Terms.
              </p>
            </div>

            <div className="mb-8 sm:mb-12">
              <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-3 sm:mb-4 relative inline-block">
                Description of Services
                <span className="absolute -bottom-1 sm:-bottom-2 left-0 w-12 sm:w-16 h-0.5 sm:h-1 bg-[#ec4899]/50 rounded-full"></span>
              </h2>
              <p className="mb-4 sm:mb-6 text-accessible-gray text-sm sm:text-base leading-relaxed">
                Window Warriors provides installation, replacement, and repair services for UPVC windows, doors, and related products. 
                We offer consultations, quotations, and professional installation services. Our website serves as an information portal about our services, 
                allowing you to request quotes, schedule consultations, and learn more about our products and services.
              </p>
            </div>

            <div className="mb-8 sm:mb-12">
              <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-3 sm:mb-4 relative inline-block">
                User Accounts
                <span className="absolute -bottom-1 sm:-bottom-2 left-0 w-12 sm:w-16 h-0.5 sm:h-1 bg-[#ec4899]/50 rounded-full"></span>
              </h2>
              <p className="mb-4 sm:mb-6 text-accessible-gray text-sm sm:text-base leading-relaxed">
                If you create an account on our website, you are responsible for maintaining the confidentiality of your account 
                and password and for restricting access to your computer or device. You agree to accept responsibility for all activities 
                that occur under your account. You must notify us immediately of any unauthorized use of your account or any other breach of security.
              </p>
            </div>

            <div className="mb-8 sm:mb-12">
              <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-3 sm:mb-4 relative inline-block">
                Quotes and Service Agreements
                <span className="absolute -bottom-1 sm:-bottom-2 left-0 w-12 sm:w-16 h-0.5 sm:h-1 bg-[#ec4899]/50 rounded-full"></span>
              </h2>
              <p className="mb-4 sm:mb-6 text-accessible-gray text-sm sm:text-base leading-relaxed">
                When you request a quote or book our services, you understand that:
              </p>
              <ul className="list-disc pl-4 sm:pl-6 mb-4 sm:mb-6 text-accessible-gray text-sm sm:text-base leading-relaxed">
                <li>All quotes are subject to a site visit and assessment by our professionals.</li>
                <li>Prices may vary based on the specific requirements of your project.</li>
                <li>Any quote provided is valid for the period specified on the quote document.</li>
                <li>A formal agreement will be created upon acceptance of a quote, which may include additional terms specific to your project.</li>
                <li>Deposits may be required to secure your booking, as detailed in your service agreement.</li>
              </ul>
            </div>

            <div className="mb-8 sm:mb-12">
              <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-3 sm:mb-4 relative inline-block">
                Payment Terms
                <span className="absolute -bottom-1 sm:-bottom-2 left-0 w-12 sm:w-16 h-0.5 sm:h-1 bg-[#ec4899]/50 rounded-full"></span>
              </h2>
              <p className="mb-4 sm:mb-6 text-accessible-gray text-sm sm:text-base leading-relaxed">
                Payment terms will be specified in your service agreement. Generally:
              </p>
              <ul className="list-disc pl-4 sm:pl-6 mb-4 sm:mb-6 text-accessible-gray text-sm sm:text-base leading-relaxed">
                <li>We may require a deposit before commencing work.</li>
                <li>Payment methods will be indicated on your invoice.</li>
                <li>Final payment is typically due upon completion of the installation or service.</li>
                <li>Late payments may incur additional charges as specified in your service agreement.</li>
              </ul>
            </div>

            <div className="mb-8 sm:mb-12">
              <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-3 sm:mb-4 relative inline-block">
                Cancellation and Refunds
                <span className="absolute -bottom-1 sm:-bottom-2 left-0 w-12 sm:w-16 h-0.5 sm:h-1 bg-[#ec4899]/50 rounded-full"></span>
              </h2>
              <p className="mb-4 sm:mb-6 text-accessible-gray text-sm sm:text-base leading-relaxed">
                Our cancellation and refund policy is as follows:
              </p>
              <ul className="list-disc pl-4 sm:pl-6 mb-4 sm:mb-6 text-accessible-gray text-sm sm:text-base leading-relaxed">
                <li>Cancellations must be made with reasonable notice, as specified in your service agreement.</li>
                <li>Deposits may be non-refundable if materials have been ordered or preparation work has begun.</li>
                <li>Refunds, if applicable, will be processed within the timeframe specified in your service agreement.</li>
                <li>Custom-ordered products may not be eligible for refunds once the order has been placed.</li>
              </ul>
              <p className="mb-4 sm:mb-6 text-accessible-gray text-sm sm:text-base leading-relaxed">
                For complete details on cancellations and refunds, please refer to your specific service agreement or contact our customer service team.
              </p>
            </div>

            <div className="mb-8 sm:mb-12">
              <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-3 sm:mb-4 relative inline-block">
                Warranties and Guarantees
                <span className="absolute -bottom-1 sm:-bottom-2 left-0 w-12 sm:w-16 h-0.5 sm:h-1 bg-[#ec4899]/50 rounded-full"></span>
              </h2>
              <p className="mb-4 sm:mb-6 text-accessible-gray text-sm sm:text-base leading-relaxed">
                We provide warranties on our products and services:
              </p>
              <ul className="list-disc pl-4 sm:pl-6 mb-4 sm:mb-6 text-accessible-gray text-sm sm:text-base leading-relaxed">
                <li>All installations come with a workmanship guarantee, as detailed in your service agreement.</li>
                <li>Product warranties vary by manufacturer and will be provided to you upon installation.</li>
                <li>To maintain your warranty, you must follow the care and maintenance instructions provided.</li>
                <li>Warranty claims should be submitted in writing to our customer service team.</li>
              </ul>
              <p className="mb-4 sm:mb-6 text-accessible-gray text-sm sm:text-base leading-relaxed">
                Specific warranty terms and conditions will be provided with your installation documents.
              </p>
            </div>

            <div className="mb-8 sm:mb-12">
              <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-3 sm:mb-4 relative inline-block">
                Intellectual Property
                <span className="absolute -bottom-1 sm:-bottom-2 left-0 w-12 sm:w-16 h-0.5 sm:h-1 bg-[#ec4899]/50 rounded-full"></span>
              </h2>
              <p className="mb-4 sm:mb-6 text-accessible-gray text-sm sm:text-base leading-relaxed">
                All content on our website, including but not limited to text, graphics, logos, images, videos, and software, 
                is the property of Window Warriors or our content suppliers and is protected by copyright, trademark, and other intellectual property laws. 
                You may not use, reproduce, distribute, or create derivative works from our content without our express written permission.
              </p>
            </div>

            <div className="mb-8 sm:mb-12">
              <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-3 sm:mb-4 relative inline-block">
                User Content
                <span className="absolute -bottom-1 sm:-bottom-2 left-0 w-12 sm:w-16 h-0.5 sm:h-1 bg-[#ec4899]/50 rounded-full"></span>
              </h2>
              <p className="mb-4 sm:mb-6 text-accessible-gray text-sm sm:text-base leading-relaxed">
                If you submit reviews, comments, or testimonials to our website, you grant us a non-exclusive, royalty-free, perpetual, irrevocable right 
                to use, reproduce, modify, adapt, publish, translate, create derivative works from, distribute, and display such content worldwide. 
                You represent that you have the rights to provide such content and that it does not violate any third-party rights.
              </p>
            </div>

            <div className="mb-8 sm:mb-12">
              <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-3 sm:mb-4 relative inline-block">
                Limitation of Liability
                <span className="absolute -bottom-1 sm:-bottom-2 left-0 w-12 sm:w-16 h-0.5 sm:h-1 bg-[#ec4899]/50 rounded-full"></span>
              </h2>
              <p className="mb-4 sm:mb-6 text-accessible-gray text-sm sm:text-base leading-relaxed">
                To the fullest extent permitted by law, Window Warriors shall not be liable for any indirect, incidental, special, consequential, or punitive damages, 
                or any loss of profits or revenues, whether incurred directly or indirectly, or any loss of data, use, goodwill, or other intangible losses, 
                resulting from (a) your use or inability to use our services; (b) any unauthorized access to or use of our servers and/or any personal information stored therein; 
                (c) any interruption or cessation of transmission to or from our services; or (d) any bugs, viruses, trojan horses, or the like that may be transmitted to or through our service.
              </p>
            </div>

            <div className="mb-8 sm:mb-12">
              <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-3 sm:mb-4 relative inline-block">
                Indemnification
                <span className="absolute -bottom-1 sm:-bottom-2 left-0 w-12 sm:w-16 h-0.5 sm:h-1 bg-[#ec4899]/50 rounded-full"></span>
              </h2>
              <p className="mb-4 sm:mb-6 text-accessible-gray text-sm sm:text-base leading-relaxed">
                You agree to defend, indemnify, and hold harmless Window Warriors, its officers, directors, employees, and agents, 
                from and against any claims, liabilities, damages, losses, and expenses, including, without limitation, reasonable legal and accounting fees, 
                arising out of or in any way connected with your access to or use of our services or your violation of these Terms.
              </p>
            </div>

            <div className="mb-8 sm:mb-12">
              <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-3 sm:mb-4 relative inline-block">
                Governing Law
                <span className="absolute -bottom-1 sm:-bottom-2 left-0 w-12 sm:w-16 h-0.5 sm:h-1 bg-[#ec4899]/50 rounded-full"></span>
              </h2>
              <p className="mb-4 sm:mb-6 text-accessible-gray text-sm sm:text-base leading-relaxed">
                These Terms shall be governed by and construed in accordance with the laws of England and Wales, 
                without regard to its conflict of law provisions. You agree to submit to the personal jurisdiction of the courts located within 
                England and Wales for the resolution of any disputes arising from these Terms or your use of our services.
              </p>
            </div>

            <div className="mb-8 sm:mb-12">
              <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-3 sm:mb-4 relative inline-block">
                Changes to Terms
                <span className="absolute -bottom-1 sm:-bottom-2 left-0 w-12 sm:w-16 h-0.5 sm:h-1 bg-[#ec4899]/50 rounded-full"></span>
              </h2>
              <p className="mb-4 sm:mb-6 text-accessible-gray text-sm sm:text-base leading-relaxed">
                We reserve the right to modify these Terms at any time. We will provide notice of any material changes by posting the updated 
                Terms on our website and updating the "Last Updated" date. Your continued use of our services after such modifications will 
                constitute your acknowledgment of the modified Terms and agreement to be bound by them.
              </p>
            </div>

            <div className="mb-8 sm:mb-12">
              <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-3 sm:mb-4 relative inline-block">
                Contact Information
                <span className="absolute -bottom-1 sm:-bottom-2 left-0 w-12 sm:w-16 h-0.5 sm:h-1 bg-[#ec4899]/50 rounded-full"></span>
              </h2>
              <p className="mb-4 sm:mb-6 text-accessible-gray text-sm sm:text-base leading-relaxed">
                If you have any questions or concerns about these Terms, please contact us at:
              </p>
              <div className="bg-gray-50 p-4 sm:p-6 rounded-lg mb-4 sm:mb-6">
                <p className="text-accessible-gray mb-2"><strong>Window Warriors</strong></p>
                <p className="text-accessible-gray mb-2">Email: <a href="mailto:<EMAIL>" className="text-[#1a3a6d] hover:text-[#ec4899] transition-colors"><EMAIL></a></p>
                <p className="text-accessible-gray mb-2">Phone: <a href="tel:+441913592774" className="text-[#1a3a6d] hover:text-[#ec4899] transition-colors">0191 359 2774</a></p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-12 sm:py-16 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-2xl sm:text-3xl font-bold text-[#1a3a6d] mb-6">Have Questions About Our Terms?</h2>
            <p className="text-accessible-gray mb-8">
              Our team is here to clarify any aspects of our terms of service. Feel free to reach out with your questions.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Link href="/contact" className="btn-primary text-center">
                Contact Us
              </Link>
              <Link href="/" className="inline-block border-2 border-[#ec4899] text-[#ec4899] hover:bg-[#ec4899] hover:text-white font-medium py-3 px-8 rounded-full transition-all duration-300 text-center">
                Back to Home
              </Link>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
} 

