import Image from "next/image";
import Link from "next/link";
import { Metadata } from 'next';
import dynamic from 'next/dynamic';

// Dynamic imports for better performance
const TestimonialCarousel = dynamic(() => import("@/components/TestimonialCarousel"), {
  loading: () => <div className="h-40 bg-gray-100 animate-pulse rounded-lg"></div>,
});

const BeforeAfterSlider = dynamic(() => import("@/components/BeforeAfterSlider"), {
  loading: () => <div className="h-60 bg-gray-100 animate-pulse rounded-lg"></div>,
});

export const metadata: Metadata = {
  title: 'Window Warriors | Professional Window & Door Services in the North East',
  description: 'Expert UPVC window and door installation in Newcastle, Durham, Sunderland & Northumberland. Energy-efficient solutions with free home visits across the North East.',
  keywords: 'UPVC windows, window installation, door replacement, conservatories, Newcastle, Durham, North East',
  openGraph: {
    title: 'Window Warriors | Premium Windows & Doors in the North East',
    description: 'Transform your home with our premium UPVC windows, doors, and conservatories. Expert installation services throughout Newcastle and the North East.',
    url: 'https://windowwarriors.uk',
    siteName: 'Window Warriors',
    images: [
      {
        url: 'https://windowwarriors.uk/images/optimized/beautiful-view-blue-lake-captured-from-inside-villa.webp',
        width: 1200,
        height: 630,
        alt: 'Window Warriors - Premium Windows & Doors',
      },
    ],
    locale: 'en_GB',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Window Warriors | Premium Windows & Doors in the North East',
    description: 'Transform your home with our premium UPVC windows, doors, and conservatories in Newcastle and the North East.',
    images: ['https://windowwarriors.uk/images/optimized/beautiful-view-blue-lake-captured-from-inside-villa.webp'],
  },
  alternates: {
    canonical: 'https://windowwarriors.uk',
  },
};

export default function Home() {
  // Mock data for benefits section
  const benefits = [
    {
      id: 1,
      title: "Energy Efficiency",
      description: "Our UPVC windows provide excellent thermal insulation, reducing heat loss and lowering your energy bills.",
      icon: (
        <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
          <path d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" />
        </svg>
      ),
    },
    {
      id: 2,
      title: "Security",
      description: "Enhanced security features including multi-point locking systems to keep your home safe and secure.",
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
        </svg>
      ),
    },
    {
      id: 3,
      title: "Low Maintenance",
      description: "UPVC windows and doors are easy to clean and require minimal maintenance compared to wooden alternatives.",
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
        </svg>
      ),
    },
  ];

  // Mock data for featured products
  const featuredProducts = [
    {
      id: "casement-windows",
      title: "Casement Windows",
      description: "Our most popular style of UPVC window, offering maximum ventilation and easy cleaning.",
      imageSrc: "/images/casement-window.jpg",
      imageAlt: "UPVC Casement Window",
      href: "/products-services#casement-windows",
      features: ["Energy efficient double glazing", "Multi-point locking system", "Various color options"],
    },
    {
      id: "upvc-doors",
      title: "UPVC Doors",
      description: "Secure, stylish and energy-efficient doors for your home or business premises.",
      imageSrc: "/images/upvc-door.jpg",
      imageAlt: "UPVC Front Door",
      href: "/products-services#upvc-doors",
      features: ["High security locks", "Weather resistant", "Low maintenance"],
    },
    {
      id: "conservatories",
      title: "Conservatories",
      description: "Create extra living space with our bespoke UPVC conservatories and orangeries.",
      imageSrc: "/images/conservatory.jpg",
      imageAlt: "UPVC Conservatory",
      href: "/products-services#conservatories",
      features: ["Custom designs", "Temperature control glass", "10-year guarantee"],
    },
  ];

  // Mock data for testimonials with better profiles
  const testimonials = [
    {
      id: 1,
      name: 'Sarah J.',
      location: 'Newcastle upon Tyne',
      text: 'Window Warriors completely transformed our home with their beautiful windows. The quality is outstanding, and their team was professional and efficient throughout the entire process.',
      image: '/images/male-worker-factory.jpg', // Using available image
      rating: 5,
      position: 'Homeowner'
    },
    {
      id: 2,
      name: 'James W.',
      location: 'Durham',
      text: 'We couldn\'t be happier with our new conservatory. It\'s become our favorite room in the house! The team at Window Warriors were excellent from start to finish.',
      image: '/images/male-worker-factory.jpg', // Using available image
      rating: 5,
      position: 'Property Developer'
    },
    {
      id: 3,
      name: 'Emma T.',
      location: 'Sunderland',
      text: 'After getting quotes from several companies, Window Warriors offered the best value without compromising on quality. Our new front door looks fantastic and feels so secure.',
      image: '/images/male-worker-factory.jpg', // Using available image
      rating: 5,
      position: 'Interior Designer'
    },
  ];

  // Services data
  const services = [
    {
      title: "UPVC Windows",
      description: "Energy efficient windows with superior insulation, designed to enhance your home's comfort and value.",
      image: "/images/optimized/beautiful-view-blue-lake-captured-from-inside-villa.webp", // Beautiful UPVC windows with lake view
      link: "/products-services#windows"
    },
    {
      title: "Residential Doors",
      description: "Secure, stylish entry doors with modern locking systems for enhanced protection and curb appeal.",
      image: "/images/optimized/decorated-front-door-with-plant.webp", // Decorated front door with plant
      link: "/products-services#doors"
    },
    {
      title: "Conservatories",
      description: "Bespoke glass extensions that create bright, versatile living spaces to enjoy year-round.",
      image: "/images/optimized/beautiful-hotel-insights-details.webp", // Beautiful hotel interior with large windows
      link: "/products-services#conservatories"
    },
    {
      title: "Window Repairs",
      description: "Comprehensive repair services to fix damaged windows and restore optimal performance.",
      image: "/images/optimized/ancient-window-old-building-quebec-city.webp", // Window repair/replacement image
      link: "/products-services#repairs"
    }
  ];

  // Features data with more specific window/door related benefits
  const features = [
    {
      title: "Premium UPVC Materials",
      description: "We use only the highest grade UPVC materials that won't fade, warp or deteriorate over time.",
      icon: (props: any) => (
        <svg {...props} fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
        </svg>
      )
    },
    {
      title: "A+ Rated Energy Efficiency",
      description: "Our windows exceed energy rating standards, significantly reducing your heating costs year-round.",
      icon: (props: any) => (
        <svg {...props} fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      )
    },
    {
      title: "10-Year Comprehensive Warranty",
      description: "Every installation is backed by our industry-leading warranty for complete peace of mind.",
      icon: (props: any) => (
        <svg {...props} fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      )
    },
    {
      title: "Expert Installation Teams",
      description: "Our certified installation teams have an average of 15+ years experience in the industry.",
      icon: (props: any) => (
        <svg {...props} fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
      )
    }
  ];

  return (
    <main className="flex min-h-screen flex-col">
      {/* Hero Section - Mobile-First Responsive Design */}
      <section className="relative min-h-screen overflow-hidden">
        {/* Optimized background image */}
        <div className="absolute inset-0">
          <Image
            src="/images/optimized/beautiful-view-blue-lake-captured-from-inside-villa.webp"
            alt="Beautiful window view"
            fill
            priority
            className="object-cover object-center"
            sizes="(max-width: 768px) 100vw, 100vw"
            quality={80}
            placeholder="blur"
            blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
          />
          {/* Enhanced gradient overlay with pink accent */}
          <div className="absolute inset-0 bg-gradient-to-br from-[#1a3a6d]/90 via-[#1a3a6d]/80 to-[#ec4899]/30"></div>
          <div className="absolute inset-0 bg-gradient-to-t from-[#0f172a]/60 via-transparent to-transparent"></div>
        </div>
        
        {/* Beautiful floating elements and particles - Mobile optimized */}
        <div className="absolute inset-0 overflow-hidden">
          {/* Main pink blur elements - Responsive sizes with reduced complexity on mobile */}
          <div className="absolute top-1/6 left-1/6 w-20 sm:w-48 md:w-64 lg:w-80 h-20 sm:h-48 md:h-64 lg:h-80 bg-[#ec4899]/20 rounded-full blur-xl sm:blur-3xl animate-float-slow"></div>
          <div className="absolute bottom-1/6 right-1/6 w-24 sm:w-64 md:w-80 lg:w-[30rem] h-24 sm:h-64 md:h-80 lg:h-[30rem] bg-[#d946ef]/15 rounded-full blur-xl sm:blur-3xl animate-float-slow" style={{animationDelay: '2s'}}></div>
          <div className="hidden sm:block absolute top-1/2 left-1/2 w-16 sm:w-40 md:w-56 lg:w-64 h-16 sm:h-40 md:h-56 lg:h-64 bg-[#f9a8d4]/10 rounded-full blur-xl sm:blur-3xl animate-float-slow" style={{animationDelay: '4s'}}></div>
          
          {/* Floating glass particles - Progressive enhancement */}
          <div className="hidden sm:block absolute top-[15%] right-[25%] w-4 sm:w-6 h-4 sm:h-6 bg-white/20 rounded-full backdrop-blur-sm animate-float-1"></div>
          <div className="hidden sm:block absolute top-[35%] right-[15%] w-3 sm:w-4 h-3 sm:h-4 bg-[#ec4899]/30 rounded-full backdrop-blur-sm animate-float-2"></div>
          <div className="hidden md:block absolute bottom-[40%] left-[20%] w-6 sm:w-8 h-6 sm:h-8 bg-[#d946ef]/20 rounded-full backdrop-blur-sm animate-float-3"></div>
          <div className="hidden md:block absolute bottom-[25%] left-[10%] w-4 sm:w-5 h-4 sm:h-5 bg-white/15 rounded-full backdrop-blur-sm animate-float-1" style={{animationDelay: '1s'}}></div>
          
          {/* Geometric pink accents - Hidden on mobile for performance */}
          <div className="hidden xl:block absolute top-[20%] left-[15%] w-16 h-16 border border-[#ec4899]/30 rounded-full animate-pulse-slow"></div>
          <div className="hidden xl:block absolute bottom-[30%] right-[20%] w-12 h-12 border border-[#d946ef]/20 rounded-full animate-pulse-slow" style={{animationDelay: '1.5s'}}></div>
        </div>
        
        {/* Hero content with proper mobile spacing */}
        <div className="container mx-auto px-4 sm:px-6 relative z-10 flex flex-col justify-center min-h-screen pt-16 sm:pt-20 md:pt-24 lg:pt-28 pb-16 sm:pb-20 md:pb-24">
          <div className="max-w-4xl animate-fade-in" style={{ animationDelay: '0.3s' }}>
            {/* Enhanced brand tag with pink glow - Mobile optimized */}
            <div className="inline-flex items-center mb-4 sm:mb-6">
              <span className="h-px w-4 sm:w-8 md:w-12 bg-gradient-to-r from-transparent to-[#ec4899] mr-2 sm:mr-3"></span>
              <span className="text-accessible-light text-xs sm:text-sm md:text-base font-medium uppercase tracking-[0.1em] sm:tracking-[0.2em] bg-[#ec4899]/10 backdrop-blur-sm px-3 sm:px-4 py-1.5 sm:py-2 rounded-full border border-[#ec4899]/30 animate-fade-blur" style={{ animationDelay: '0.2s' }}>
                WINDOW WARRIORS
              </span>
              <span className="h-px w-4 sm:w-8 md:w-12 bg-gradient-to-l from-transparent to-[#ec4899] ml-2 sm:ml-3"></span>
            </div>
            
            {/* Enhanced main heading with mobile-optimized sizing */}
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold mb-4 sm:mb-6 leading-tight">
              <span className="text-white block sm:inline">Transform Your </span>
              <span className="relative inline-block">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-[#ec4899] via-[#d946ef] to-[#f9a8d4]">
                  Home
                </span>
                {/* Pink glow effect under text - Mobile optimized */}
                <div className="absolute -bottom-1 sm:-bottom-2 left-0 right-0 h-0.5 sm:h-1 bg-gradient-to-r from-[#ec4899]/60 via-[#d946ef]/80 to-[#ec4899]/60 rounded-full blur-sm animate-pulse"></div>
              </span>
            </h1>
            
            <h2 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bold text-white mb-6 sm:mb-8">
              With <span className="text-gradient-accent">Premium Windows</span> & Doors
            </h2>
            
            {/* Enhanced description with mobile-optimized sizing */}
            <p className="text-sm sm:text-base md:text-lg lg:text-xl text-accessible-light mb-6 sm:mb-8 leading-relaxed animate-slide-up max-w-2xl" style={{ animationDelay: '0.6s' }}>
              Expert installation, repair, and replacement services across 
              <span className="text-[#f9a8d4] font-medium block sm:inline"> Newcastle, Durham, Sunderland</span> and Northumberland.
            </p>
            
            {/* Beautiful CTA buttons with mobile-optimized spacing */}
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 md:gap-6 animate-slide-up mb-6 sm:mb-8" style={{ animationDelay: '0.9s' }}>
              <Link href="/products-services#quote" className="group relative overflow-hidden bg-gradient-to-r from-[#ec4899] to-[#be185d] text-white font-semibold px-6 sm:px-8 py-3 sm:py-4 rounded-full transition-all duration-300 transform hover:-translate-y-1 hover:shadow-2xl hover:shadow-[#ec4899]/30 text-center text-sm sm:text-base">
                <span className="relative z-10 flex items-center justify-center gap-2">
                  <span>Get Your Free Quote</span>
                  <svg className="w-4 sm:w-5 h-4 sm:h-5 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                  </svg>
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-[#d946ef] to-[#ec4899] opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
              </Link>
              
              <Link href="/products-services" className="group relative overflow-hidden bg-white/80 backdrop-blur-sm border-2 border-white/30 text-white font-medium px-6 sm:px-8 py-3 sm:py-4 rounded-full transition-all duration-300 transform hover:-translate-y-1 hover:bg-white/20 hover:border-[#ec4899]/50 text-center text-sm sm:text-base">
                <span className="flex items-center justify-center gap-2">
                  <span>Explore Our Services</span>
                  <svg className="w-4 sm:w-5 h-4 sm:h-5 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </span>
              </Link>
            </div>
            
            {/* Trust badges with mobile-optimized layout */}
            <div className="flex justify-start animate-fade-in" style={{ animationDelay: '1.2s' }}>
              <div className="flex flex-col items-start sm:items-center">
                <div className="flex items-center justify-center mb-2 sm:mb-3">
                  <div className="h-px w-4 sm:w-8 bg-gradient-to-r from-transparent to-[#ec4899]/50"></div>
                  <span className="text-accessible-muted text-xs mx-2 font-medium">Trusted</span>
                  <div className="h-px w-4 sm:w-8 bg-gradient-to-l from-transparent to-[#ec4899]/50"></div>
                </div>
                
                <div className="flex justify-start sm:justify-center items-center space-x-2 sm:space-x-3">
                  <div className="group relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-[#ec4899]/20 to-[#d946ef]/20 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="relative bg-white/90 backdrop-blur-sm rounded-full p-1 sm:p-1.5 h-8 w-8 sm:h-10 sm:w-10 flex items-center justify-center overflow-hidden border border-white/20 hover:border-[#ec4899]/30 transition-all duration-300 hover:scale-105">
                      <Image 
                        src="/halo.jpeg" 
                        alt="Halo Certification" 
                        width={20} 
                        height={20}
                        className="object-contain"
                      />
                    </div>
                  </div>
                  
                  <div className="group relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-[#ec4899]/20 to-[#d946ef]/20 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="relative bg-white/90 backdrop-blur-sm rounded-full p-1 sm:p-1.5 h-8 w-8 sm:h-10 sm:w-10 flex items-center justify-center overflow-hidden border border-white/20 hover:border-[#ec4899]/30 transition-all duration-300 hover:scale-105">
                      <Image 
                        src="/veka.jpeg" 
                        alt="Veka Certification" 
                        width={20} 
                        height={20}
                        className="object-contain"
                      />
                    </div>
                  </div>
                  
                  <div className="group relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-[#ec4899]/20 to-[#d946ef]/20 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="relative bg-white/90 backdrop-blur-sm rounded-full p-1 sm:p-1.5 h-8 w-8 sm:h-10 sm:w-10 flex items-center justify-center overflow-hidden border border-white/20 hover:border-[#ec4899]/30 transition-all duration-300 hover:scale-105">
                      <Image 
                        src="/Assure Certified Logo 2.jpg" 
                        alt="Assure Certified" 
                        width={20} 
                        height={20}
                        className="object-contain"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Floating glass cards - Only on extra large screens */}
          <div className="hidden 2xl:flex absolute top-1/2 right-8 transform -translate-y-1/2 flex-col gap-4 z-10">
            <div className="group relative">
              <div className="absolute -inset-1 bg-gradient-to-r from-[#ec4899]/20 to-[#d946ef]/20 rounded-xl blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="relative bg-white/10 backdrop-blur-lg border border-white/20 rounded-xl p-4 flex flex-col items-center transition-all duration-500 hover:-translate-y-1 hover:bg-white/15 hover:border-[#ec4899]/30 animate-float-1" style={{ minWidth: '140px' }}>
                <div className="text-xl font-bold bg-gradient-to-r from-[#ec4899] to-[#d946ef] bg-clip-text text-transparent mb-1">15+</div>
                <div className="text-accessible-light font-medium text-center text-xs">Years Experience</div>
              </div>
            </div>
            
            <div className="group relative">
              <div className="absolute -inset-1 bg-gradient-to-r from-[#d946ef]/20 to-[#ec4899]/20 rounded-xl blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="relative bg-white/10 backdrop-blur-lg border border-white/20 rounded-xl p-4 flex flex-col items-center transition-all duration-500 hover:-translate-y-1 hover:bg-white/15 hover:border-[#ec4899]/30 animate-float-2" style={{ minWidth: '140px' }}>
                <div className="text-xl font-bold bg-gradient-to-r from-[#d946ef] to-[#f9a8d4] bg-clip-text text-transparent mb-1">2,500+</div>
                <div className="text-accessible-light font-medium text-center text-xs">Projects Completed</div>
              </div>
            </div>
            
            <div className="group relative">
              <div className="absolute -inset-1 bg-gradient-to-r from-[#f9a8d4]/20 to-[#ec4899]/20 rounded-xl blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="relative bg-white/10 backdrop-blur-lg border border-white/20 rounded-xl p-4 flex flex-col items-center transition-all duration-500 hover:-translate-y-1 hover:bg-white/15 hover:border-[#ec4899]/30 animate-float-3" style={{ minWidth: '140px' }}>
                <div className="text-xl font-bold bg-gradient-to-r from-[#f9a8d4] to-[#ec4899] bg-clip-text text-transparent mb-1">4.9</div>
                <div className="text-accessible-light font-medium text-center text-xs">Customer Rating</div>
              </div>
            </div>
          </div>
          
          {/* Enhanced scroll indicator - Mobile optimized */}
          <div className="absolute bottom-4 sm:bottom-6 left-1/2 transform -translate-x-1/2 z-30">
            <div className="group flex flex-col items-center animate-bounce-slow">
              <div className="bg-[#ec4899]/10 backdrop-blur-sm border border-[#ec4899]/30 rounded-full p-1.5 sm:p-2 group-hover:bg-[#ec4899]/20 transition-all duration-300">
                <svg width="12" height="12" className="sm:w-4 sm:h-4 text-white group-hover:text-[#f9a8d4] transition-colors duration-300" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 4V20M12 20L18 14M12 20L6 14" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Key stats section (mobile only) */}
      <section className="lg:hidden py-10 sm:py-16 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 sm:gap-6">
            <div className="glass-card p-6 sm:p-8 flex flex-col items-center">
              <div className="text-3xl sm:text-4xl font-bold text-gradient-accent mb-2">15+</div>
              <div className="text-[#1a3a6d] font-medium text-sm sm:text-base">Years Experience</div>
            </div>
            <div className="glass-card p-6 sm:p-8 flex flex-col items-center">
              <div className="text-3xl sm:text-4xl font-bold text-gradient-accent mb-2">2,500+</div>
              <div className="text-[#1a3a6d] font-medium text-sm sm:text-base">Projects Completed</div>
            </div>
            <div className="glass-card p-6 sm:p-8 flex flex-col items-center sm:col-span-2 md:col-span-1 sm:mx-auto">
              <div className="text-3xl sm:text-4xl font-bold text-gradient-accent mb-2">4.9</div>
              <div className="text-[#1a3a6d] font-medium text-sm sm:text-base">Customer Rating</div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Story Section - Mobile Optimized */}
      <section className="py-12 sm:py-16 md:py-24 lg:py-32 bg-white relative overflow-hidden">
        {/* Mobile-optimized background decorative elements */}
        <div className="absolute top-0 left-0 w-32 sm:w-48 md:w-64 h-32 sm:h-48 md:h-64 bg-[#ec4899]/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-48 sm:w-72 md:w-96 h-48 sm:h-72 md:h-96 bg-[#1a3a6d]/20 rounded-full blur-3xl"></div>
        
        {/* Decorative background elements - Progressive enhancement */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/4 left-1/4 w-24 sm:w-40 md:w-64 h-24 sm:h-40 md:h-64 bg-[#ec4899]/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/4 right-1/4 w-32 sm:w-64 md:w-96 h-32 sm:h-64 md:h-96 bg-[#1a3a6d]/20 rounded-full blur-3xl"></div>
          
          {/* Floating elements - Hidden on mobile */}
          <div className="hidden md:block absolute top-[20%] right-[20%] w-20 lg:w-24 h-20 lg:h-24 circle-decoration opacity-40"></div>
          <div className="hidden md:block absolute bottom-[30%] left-[15%] w-14 lg:w-16 h-14 lg:h-16 circle-decoration opacity-30" style={{animationDelay: '1s'}}></div>
        </div>
        
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center mb-8 sm:mb-12 md:mb-16">
            <div className="inline-flex items-center justify-center mb-3 sm:mb-4">
              <span className="h-px w-6 sm:w-8 md:w-10 bg-[#ec4899]"></span>
              <span className="text-[#ec4899] font-medium mx-2 sm:mx-3 md:mx-4 uppercase tracking-wider text-xs sm:text-sm">Our Mission</span>
              <span className="h-px w-6 sm:w-8 md:w-10 bg-[#ec4899]"></span>
            </div>
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-[#1a3a6d] mb-4 sm:mb-6 leading-tight px-4 sm:px-0">
              We're Not Just Fitting Windows — <br className="hidden sm:block" />
              We're <span className="text-gradient-premium relative">
                Rebuilding Trust
                <span className="absolute bottom-0 left-0 w-full h-0.5 sm:h-1 bg-[#ec4899]/70 rounded-full animate-shimmer"></span>
              </span>
            </h2>
          </div>
          
          <div className="max-w-4xl mx-auto mb-8 sm:mb-12 md:mb-16">
            <div className="glass-card p-6 sm:p-8 md:p-10 rounded-xl sm:rounded-2xl shadow-xl relative overflow-hidden premium-card-glow border border-gray-200">
              {/* Subtle background pattern */}
              <div className="absolute inset-0 bg-pattern opacity-5"></div>
              
              <p className="text-base sm:text-lg md:text-xl text-[#1a3a6d] mb-4 sm:mb-6 font-medium">
                Window Warriors wasn't built to be another glazing company.
              </p>
              
              <p className="text-sm sm:text-base md:text-lg text-gray-700 mb-4 sm:mb-6 leading-relaxed">
                We started this business because we were tired — tired of hearing story after story of homeowners being pushed, pressured, or completely let down.
                <br />Tired of seeing good people overpay for bad work.
                <br />Tired of trades showing up late (if at all), rushing through jobs, leaving messes behind, or worse — disappearing once the invoice was paid.
              </p>
              
              <p className="text-sm sm:text-base md:text-lg text-gray-700 mb-4 sm:mb-6 leading-relaxed">
                We thought:
                <br />There has to be a better way.
                <br />So we built it.
              </p>
              
              <div className="w-full h-px bg-gradient-to-r from-transparent via-[#ec4899]/50 to-transparent my-6 sm:my-8 md:my-10"></div>
              
              <p className="text-base sm:text-lg md:text-xl text-[#1a3a6d] mb-4 sm:mb-6 font-medium">
                This is our way:
              </p>
              
              <ul className="space-y-3 sm:space-y-4 mb-6 sm:mb-8 md:mb-10">
                <li className="flex items-start">
                  <div className="flex-shrink-0 w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 bg-[#ec4899]/20 rounded-full flex items-center justify-center mt-0.5 mr-2 sm:mr-3 md:mr-4">
                    <div className="w-1 h-1 sm:w-1.5 sm:h-1.5 md:w-2 md:h-2 bg-[#ec4899] rounded-full"></div>
                  </div>
                  <p className="text-sm sm:text-base md:text-lg text-gray-700">Where honest advice beats sales pressure.</p>
                </li>
                <li className="flex items-start">
                  <div className="flex-shrink-0 w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 bg-[#ec4899]/20 rounded-full flex items-center justify-center mt-0.5 mr-2 sm:mr-3 md:mr-4">
                    <div className="w-1 h-1 sm:w-1.5 sm:h-1.5 md:w-2 md:h-2 bg-[#ec4899] rounded-full"></div>
                  </div>
                  <p className="text-sm sm:text-base md:text-lg text-gray-700">Where your home is treated like it's ours.</p>
                </li>
                <li className="flex items-start">
                  <div className="flex-shrink-0 w-5 h-5 sm:w-6 sm:h-6 bg-[#ec4899]/20 rounded-full flex items-center justify-center mt-0.5 mr-3 sm:mr-4">
                    <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-[#ec4899] rounded-full"></div>
                  </div>
                  <p className="text-base sm:text-lg text-gray-700">Where cups of tea and conversation matter just as much as the quote.</p>
                </li>
                <li className="flex items-start">
                  <div className="flex-shrink-0 w-5 h-5 sm:w-6 sm:h-6 bg-[#ec4899]/20 rounded-full flex items-center justify-center mt-0.5 mr-3 sm:mr-4">
                    <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-[#ec4899] rounded-full"></div>
                  </div>
                  <p className="text-base sm:text-lg text-gray-700">Where small things — like turning up on time, cleaning up after ourselves, and calling when we say we will — aren't small at all.</p>
                </li>
              </ul>
              
              <p className="text-base sm:text-lg text-gray-700 mb-8">
                We're a team of real people, from the North East and proud of it — no suits, no sales scripts, just genuine folk who care about doing the job properly.
                <br /><br />
                We're not in it for fast sales. We're in it for long-term trust.
                <br /><br />
                Because we believe every homeowner deserves warmth, safety, and confidence in the people they hire — without the headache.
              </p>
              
              <div className="w-full h-px bg-gradient-to-r from-transparent via-[#ec4899]/50 to-transparent my-8 sm:my-10"></div>
              
              <p className="text-lg sm:text-xl text-[#1a3a6d] mb-6 font-medium">
                Our values are simple:
              </p>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 mb-8 sm:mb-10">
                <div className="bg-white/5 backdrop-blur-lg p-6 rounded-xl border border-white/20 transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:border-white/30">
                  <h3 className="text-[#1a3a6d] font-bold mb-2 flex items-center">
                    <span className="w-6 h-6 bg-[#ec4899]/20 rounded-full flex items-center justify-center mr-2">
                      <span className="w-1.5 h-1.5 bg-[#ec4899] rounded-full"></span>
                    </span>
                    People first. Always.
                  </h3>
                </div>
                <div className="bg-white/5 backdrop-blur-lg p-6 rounded-xl border border-white/20 transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:border-white/30">
                  <h3 className="text-[#1a3a6d] font-bold mb-2 flex items-center">
                    <span className="w-6 h-6 bg-[#ec4899]/20 rounded-full flex items-center justify-center mr-2">
                      <span className="w-1.5 h-1.5 bg-[#ec4899] rounded-full"></span>
                    </span>
                    Be real. Say what we mean.
                  </h3>
                </div>
                <div className="bg-white/5 backdrop-blur-lg p-6 rounded-xl border border-white/20 transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:border-white/30">
                  <h3 className="text-[#1a3a6d] font-bold mb-2 flex items-center">
                    <span className="w-6 h-6 bg-[#ec4899]/20 rounded-full flex items-center justify-center mr-2">
                      <span className="w-1.5 h-1.5 bg-[#ec4899] rounded-full"></span>
                    </span>
                    Work hard. Do it right.
                  </h3>
                </div>
                <div className="bg-white/5 backdrop-blur-lg p-6 rounded-xl border border-white/20 transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:border-white/30">
                  <h3 className="text-[#1a3a6d] font-bold mb-2 flex items-center">
                    <span className="w-6 h-6 bg-[#ec4899]/20 rounded-full flex items-center justify-center mr-2">
                      <span className="w-1.5 h-1.5 bg-[#ec4899] rounded-full"></span>
                    </span>
                    Leave it better.
                  </h3>
                </div>
              </div>
              
              <div className="w-full h-px bg-gradient-to-r from-transparent via-[#ec4899]/50 to-transparent my-8 sm:my-10"></div>
              
              <p className="text-base sm:text-lg text-gray-700 mb-6">
                Window Warriors isn't just a name — it's a promise.
                <br />A promise to stand up for quality. For fairness. For the people on the other side of the front door.
              </p>
              
              <p className="text-base sm:text-lg text-gray-700 mb-6">
                And if that sounds a bit bold?
                <br />Good.
                <br />We'd rather be bold than basic.
              </p>
              
              <p className="text-lg sm:text-xl text-[#1a3a6d] font-medium">
                You've got enough to worry about. Your windows shouldn't be one of them.
              </p>
            </div>
          </div>
          
          <div className="text-center">
            <Link href="/about" className="inline-flex items-center gap-2 sm:gap-3 px-6 sm:px-8 py-3 sm:py-4 bg-[#ec4899] hover:bg-[#be185d] text-white font-medium rounded-full transition-all duration-300 shadow-lg shadow-[#ec4899]/20 group btn-premium text-sm sm:text-base">
              <span>Learn More About Us</span>
              <svg className="w-4 h-4 sm:w-5 sm:h-5 transform transition-all duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
              </svg>
            </Link>
          </div>
        </div>
      </section>

      {/* Services Section with premium 3D layout */}
      <section className="py-16 sm:py-24 md:py-32 bg-gray-50 relative overflow-hidden">
        {/* 3D effect background */}
        <div className="absolute inset-0 perspective-1000">
          <div className="absolute inset-0 transform-3d rotate-x-12 scale-110 opacity-5">
            <div className="absolute inset-0 bg-grid-pattern"></div>
          </div>
        </div>
        
        {/* Background effects */}
                  <div className="absolute inset-0 opacity-20">
            <div className="absolute -top-20 -right-20 w-96 h-96 rounded-full bg-[#ec4899]/10 blur-3xl"></div>
            <div className="absolute bottom-0 left-0 w-[30rem] h-[30rem] rounded-full bg-[#ec4899]/5 blur-3xl"></div>
          </div>
        
        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          <div className="text-center mb-12 sm:mb-16 md:mb-24">
            <div className="inline-flex items-center justify-center mb-4">
              <span className="h-px w-8 sm:w-10 bg-[#ec4899]"></span>
              <span className="text-[#ec4899] font-medium mx-3 sm:mx-4 uppercase tracking-wider text-xs sm:text-sm">Our Solutions</span>
              <span className="h-px w-8 sm:w-10 bg-[#ec4899]"></span>
            </div>
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-[#1a3a6d] mb-4 sm:mb-6 leading-tight">
              Premium Window & Door <span className="text-gradient-accent">Craftsmanship</span>
            </h2>
            <p className="text-base sm:text-lg text-gray-700 max-w-2xl mx-auto">
              Discover our range of custom-designed windows and doors, meticulously crafted to enhance your home's aesthetics and performance.
            </p>
          </div>
          
          {/* 3D Service Layout */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8 md:gap-x-8 md:gap-y-20 perspective-1000">
            {services.map((service, index) => (
              <div 
                key={service.title} 
                className="group relative transform transition-all duration-700 hover:-translate-y-4 premium-card-glow"
                style={{ transformStyle: 'preserve-3d', animationDelay: `${index * 150}ms` }}
              >
                {/* 3D Card with Glow */}
                <div className="relative h-[300px] sm:h-[350px] md:h-[400px] rounded-2xl overflow-hidden shadow-2xl" 
                  style={{ 
                    transformStyle: 'preserve-3d',
                    transform: 'translateZ(0)', 
                    transition: 'transform 0.5s ease-out'
                  }}
                >
                  {/* Glow effect */}
                  <div className="absolute -inset-0.5 bg-gradient-to-tr from-[#ec4899]/30 to-[#1a3a6d]/30 rounded-2xl blur opacity-0 group-hover:opacity-100 transition-opacity duration-700 -z-10"></div>
                  
                  {/* Image Layer */}
                  <div className="absolute inset-0 overflow-hidden">
                    <Image 
                      src={service.image} 
                      alt={service.title} 
                      fill 
                      className="object-cover object-center transition-transform duration-700 group-hover:scale-110"
                    />
                    {/* Gradient overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-[#0b1423]/95 via-[#0b1423]/60 to-transparent"></div>
                  </div>
                  
                  {/* Content Layer with 3D effect */}
                  <div className="absolute inset-x-0 bottom-0 p-4 sm:p-6 md:p-8 text-white" style={{ transform: 'translateZ(30px)' }}>
                    <div className="transition-all duration-500 transform group-hover:-translate-y-2">
                      <h3 className="text-xl sm:text-2xl font-bold mb-2 sm:mb-4 relative">
                        {service.title}
                        <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#ec4899] transition-all duration-500 group-hover:w-full"></span>
                      </h3>
                      <p className="text-sm sm:text-base text-accessible-muted mb-4 sm:mb-6 line-clamp-3 transform transition-all duration-500 group-hover:line-clamp-none">
                        {service.description}
                      </p>
                    </div>
                    
                    <Link 
                      href={service.link} 
                      className="inline-flex items-center text-[#ec4899] font-medium transition-all duration-500 group-hover:pl-2 text-sm sm:text-base"
                    >
                      <span>Explore</span>
                      <svg className="w-4 h-4 sm:w-5 sm:h-5 ml-2 transform transition-all duration-500 group-hover:translate-x-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                      </svg>
                    </Link>
                  </div>
                  
                  {/* Premium corner accent */}
                  <div className="absolute top-0 right-0 w-12 sm:w-16 h-12 sm:h-16 overflow-hidden">
                    <div className="absolute top-0 right-0 transform rotate-45 translate-x-1/2 -translate-y-1/2 w-12 sm:w-16 h-2 bg-gradient-to-r from-[#ec4899] to-[#be185d]"></div>
                  </div>
                </div>
                
                {/* 3D shadow */}
                <div className="absolute -bottom-6 inset-x-4 h-4 rounded-full bg-black/20 blur-md transform -translate-z-10 translate-y-2 opacity-0 transition-opacity duration-500 group-hover:opacity-100"></div>
              </div>
            ))}
          </div>

          <div className="mt-12 sm:mt-16 md:mt-24 text-center">
            <Link href="/products-services" className="inline-flex items-center gap-2 sm:gap-3 px-6 sm:px-8 py-3 sm:py-4 bg-white hover:bg-gray-50 text-[#1a3a6d] border border-white/20 rounded-full transition-all duration-300 hover:shadow-lg hover:shadow-[#ec4899]/10 group hover-lift text-sm sm:text-base">
              <span>Browse All Products</span>
              <svg className="w-4 h-4 sm:w-5 sm:h-5 transform transition-all duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
              </svg>
            </Link>
          </div>
        </div>
      </section>

      {/* Before/After Comparison with architecture focus */}
      <section className="py-16 sm:py-24 md:py-32 bg-white relative">
        {/* Accent lines - hide on mobile */}
        <div className="hidden sm:block absolute left-0 top-0 h-full w-1/6 pointer-events-none">
          <div className="absolute left-[10%] top-0 h-full w-px bg-gradient-to-b from-transparent via-[#ec4899]/20 to-transparent"></div>
          <div className="absolute left-[60%] top-0 h-full w-px bg-gradient-to-b from-transparent via-[#ec4899]/10 to-transparent"></div>
        </div>
        <div className="hidden sm:block absolute right-0 top-0 h-full w-1/6 pointer-events-none">
          <div className="absolute right-[10%] top-0 h-full w-px bg-gradient-to-b from-transparent via-[#ec4899]/20 to-transparent"></div>
          <div className="absolute right-[60%] top-0 h-full w-px bg-gradient-to-b from-transparent via-[#ec4899]/10 to-transparent"></div>
        </div>
        
        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          <div className="flex flex-col lg:flex-row gap-8 sm:gap-12 md:gap-16 items-center">
            <div className="lg:w-2/5">
              <div className="inline-flex items-center mb-4">
                <span className="h-px w-6 sm:w-8 md:w-10 bg-[#ec4899]"></span>
                <span className="text-[#ec4899] font-medium ml-3 sm:ml-4 uppercase tracking-wider text-xs sm:text-sm">Before & After</span>
              </div>
              <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-[#1a3a6d] mb-4 sm:mb-6 leading-tight">
                Home <br/><span className="text-gradient-accent">Transformations</span>
              </h2>
              <p className="text-base sm:text-lg text-gray-700 mb-6 sm:mb-8">
                See the remarkable difference our premium windows and doors make to properties across the North East.
                Our installations don't just improve aesthetics—they enhance energy efficiency, security, and value.
              </p>
              
              <div className="space-y-4 sm:space-y-6 mb-6 sm:mb-10">
                <div className="flex items-start">
                  <div className="bg-[#ec4899]/20 rounded-full p-1.5 sm:p-2 mr-3 sm:mr-4">
                    <svg className="w-5 h-5 sm:w-6 sm:h-6 text-[#ec4899]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-base sm:text-lg font-medium text-[#1a3a6d] mb-0.5 sm:mb-1">Increased Property Value</h3>
                    <p className="text-sm sm:text-base text-gray-600">Typically adds 5-10% to your home's market value</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="bg-[#ec4899]/20 rounded-full p-1.5 sm:p-2 mr-3 sm:mr-4">
                    <svg className="w-5 h-5 sm:w-6 sm:h-6 text-[#ec4899]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-base sm:text-lg font-medium text-[#1a3a6d] mb-0.5 sm:mb-1">Energy Savings</h3>
                    <p className="text-sm sm:text-base text-gray-600">Cut heating costs by up to 25% with our A-rated windows</p>
                  </div>
                </div>
              </div>
              
              <Link href="/gallery" className="inline-flex items-center gap-2 sm:gap-3 px-6 sm:px-8 py-3 sm:py-4 bg-[#ec4899] hover:bg-[#be185d] text-white font-medium rounded-full transition-all duration-300 shadow-lg shadow-[#ec4899]/20 group btn-premium text-sm sm:text-base">
                <span>View Gallery</span>
                <svg className="w-4 h-4 sm:w-5 sm:h-5 transform transition-all duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                </svg>
              </Link>
            </div>
            
            <div className="lg:w-3/5 relative mt-8 lg:mt-0 overflow-hidden">
              {/* Decorative frame - smaller on mobile, constrained */}
              <div className="absolute -top-2 sm:-top-3 md:-top-6 -bottom-2 sm:-bottom-3 md:-bottom-6 -right-2 sm:-right-3 md:-right-6 -left-2 sm:-left-3 md:-left-6 border-2 border-[#ec4899]/20 rounded-xl"></div>
              
              <div className="rounded-lg overflow-hidden shadow-2xl relative z-10 max-w-full">
                          <BeforeAfterSlider 
            beforeImage="/images/optimized/old-brick-house-paved-street.webp" // Old brick house with windows
            afterImage="/images/optimized/exterior-home.webp" // Renovated home with modern windows
                  beforeAlt="Home with old windows" 
                  afterAlt="Home with new UPVC windows"
                />
              </div>
              
              {/* Floating accent elements - constrained and smaller on mobile */}
              <div className="absolute -top-4 sm:-top-6 md:-top-8 -right-2 sm:-right-4 md:-right-6 w-8 sm:w-12 md:w-16 lg:w-20 h-8 sm:h-12 md:h-16 lg:h-20 bg-[#ec4899]/10 rounded-full blur-lg"></div>
              <div className="absolute -bottom-4 sm:-bottom-6 md:-bottom-8 -left-2 sm:-left-4 md:-left-6 w-12 sm:w-16 md:w-24 lg:w-32 h-12 sm:h-16 md:h-24 lg:h-32 bg-[#1a3a6d]/30 rounded-full blur-xl"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section - Mobile Optimized */}
      <section className="py-12 sm:py-16 md:py-24 lg:py-32 bg-gray-50 relative overflow-hidden">
        {/* Diagonal decorative element - Mobile responsive */}
        <div className="absolute top-0 left-0 right-0 h-12 sm:h-16 md:h-24 bg-[#ec4899]" style={{ clipPath: 'polygon(0 0, 100% 0, 100% 100%, 0 0)' }}></div>
        
        {/* Background elements */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-full h-full opacity-5" style={{ 
            backgroundImage: 'radial-gradient(#ffffff 1px, transparent 1px)', 
            backgroundSize: '20px 20px'
          }}></div>
          <div className="absolute top-1/4 right-1/4 w-[40rem] h-[40rem] rounded-full bg-[#ec4899]/5 blur-3xl"></div>
          <div className="absolute bottom-1/3 left-1/6 w-96 h-96 rounded-full bg-[#d946ef]/20 blur-3xl"></div>
        </div>
        
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="flex flex-col items-center mb-8 sm:mb-12 md:mb-16 lg:mb-20">
            <div className="w-12 h-12 sm:w-16 sm:h-16 md:w-20 md:h-20 lg:w-24 lg:h-24 relative mb-4 sm:mb-6 md:mb-8">
              <div className="absolute inset-0 rounded-full bg-[#ec4899]/20 animate-pulse-slow"></div>
              <div className="absolute inset-1 sm:inset-2 md:inset-3 rounded-full bg-[#ec4899]/40"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <svg className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 lg:w-12 lg:h-12 text-[#ec4899]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M8 10.5h-.13c-1.06 0-2.07.42-2.82 1.17A3.97 3.97 0 0 0 4 14.5c0 2.21 1.79 4 4 4h.5M20 10.5h-.13c-1.06 0-2.07.42-2.82 1.17a3.97 3.97 0 0 0-1.05 2.83c0 2.21 1.79 4 4 4h.5" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M12 4v4M16 7l-4 1M8 7l4 1" />
                </svg>
              </div>
            </div>
            
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-[#ec4899] mb-2 sm:mb-3 md:mb-4 text-center max-w-2xl leading-tight px-4 sm:px-0">
              What Our <span className="text-gradient-accent">Clients</span> Say About Us
            </h2>
            <p className="text-sm sm:text-base md:text-lg text-gray-700 max-w-2xl mx-auto text-center px-4 sm:px-0">
              Real stories from real homeowners who have experienced the Window Warriors difference.
            </p>
          </div>
          
          <div className="relative rounded-lg sm:rounded-xl md:rounded-2xl shadow-2xl bg-white backdrop-blur-lg border border-gray-200 p-2 sm:p-3 md:p-4 max-w-5xl mx-auto premium-card-glow">
            {/* Glow effect */}
            <div className="absolute -inset-0.5 bg-gradient-to-r from-[#ec4899]/20 to-[#d946ef]/20 rounded-xl sm:rounded-2xl blur-xl opacity-20"></div>
            
            {/* Quote marks - Progressive enhancement */}
            <div className="hidden sm:block absolute top-4 sm:top-6 left-4 sm:left-8 text-[#ec4899]/20 text-4xl sm:text-5xl md:text-7xl lg:text-9xl font-serif">&#8220;</div>
            <div className="hidden sm:block absolute bottom-4 sm:bottom-6 right-4 sm:right-8 text-[#ec4899]/20 text-4xl sm:text-5xl md:text-7xl lg:text-9xl font-serif">&#8221;</div>
            
            {/* Inner container */}
            <div className="relative backdrop-blur-lg rounded-md sm:rounded-lg md:rounded-xl overflow-hidden p-3 sm:p-4 md:p-6 lg:p-8">
              <TestimonialCarousel testimonials={testimonials} />
            </div>
          </div>
          
          <div className="mt-8 sm:mt-12 md:mt-16 lg:mt-20 text-center">
            <div className="flex flex-wrap gap-2 sm:gap-3 md:gap-4 justify-center mb-6 sm:mb-8 md:mb-10">
              <div className="bg-white/80 backdrop-blur-sm border border-gray-200 px-3 sm:px-4 md:px-6 py-2 sm:py-3 md:py-4 rounded-full flex items-center hover-lift">
                <span className="text-[#ec4899] font-bold mr-2">4.9</span>
                <div className="flex">
                  {[...Array(5)].map((_, i) => (
                    <svg key={i} className="w-4 h-4 sm:w-5 sm:h-5 text-[#ec4899]" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  ))}
                </div>
                <span className="text-gray-600 ml-2 text-xs sm:text-sm">from 200+ reviews</span>
              </div>
              
              <div className="bg-white/80 backdrop-blur-sm border border-gray-200 px-4 sm:px-6 py-3 sm:py-4 rounded-full flex items-center hover-lift">
                <div className="w-5 h-5 sm:w-6 sm:h-6 bg-[#ec4899] rounded-full flex items-center justify-center mr-2 sm:mr-3">
                  <svg className="w-3 h-3 sm:w-4 sm:h-4 text-[#0f172a]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <span className="text-gray-700 text-xs sm:text-sm">Trusted Traders</span>
              </div>
              
              <div className="bg-white/80 backdrop-blur-sm border border-gray-200 px-4 sm:px-6 py-3 sm:py-4 rounded-full flex items-center hover-lift">
                <div className="w-5 h-5 sm:w-6 sm:h-6 bg-[#ec4899] rounded-full flex items-center justify-center mr-2 sm:mr-3">
                  <svg className="w-3 h-3 sm:w-4 sm:h-4 text-[#0f172a]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <span className="text-gray-700 text-xs sm:text-sm">ASSURE Approved</span>
              </div>
            </div>
            
            <Link href="/testimonials" className="inline-flex items-center gap-2 sm:gap-3 px-6 sm:px-8 py-3 sm:py-4 bg-white hover:bg-gray-50 text-[#1a3a6d] border border-white/20 rounded-full transition-all duration-300 group btn-premium text-sm sm:text-base">
              <span>View All Testimonials</span>
              <svg className="w-4 h-4 sm:w-5 sm:h-5 transform transition-all duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
              </svg>
            </Link>
          </div>
        </div>
      </section>

      {/* Why Choose Us Section styled as an exclusive premium showcase */}
      <section className="py-16 sm:py-24 md:py-32 bg-white relative overflow-hidden">
        {/* Background pattern */}
        <div className="absolute inset-0 bg-pattern opacity-5"></div>
        
        {/* Floating elements */}
        <div className="absolute top-1/4 right-1/4 w-72 h-72 bg-[#ec4899]/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 left-1/4 w-96 h-96 bg-[#1a3a6d]/20 rounded-full blur-3xl"></div>
        
        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          <div className="text-center mb-12 sm:mb-16 md:mb-20">
            <div className="inline-flex items-center justify-center space-x-2 mb-4">
              <div className="flex-1 h-px max-w-[40px] sm:max-w-[60px] bg-gradient-to-r from-transparent to-[#ec4899]/70"></div>
              <div className="w-2 h-2 sm:w-3 sm:h-3 rounded-full bg-[#ec4899]"></div>
              <div className="flex-1 h-px max-w-[40px] sm:max-w-[60px] bg-gradient-to-l from-transparent to-[#ec4899]/70"></div>
            </div>
            
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-[#1a3a6d] mb-4 sm:mb-6 leading-tight">
              The Window Warriors <span className="text-gradient-accent">Difference</span>
            </h2>
            <p className="text-base sm:text-lg text-gray-700 max-w-2xl mx-auto">
              We blend craftsmanship with cutting-edge technology to deliver exceptional results that stand the test of time.
            </p>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8">
            {features.map((feature, index) => (
              <div 
                key={feature.title} 
                className="bg-white/5 backdrop-blur-lg rounded-xl sm:rounded-2xl p-6 sm:p-8 border border-white/10 transform transition-all duration-500 hover:-translate-y-2 group"
                style={{ animationDelay: `${index * 200}ms` }}
              >
                <div className="w-12 h-12 sm:w-16 sm:h-16 mb-4 sm:mb-6 rounded-xl sm:rounded-2xl bg-gradient-to-br from-[#ec4899] to-[#be185d] flex items-center justify-center group-hover:scale-110 transition-transform duration-500">
                  <feature.icon className="w-6 h-6 sm:w-8 sm:h-8 text-[#1a3a6d]" />
                </div>
                
                <h3 className="text-lg sm:text-xl font-bold text-[#1a3a6d] mb-3 sm:mb-4">{feature.title}</h3>
                <p className="text-sm sm:text-base text-gray-700">{feature.description}</p>
                
                {/* Bottom accent line */}
                <div className="w-10 h-1 sm:w-12 sm:h-1 bg-[#ec4899]/50 mt-4 sm:mt-6 rounded-full group-hover:w-16 sm:group-hover:w-20 transition-all duration-500"></div>
              </div>
            ))}
          </div>
          
          <div className="mt-16 sm:mt-20 md:mt-24 rounded-xl sm:rounded-2xl overflow-hidden relative">
            {/* Premium image with overlays */}
                          <div className="relative h-[300px] sm:h-[400px] md:h-[450px] lg:h-[500px] aspect-ratio-16-9">
              <Image 
                src="/images/optimized/front-view-front-door-with-blue-wall.webp" // Front door with blue wall
                alt="Modern front door and windows" 
                fill 
                className="object-cover object-center composite-layer"
                sizes="(max-width: 768px) 100vw, 100vw"
                loading="lazy"
              />
              
              {/* Gradient overlay */}
              <div className="absolute inset-0 bg-gradient-to-r from-[#0f172a]/90 via-[#0f172a]/70 to-transparent"></div>
              
              {/* Content overlay */}
              <div className="absolute inset-0 flex items-center">
                <div className="pl-6 sm:pl-8 md:pl-12 lg:pl-16 max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg">
                  <div className="flex items-center gap-2 mb-3 sm:mb-4">
                    <span className="w-4 sm:w-6 h-1 bg-[#ec4899]"></span>
                    <span className="text-[#ec4899] uppercase tracking-wider text-xs sm:text-sm font-medium">Our Heritage</span>
                  </div>
                  
                  <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-3 sm:mb-4 md:mb-6 leading-tight">
                    Master Craftsmen <br/><span className="text-gradient-accent">Since 2008</span>
                  </h2>
                  
                  <p className="text-sm sm:text-base md:text-lg text-accessible-light mb-6 sm:mb-8">
                    With over 15 years of experience and 2,500+ successful projects, 
                    we've built our reputation on exceptional quality and service that exceeds expectations.
                  </p>
                  
                  <Link href="/about" className="inline-flex items-center gap-2 sm:gap-3 px-6 sm:px-8 py-3 sm:py-4 bg-[#ec4899] hover:bg-[#be185d] text-white font-medium rounded-full transition-all duration-300 shadow-lg shadow-[#ec4899]/20 group btn-premium text-sm sm:text-base">
                    <span>Our History</span>
                    <svg className="w-4 h-4 sm:w-5 sm:h-5 transform transition-all duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                    </svg>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section with premium dark look */}
      <section className="py-16 sm:py-20 md:py-24 bg-white text-[#1a3a6d] relative overflow-hidden">
        {/* Decorative elements */}
        <div className="absolute top-0 left-0 w-full h-full pointer-events-none">
          <div className="absolute top-0 right-0 w-96 h-96 bg-[#ec4899]/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 left-0 w-64 h-64 bg-[#ec4899]/5 rounded-full blur-3xl"></div>
          
          {/* Floating particles - hidden on smaller screens */}
          <div className="hidden sm:block absolute top-1/4 left-1/3 w-8 h-8 rounded-full bg-white/5"></div>
          <div className="hidden sm:block absolute top-1/2 right-1/4 w-12 h-12 rounded-full bg-white/5"></div>
          <div className="hidden sm:block absolute bottom-1/3 left-1/4 w-10 h-10 rounded-full bg-white/5"></div>
        </div>
        
        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <span className="inline-block text-[#ec4899] font-medium mb-3 sm:mb-4 text-sm sm:text-base">GET IN TOUCH TODAY</span>
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-[#1a3a6d] mb-4 sm:mb-6">Ready to Transform Your Home?</h2>
            <p className="text-base sm:text-lg md:text-xl text-gray-700 mb-8 sm:mb-10 md:mb-12 max-w-2xl mx-auto">
              Contact us today for a free, no-obligation quote and consultation. We're here to help you find the perfect windows and doors for your home.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4 sm:gap-6">
              <Link href="/products-services#quote" className="btn-accent text-center text-sm sm:text-base px-6 sm:px-8 py-3 sm:py-4">
                Get Your Free Quote
              </Link>
              <Link href="/products-services" className="inline-block bg-transparent border-2 border-[#ec4899] text-[#ec4899] hover:bg-[#ec4899] hover:text-white font-medium py-3 px-6 sm:px-8 rounded-full transition-all duration-300 text-center text-sm sm:text-base">
                Browse Our Products
              </Link>
            </div>
            
            {/* Trust badges */}
            <div className="mt-10 sm:mt-12 md:mt-16 grid grid-cols-2 md:grid-cols-4 gap-3 sm:gap-6">
              <div className="bg-white/80 backdrop-blur-sm border border-gray-200 p-3 sm:p-4 rounded-lg flex flex-col items-center">
                <svg className="w-8 h-8 sm:w-10 sm:h-10 text-[#ec4899] mb-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 12L11 14L15 10M20.618 5.984C20.846 6.887 20.846 7.897 20.846 9C20.846 14.5 17.346 18 11.846 18C6.346 18 2.846 14.5 2.846 9C2.846 3.5 6.346 0 11.846 0C12.949 0 13.959 0 14.862 0.228" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span className="text-xs sm:text-sm font-medium text-center">Free No-Obligation Quotes</span>
              </div>
              <div className="bg-white/80 backdrop-blur-sm border border-gray-200 p-3 sm:p-4 rounded-lg flex flex-col items-center">
                <svg className="w-8 h-8 sm:w-10 sm:h-10 text-[#ec4899] mb-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M19.4 15C19.1277 15.8031 19.2292 16.6718 19.6727 17.4019C20.1162 18.132 20.8622 18.6376 21.7 18.8C20.5 21.4 18.5 22 16 22C12 22 11 20 7.00001 20C5.50001 20 4.00001 20.5 3.00001 22L2.50001 18.5C2.20001 16.5 3.40001 14 6.00001 14C8.00001 14 9.00001 16 12 16C14 16 14.5 15 19.4 15Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M12 7C9 7 7.00001 5 7.00001 3C7.00001 1.5 8.00001 1 8.00001 1C8.00001 1 9.50001 3 12 3C14.5 3 16 1 16 1C16 1 17 1.5 17 3C17 5 15 7 12 7Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span className="text-xs sm:text-sm font-medium text-center">Energy Efficiency Ratings</span>
              </div>
              <div className="bg-white/80 backdrop-blur-sm border border-gray-200 p-3 sm:p-4 rounded-lg flex flex-col items-center">
                <svg className="w-8 h-8 sm:w-10 sm:h-10 text-[#ec4899] mb-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span className="text-xs sm:text-sm font-medium text-center">10 Year Guarantee</span>
              </div>
              <div className="bg-white/80 backdrop-blur-sm border border-gray-200 p-3 sm:p-4 rounded-lg flex flex-col items-center">
                <svg className="w-8 h-8 sm:w-10 sm:h-10 text-[#ec4899] mb-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 12H15M9 16H15M17 21H7C5.89543 21 5 20.1046 5 19V5C5 3.89543 5.89543 3 7 3H12.5858C12.851 3 13.1054 3.10536 13.2929 3.29289L18.7071 8.70711C18.8946 8.89464 19 9.149 19 9.41421V19C19 20.1046 18.1046 21 17 21Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span className="text-xs sm:text-sm font-medium text-center">ASSURE Approved</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}
