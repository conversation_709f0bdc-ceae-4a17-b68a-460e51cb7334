import React from 'react';
import { Metadata } from 'next';
import Image from 'next/image';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Customer Testimonials | Window Warriors UPVC Windows & Doors',
  description: 'Read what our customers say about our UPVC windows, doors and conservatories. Trusted by homeowners across Newcastle, Durham and the North East.',
  keywords: 'UPVC customer reviews Newcastle, window installation testimonials Durham, conservatory customer feedback North East, door fitting reviews UK',
  openGraph: {
    title: 'Customer Testimonials | Window Warriors UPVC Windows & Doors',
    description: 'Discover what our customers say about our premium window and door installations. Read success stories from homeowners across Newcastle and the North East.',
    url: 'https://windowwarriors.uk/testimonials',
    siteName: 'Window Warriors',
    images: [
      {
        url: 'https://windowwarriors.uk/images/beautiful-view-blue-lake-captured-from-inside-villa.jpg',
        width: 1200,
        height: 630,
        alt: 'Window Warriors Customer Testimonials',
      },
    ],
    locale: 'en_GB',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Customer Testimonials | Window Warriors UPVC Windows & Doors',
    description: 'Read what our satisfied customers say about their experience with Window Warriors premium window and door installations.',
    images: ['https://windowwarriors.uk/images/beautiful-view-blue-lake-captured-from-inside-villa.jpg'],
  },
  alternates: {
    canonical: 'https://windowwarriors.uk/testimonials',
  },
};

export default function TestimonialsPage() {
  // Testimonial data with real client images
  const testimonials = [
    {
      id: 1,
      quote: "The team at Window Warriors completely transformed our home with their stunning UPVC windows. Not only do they look fantastic, but our home is so much warmer and quieter. The difference is remarkable!",
      name: "Sarah J.",
      location: "Newcastle upon Tyne",
      rating: 5,
      image: "/gallery/399964752_788247486438733_8470154972341291903_n.jpg",
      project: "Full House Window Replacement",
      featured: true
    },
    {
      id: 2,
      quote: "From the initial consultation to the final installation, Window Warriors were professional, courteous and efficient. Our new front door has transformed the appearance of our property and the security features give us great peace of mind.",
      name: "James W.",
      location: "Durham",
      rating: 5,
      image: "/gallery/107737930_2714639882145635_5548775699563401414_n.jpg",
      project: "Composite Front Door Installation",
      featured: true
    },
    {
      id: 3,
      quote: "As an interior designer, I'm particular about details and quality. Window Warriors exceeded my expectations with their attention to detail and the premium finish of their UPVC doors. I've recommended them to several clients already.",
      name: "Emma T.",
      location: "Sunderland",
      rating: 5,
      image: "/gallery/171007892_196756585587829_6830486126757344649_n.jpg",
      project: "Bifold Doors Installation",
      featured: false
    }
  ];

  return (
    <main className="flex min-h-screen flex-col">
      {/* Enhanced Hero Section - Mobile Optimized */}
      <section className="relative min-h-[500px] sm:min-h-[600px] lg:min-h-screen py-16 sm:py-20 md:py-24 lg:py-32 overflow-hidden">
        {/* Multi-layered Background - Mobile Responsive */}
        <div className="absolute inset-0">
          {/* Background image */}
          <div className="absolute inset-0 bg-cover bg-center" style={{
            backgroundImage: 'url("/images/beautiful-view-blue-lake-captured-from-inside-villa.jpg")',
            backgroundPosition: 'center 30%',
            backgroundSize: 'cover',
          }}>
            {/* Enhanced overlay with pink accents */}
            <div className="absolute inset-0 bg-gradient-to-br from-[#1a3a6d]/95 via-[#1a3a6d]/85 to-[#ec4899]/20"></div>
            <div className="absolute inset-0 bg-gradient-to-t from-[#1a3a6d]/90 via-transparent to-transparent"></div>
          </div>
          
          {/* Floating pink elements - Mobile responsive sizing */}
          <div className="absolute top-1/4 left-1/4 w-20 sm:w-32 md:w-48 lg:w-64 h-20 sm:h-32 md:h-48 lg:h-64 bg-[#ec4899]/15 rounded-full blur-3xl animate-float-slow"></div>
          <div className="absolute bottom-1/4 right-1/4 w-24 sm:w-48 md:w-72 lg:w-96 h-24 sm:h-48 md:h-72 lg:h-96 bg-[#d946ef]/10 rounded-full blur-3xl animate-float" style={{animationDelay: '2s'}}></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 sm:w-60 md:w-80 lg:w-96 h-40 sm:h-60 md:h-80 lg:h-96 bg-[#ec4899]/5 rounded-full blur-3xl animate-pulse-slow"></div>
          
          {/* Glass particles - Mobile friendly */}
          <div className="hidden sm:block absolute top-[15%] right-[15%] w-2 sm:w-3 md:w-4 h-2 sm:h-3 md:h-4 bg-white/20 rounded-full animate-float"></div>
          <div className="hidden sm:block absolute top-[25%] right-[25%] w-2 sm:w-2.5 md:w-3 h-2 sm:h-2.5 md:h-3 bg-[#ec4899]/30 rounded-full animate-float" style={{animationDelay: '1s'}}></div>
          <div className="hidden md:block absolute top-[35%] right-[35%] w-1.5 sm:w-2 h-1.5 sm:h-2 bg-white/15 rounded-full animate-float" style={{animationDelay: '2s'}}></div>
          
          {/* Geometric pink accents - Responsive */}
          <div className="hidden sm:block absolute top-[20%] left-[10%] w-6 sm:w-8 h-6 sm:h-8 border border-[#ec4899]/30 rotate-45 animate-spin-slow"></div>
          <div className="hidden sm:block absolute bottom-[30%] right-[10%] w-4 sm:w-6 h-4 sm:h-6 bg-[#ec4899]/20 transform rotate-45 animate-pulse-slow"></div>
          <div className="hidden md:block absolute top-[60%] left-[20%] w-8 sm:w-10 md:w-12 h-8 sm:h-10 md:h-12 border-2 border-[#d946ef]/20 rounded-full animate-bounce-slow"></div>
        </div>
        
        {/* Enhanced Hero Content - Mobile Optimized */}
        <div className="container mx-auto px-4 sm:px-6 relative z-10 flex flex-col justify-center min-h-[500px] sm:min-h-[600px] lg:min-h-screen pb-8 sm:pb-12 lg:pb-16">
          <div className="max-w-4xl animate-fade-in" style={{ animationDelay: '0.3s' }}>
            {/* Enhanced brand badge - Mobile responsive */}
            <div className="inline-flex items-center mb-4 sm:mb-6 md:mb-8">
              <div className="bg-white/10 backdrop-blur-sm border border-[#ec4899]/30 rounded-full px-4 sm:px-6 py-1.5 sm:py-2 animate-fade-blur" style={{ animationDelay: '0.2s' }}>
                <span className="text-accessible-light text-xs sm:text-sm md:text-base font-medium">CUSTOMER STORIES</span>
                <div className="w-full h-px bg-gradient-to-r from-transparent via-[#ec4899]/70 to-transparent mt-1"></div>
              </div>
            </div>
            
            {/* Enhanced title with pink gradients - Mobile responsive */}
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl 2xl:text-8xl font-bold text-white mb-3 sm:mb-4 md:mb-6 leading-tight">
              Our <span className="relative inline-block">
                <span className="text-gradient-logo">Testimonials</span>
                <div className="absolute -bottom-1 sm:-bottom-2 left-0 w-full h-0.5 sm:h-1 bg-gradient-to-r from-[#ec4899] to-[#d946ef] rounded-full animate-shimmer"></div>
                <div className="absolute -top-0.5 sm:-top-1 -right-0.5 sm:-right-1 w-2 sm:w-3 h-2 sm:h-3 bg-[#ec4899]/60 rounded-full animate-pulse"></div>
              </span>
            </h1>
            
            {/* Enhanced description - Mobile responsive */}
            <div className="relative mb-4 sm:mb-6 md:mb-8">
              <p className="text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl text-accessible-light leading-relaxed animate-slide-up" style={{ animationDelay: '0.6s' }}>
                Discover what homeowners across the North East say about our premium UPVC windows, doors and conservatories.
              </p>
              <div className="absolute -left-2 sm:-left-4 top-0 w-0.5 sm:w-1 h-full bg-gradient-to-b from-[#ec4899] to-transparent rounded-full opacity-60"></div>
            </div>
            
            {/* Enhanced buttons - Mobile responsive */}
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 md:gap-6 mb-6 sm:mb-8 md:mb-10 animate-slide-up" style={{ animationDelay: '0.9s' }}>
              <Link href="/products-services#quote" className="btn-primary text-center text-sm sm:text-base">
                Get a Free Quote
              </Link>
              <Link href="/gallery" className="inline-block bg-white/10 backdrop-blur-sm border-2 border-white/30 text-white hover:bg-white hover:text-[#1a3a6d] font-medium py-3 sm:py-4 px-6 sm:px-8 rounded-full transition-all duration-300 text-center group text-sm sm:text-base">
                <span className="flex items-center justify-center">
                  <svg className="w-4 sm:w-5 h-4 sm:h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                  View Our Gallery
                </span>
              </Link>
            </div>
            
            {/* Enhanced rating section - Mobile responsive */}
            <div className="animate-fade-in" style={{ animationDelay: '1.2s' }}>
              <p className="text-accessible-muted text-xs sm:text-sm md:text-base mb-3 sm:mb-4">Rated by our customers</p>
              <div className="flex flex-wrap items-center gap-3 sm:gap-4">
                <div className="flex items-center space-x-1 sm:space-x-2">
                  <div className="flex space-x-0.5 sm:space-x-1">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <svg key={star} className="w-4 sm:w-5 md:w-6 h-4 sm:h-5 md:h-6 text-[#ec4899]" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                      </svg>
                    ))}
                  </div>
                  <span className="text-white font-bold text-lg sm:text-xl">4.9/5</span>
                </div>
                <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-full px-3 sm:px-4 py-1.5 sm:py-2">
                  <span className="text-accessible-light text-xs sm:text-sm font-medium">200+ Happy Customers</span>
                </div>
              </div>
            </div>
          </div>
          
          {/* Modern scroll indicator - Mobile responsive */}
          <div className="mt-auto pt-6 sm:pt-8 flex justify-center animate-bounce-slow">
            <div className="flex flex-col items-center">
              <div className="w-5 sm:w-6 h-8 sm:h-10 border-2 border-white/30 rounded-full flex justify-center">
                <div className="w-0.5 sm:w-1 h-2 sm:h-3 bg-white/60 rounded-full mt-1.5 sm:mt-2 animate-pulse"></div>
              </div>
              <span className="text-accessible-subtle text-xs mt-1 sm:mt-2 hidden sm:block">Read testimonials</span>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Testimonials - Mobile Optimized */}
      <section className="py-12 sm:py-16 md:py-20 lg:py-24 bg-white relative">
        {/* Decorative elements - Mobile responsive */}
        <div className="absolute top-16 sm:top-24 md:top-32 left-0 w-32 sm:w-48 md:w-64 h-32 sm:h-48 md:h-64 bg-gradient-to-r from-[#ec4899]/20 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-16 sm:bottom-24 md:bottom-32 right-0 w-32 sm:w-48 md:w-64 h-32 sm:h-48 md:h-64 bg-gradient-to-l from-[#d946ef]/10 to-transparent rounded-full blur-3xl"></div>
        
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 pt-8 sm:pt-12 md:pt-16">
          <div className="text-center mb-8 sm:mb-10 md:mb-12">
            <div className="bg-gradient-to-r from-[#ec4899] to-[#d946ef] p-0.5 inline-block rounded-full mb-3 sm:mb-4 md:mb-5">
              <span className="bg-white text-[#1a3a6d] text-xs sm:text-sm font-semibold px-3 sm:px-4 py-1 sm:py-1.5 rounded-full inline-block">
                Happy Customers
              </span>
            </div>
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-[#1a3a6d] mb-3 sm:mb-4">Featured <span className="text-gradient-logo">Success Stories</span></h2>
            <p className="text-sm sm:text-base md:text-lg text-gray-700 max-w-2xl mx-auto mb-6 sm:mb-8">
              Here's what some of our recent customers have to say about their experience with Window Warriors
            </p>
          </div>

          {/* Featured testimonials - Mobile optimized */}
          <div className="grid grid-cols-1 gap-6 sm:gap-8 mb-12 sm:mb-14 md:mb-16">
            {testimonials.filter(t => t.featured).map((testimonial, index) => (
              <div key={testimonial.id} className="glass-card rounded-lg sm:rounded-xl overflow-hidden shadow-lg transition-all duration-500 hover:shadow-xl group">
                <div className="flex flex-col lg:flex-row">
                  <div className="relative w-full lg:w-1/3 h-48 sm:h-56 md:h-64 lg:h-auto">
                    <Image 
                      src={testimonial.image}
                      alt={testimonial.name}
                      fill
                      className="object-cover transition-transform duration-700 group-hover:scale-105"
                    />
                    <div className="absolute inset-0 bg-gradient-to-r from-[#1a3a6d]/60 to-transparent lg:bg-gradient-to-t"></div>
                    
                    <div className="absolute bottom-3 sm:bottom-4 left-3 sm:left-4 lg:bottom-8 lg:left-8 z-10">
                      <div className="bg-white/10 backdrop-blur-sm rounded-lg px-2 sm:px-3 py-0.5 sm:py-1 text-white text-xs sm:text-sm font-medium inline-block">
                        {testimonial.project}
                      </div>
                    </div>
                  </div>
                  
                  <div className="p-4 sm:p-6 md:p-8 lg:w-2/3 flex flex-col justify-between">
                    <div>
                      <div className="flex justify-between items-start mb-3 sm:mb-4">
                        <svg className="w-8 sm:w-10 md:w-12 h-8 sm:h-10 md:h-12 text-[#ec4899]/30" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                          <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
                        </svg>
                        <div className="flex">
                          {[1, 2, 3, 4, 5].map((star) => (
                            <svg key={star} className="w-4 sm:w-5 h-4 sm:h-5 text-[#ec4899]" viewBox="0 0 24 24" fill="currentColor">
                              <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                            </svg>
                          ))}
                        </div>
                      </div>
                      
                      <p className="text-gray-700 text-sm sm:text-base md:text-lg lg:text-xl leading-relaxed mb-4 sm:mb-6">"{testimonial.quote}"</p>
                    </div>
                    
                    <div className="flex items-center mt-3 sm:mt-4">
                      <div className="w-0.5 sm:w-1 h-8 sm:h-10 md:h-12 mr-3 sm:mr-4 bg-[#ec4899]"></div>
                      <div>
                        <h3 className="font-bold text-[#1a3a6d] text-base sm:text-lg md:text-xl">{testimonial.name}</h3>
                        <p className="text-accessible-gray text-sm sm:text-base">{testimonial.location}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonial Grid - Mobile Optimized */}
      <section className="py-12 sm:py-16 md:py-20 lg:py-24 bg-gray-50 relative overflow-hidden">
        {/* Decorative background elements - Mobile responsive */}
        <div className="absolute top-0 right-0 w-48 sm:w-64 md:w-80 lg:w-96 h-48 sm:h-64 md:h-80 lg:h-96 bg-gradient-to-bl from-[#ec4899]/5 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-48 sm:w-64 md:w-80 lg:w-96 h-48 sm:h-64 md:h-80 lg:h-96 bg-gradient-to-tr from-[#d946ef]/5 to-transparent rounded-full blur-3xl"></div>
        
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center mb-8 sm:mb-10 md:mb-12">
            <div className="inline-block mb-3 sm:mb-4">
              <div className="relative inline-block">
                <span className="absolute inset-x-0 bottom-0 h-1.5 sm:h-2 bg-gradient-to-r from-[#ec4899]/30 to-[#ec4899]/80 transform -rotate-1"></span>
                <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-[#1a3a6d] mb-2 relative z-10">More Customer <span className="text-gradient-logo">Reviews</span></h2>
              </div>
            </div>
            <p className="text-sm sm:text-base md:text-lg text-gray-700 max-w-2xl mx-auto">
              Read more feedback from our satisfied customers across Newcastle, Durham and the North East
            </p>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
            {[
              {
                quote: "The new windows have made such a difference to our bedroom, it's so much quieter and warmer. The installation team was friendly and cleaned up perfectly after they finished.",
                name: "Robert T.",
                location: "Gateshead",
                project: "Bedroom Windows",
                image: "/gallery/171676045_196756592254495_2685816906816687273_n.jpg"
              },
              {
                quote: "My new front door looks fantastic and has really improved the kerb appeal of my home. The security features are excellent, giving me great peace of mind.",
                name: "Susan C.",
                location: "Washington",
                project: "Front Door",
                image: "/gallery/118518515_2757609127848710_4068117359392121180_n.jpg"
              },
              {
                quote: "Window Warriors were incredibly helpful when I was choosing windows for my period property. They helped me select styles that maintained the character while providing modern benefits.",
                name: "Michael B.",
                location: "Durham",
                project: "Period Property Windows",
                image: "/gallery/56196668_2326392070970420_8862400157547757568_n.jpg"
              },
              {
                quote: "From quote to installation, the process was smooth and professional. The price was competitive and the quality of the products is exceptional.",
                name: "Jennifer G.",
                location: "South Shields",
                project: "Whole House Windows",
                image: "/gallery/79129539_2531714537104838_1705095243348574208_n.jpg"
              },
              {
                quote: "Our new conservatory has transformed how we use our home. It's comfortable year-round and the build quality is outstanding. So glad we chose Window Warriors.",
                name: "Thomas W.",
                location: "North Shields",
                project: "Conservatory",
                image: "/gallery/18423818_1912587215684243_517468482600756912_n.jpg"
              },
              {
                quote: "The bifold doors have completely changed our living space. The installation was quick and professional, and the doors operate smoothly - very impressed!",
                name: "Laura M.",
                location: "Morpeth",
                project: "Bifold Doors",
                image: "/gallery/58577539_2340166859592941_8734424958354063360_n.jpg"
              },
            ].map((review, index) => (
              <div 
                key={index}
                className="glass-card p-4 sm:p-6 rounded-lg sm:rounded-xl shadow-md transition-all duration-500 hover:shadow-xl hover:-translate-y-1"
              >
                <div className="relative h-40 sm:h-48 mb-3 sm:mb-4 rounded-lg overflow-hidden">
                  <Image
                    src={review.image}
                    alt={review.name}
                    fill
                    className="object-cover transition-transform duration-500 hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-[#1a3a6d]/80 to-transparent"></div>
                  <div className="absolute bottom-2 sm:bottom-3 left-2 sm:left-3">
                    <div className="bg-white/10 backdrop-blur-sm rounded-lg px-2 sm:px-3 py-0.5 sm:py-1 text-white text-xs font-medium inline-block">
                      {review.project}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-start justify-between mb-3 sm:mb-4">
                  <svg className="w-8 sm:w-10 h-8 sm:h-10 text-[#ec4899]/30" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                    <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
                  </svg>
                  <div className="flex">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <svg key={star} className="w-3 sm:w-4 h-3 sm:h-4 text-[#ec4899]" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                      </svg>
                    ))}
                  </div>
                </div>
                
                <p className="text-gray-700 leading-relaxed mb-4 sm:mb-6 text-sm sm:text-base">"{review.quote}"</p>
                
                <div>
                  <div className="h-0.5 w-8 sm:w-12 bg-[#ec4899] mb-3 sm:mb-4"></div>
                  <h3 className="font-bold text-[#1a3a6d] text-sm sm:text-base">{review.name}</h3>
                  <p className="text-accessible-gray text-xs sm:text-sm mb-1">{review.location}</p>
                  <p className="text-[#ec4899] text-xs sm:text-sm font-medium">{review.project}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section - Mobile Optimized */}
      <section className="py-12 sm:py-16 md:py-20 lg:py-24 bg-white relative overflow-hidden">
        {/* Decorative elements - Mobile responsive */}
        <div className="absolute top-0 left-0 w-full h-full pointer-events-none">
          <div className="absolute top-0 right-0 w-48 sm:w-64 md:w-80 lg:w-96 h-48 sm:h-64 md:h-80 lg:h-96 bg-[#ec4899]/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 left-0 w-32 sm:w-48 md:w-64 h-32 sm:h-48 md:h-64 bg-[#d946ef]/5 rounded-full blur-3xl"></div>
          
          {/* Floating particles - Mobile responsive */}
          <div className="hidden sm:block absolute top-1/4 left-1/3 w-6 sm:w-8 h-6 sm:h-8 rounded-full bg-[#ec4899]/10"></div>
          <div className="hidden sm:block absolute top-1/2 right-1/4 w-8 sm:w-12 h-8 sm:h-12 rounded-full bg-[#d946ef]/10"></div>
          <div className="hidden sm:block absolute bottom-1/3 left-1/4 w-6 sm:w-10 h-6 sm:h-10 rounded-full bg-[#ec4899]/10"></div>
        </div>
        
        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <span className="inline-block text-[#ec4899] font-medium mb-3 sm:mb-4 text-xs sm:text-sm md:text-base">GET IN TOUCH TODAY</span>
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-3 sm:mb-4 md:mb-6 text-[#1a3a6d]">Ready to Transform Your Home?</h2>
            <p className="text-sm sm:text-base md:text-lg lg:text-xl text-gray-600 mb-6 sm:mb-8 md:mb-10 lg:mb-12 max-w-2xl mx-auto">
              Join our happy customers and transform your home with our premium UPVC windows, doors and conservatories.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-3 sm:gap-4 md:gap-6">
              <Link href="/products-services#quote" className="btn-primary text-center text-sm sm:text-base px-6 sm:px-8 py-3 sm:py-4">
                Get Your Free Quote
              </Link>
              <Link href="/gallery" className="inline-block border-2 border-[#ec4899] text-[#ec4899] hover:bg-[#ec4899] hover:text-white font-medium py-3 sm:py-4 px-6 sm:px-8 rounded-full transition-all duration-300 text-center text-sm sm:text-base">
                View Our Gallery
              </Link>
            </div>
            
            {/* Trust badges - Mobile optimized */}
            <div className="mt-8 sm:mt-10 md:mt-12 lg:mt-16 grid grid-cols-2 md:grid-cols-4 gap-3 sm:gap-4 md:gap-6">
              <div className="bg-gray-50 border border-gray-200 p-3 sm:p-4 rounded-lg flex flex-col items-center hover:border-[#ec4899]/30 transition-colors">
                <svg className="w-6 sm:w-8 md:w-10 h-6 sm:h-8 md:h-10 text-[#ec4899] mb-1 sm:mb-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 12L11 14L15 10M20.618 5.984C20.846 6.887 20.846 7.897 20.846 9C20.846 14.5 17.346 18 11.846 18C6.346 18 2.846 14.5 2.846 9C2.846 3.5 6.346 0 11.846 0C12.949 0 13.959 0 14.862 0.228" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span className="text-xs sm:text-sm font-medium text-center text-gray-700">Free No-Obligation Quotes</span>
              </div>
              <div className="bg-gray-50 border border-gray-200 p-3 sm:p-4 rounded-lg flex flex-col items-center hover:border-[#ec4899]/30 transition-colors">
                <svg className="w-6 sm:w-8 md:w-10 h-6 sm:h-8 md:h-10 text-[#ec4899] mb-1 sm:mb-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M19.4 15C19.1277 15.8031 19.2292 16.6718 19.6727 17.4019C20.1162 18.132 20.8622 18.6376 21.7 18.8C20.5 21.4 18.5 22 16 22C12 22 11 20 7.00001 20C5.50001 20 4.00001 20.5 3.00001 22L2.50001 18.5C2.20001 16.5 3.40001 14 6.00001 14C8.00001 14 9.00001 16 12 16C14 16 14.5 15 19.4 15Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M12 7C9 7 7.00001 5 7.00001 3C7.00001 1.5 8.00001 1 8.00001 1C8.00001 1 9.50001 3 12 3C14.5 3 16 1 16 1C16 1 17 1.5 17 3C17 5 15 7 12 7Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span className="text-xs sm:text-sm font-medium text-center text-gray-700">Energy Efficiency Ratings</span>
              </div>
              <div className="bg-gray-50 border border-gray-200 p-3 sm:p-4 rounded-lg flex flex-col items-center hover:border-[#ec4899]/30 transition-colors">
                <svg className="w-6 sm:w-8 md:w-10 h-6 sm:h-8 md:h-10 text-[#ec4899] mb-1 sm:mb-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span className="text-xs sm:text-sm font-medium text-center text-gray-700">10 Year Guarantee</span>
              </div>
              <div className="bg-gray-50 border border-gray-200 p-3 sm:p-4 rounded-lg flex flex-col items-center hover:border-[#ec4899]/30 transition-colors">
                <svg className="w-6 sm:w-8 md:w-10 h-6 sm:h-8 md:h-10 text-[#ec4899] mb-1 sm:mb-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 12H15M9 16H15M17 21H7C5.89543 21 5 20.1046 5 19V5C5 3.89543 5.89543 3 7 3H12.5858C12.851 3 13.1054 3.10536 13.2929 3.29289L18.7071 8.70711C18.8946 8.89464 19 9.149 19 9.41421V19C19 20.1046 18.1046 21 17 21Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span className="text-xs sm:text-sm font-medium text-center text-gray-700">ASSURE Approved</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
} 