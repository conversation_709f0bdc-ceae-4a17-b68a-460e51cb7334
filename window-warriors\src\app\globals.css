@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  
  /* Brand colors - Updated with new logo colors */
  --primary: #1a3a6d;
  --primary-dark: #0e2348;
  --primary-light: #3a5a8d;
  --accent: #ec4899;        /* Hot pink from logo */
  --accent-dark: #be185d;   /* Deep pink */
  --accent-light: #f9a8d4;  /* Light pink */
  
  /* New logo-inspired colors */
  --logo-pink: #d946ef;     /* Bright magenta from logo */
  --logo-pink-dark: #a21caf; /* Deep magenta */
  --logo-pink-light: #f0abfc; /* Light magenta */
  
  /* Secondary modern colors */
  --secondary: #06b6d4;     /* Modern cyan */
  --secondary-dark: #0891b2;
  --secondary-light: #67e8f9;
  
  /* Neutral colors */
  --neutral-50: #f9fafb;
  --neutral-100: #f3f4f6;
  --neutral-200: #e5e7eb;
  --neutral-300: #d1d5db;
  --neutral-400: #9ca3af;
  --neutral-500: #6b7280;
  --neutral-600: #4b5563;
  --neutral-700: #374151;
  --neutral-800: #1f2937;
  --neutral-900: #111827;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Fonts */
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  
  /* Glass effects */
  --glass-light: rgba(255, 255, 255, 0.85);
  --glass-dark: rgba(15, 23, 42, 0.8);
  --glass-blur: 12px;
  --glass-border-light: rgba(255, 255, 255, 0.3);
  --glass-border-dark: rgba(255, 255, 255, 0.1);
  
  /* Transitions */
  --transition-fast: 0.2s ease;
  --transition-normal: 0.4s ease;
  --transition-slow: 0.7s ease;
  
  /* Motion */
  --motion-bounce: cubic-bezier(0.175, 0.885, 0.32, 1.275);
  --motion-smooth: cubic-bezier(0.645, 0.045, 0.355, 1);
  --motion-swing: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

body {
  background: #ffffff;
  color: #171717;
  font-family: var(--font-sans);
}

/* Text gradient effects */
.text-gradient {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  background-image: linear-gradient(90deg, var(--primary), var(--primary-light));
}

.text-gradient-accent {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  background-image: linear-gradient(90deg, var(--accent), var(--accent-light));
}

.text-gradient-premium {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  background-image: linear-gradient(135deg, var(--logo-pink), var(--accent), var(--secondary));
  background-size: 300% 300%;
  animation: gradient-shift 8s ease infinite;
}

.text-gradient-logo {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  background-image: linear-gradient(135deg, var(--logo-pink-dark), var(--logo-pink), var(--accent-light));
  background-size: 200% 200%;
  animation: gradient-shift 6s ease infinite;
}

.text-gradient-subtle {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  background-image: linear-gradient(to right, var(--primary), var(--primary-light) 30%, var(--primary) 70%, var(--primary-light));
  background-size: 200% auto;
  animation: text-flow 5s linear infinite;
}

/* Background gradients */
.bg-gradient-primary {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
}

.bg-gradient-accent {
  background: linear-gradient(135deg, var(--accent), var(--accent-dark));
}

.bg-gradient-logo {
  background: linear-gradient(135deg, var(--logo-pink-dark), var(--logo-pink));
}

.bg-gradient-modern {
  background: linear-gradient(135deg, var(--primary-dark), var(--logo-pink-dark), var(--accent-dark));
}

.bg-gradient-subtle {
  background: linear-gradient(to right, var(--primary-dark), var(--primary) 50%, var(--primary-dark));
  background-size: 200% 100%;
  animation: gradient-flow 15s ease infinite;
}

.bg-gradient-premium {
  background: linear-gradient(135deg, var(--primary-dark), var(--logo-pink-dark), var(--accent), var(--secondary));
  background-size: 400% 400%;
  animation: gradient-shift 15s ease infinite;
}

/* Glass card effects */
.glass-card {
  @apply bg-white backdrop-filter backdrop-blur-sm border border-white/30 transition-all duration-300 relative z-10;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05), 0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

.glass-card:hover {
  @apply bg-white;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.2) inset;
}

.glass-card-dark {
  background: var(--glass-dark);
  backdrop-filter: blur(var(--glass-blur));
  -webkit-backdrop-filter: blur(var(--glass-blur));
  border: 1px solid var(--glass-border-dark);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 2px 8px rgba(255, 255, 255, 0.05) inset,
    0 -2px 8px rgba(0, 0, 0, 0.1) inset;
  border-radius: 16px;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.glass-card-dark::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(rgba(255, 255, 255, 0.05), transparent 70%);
  opacity: 0;
  transform: scale(1.5);
  transition: opacity var(--transition-normal), transform var(--transition-slow);
  pointer-events: none;
  z-index: 1;
}

.glass-card-dark:hover {
  background: rgba(15, 23, 42, 0.9);
  box-shadow: 
    0 15px 50px rgba(0, 0, 0, 0.4),
    0 5px 15px rgba(255, 255, 255, 0.1) inset,
    0 -5px 15px rgba(0, 0, 0, 0.2) inset;
  transform: translateY(-7px) scale(1.01);
}

.glass-card-dark:hover::before {
  opacity: 1;
  transform: scale(1);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(40px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideInRight {
  from { transform: translateX(60px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes pulse {
  0% { transform: scale(1); box-shadow: 0 0 0 0 rgba(236, 72, 153, 0.4); }
  50% { transform: scale(1.05); box-shadow: 0 0 0 15px rgba(236, 72, 153, 0); }
  100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(236, 72, 153, 0); }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes text-flow {
  to { background-position: 200% center; }
}

@keyframes gradient-flow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
  100% { transform: translateY(0px); }
}

@keyframes bounceSlow {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-20px); }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes fadeInBlur {
  from { 
    opacity: 0;
    filter: blur(10px);
  }
  to { 
    opacity: 1;
    filter: blur(0px);
  }
}

.animate-fade-in {
  animation: fadeIn 1.2s var(--motion-smooth) forwards;
  transform: translateZ(0);
}

.animate-slide-up {
  animation: slideUp 1.2s var(--motion-smooth) forwards;
  transform: translateZ(0);
}

.animate-slide-right {
  animation: slideInRight 1s var(--motion-smooth) forwards;
  transform: translateZ(0);
}

.animate-pulse-slow {
  animation: pulse 4s var(--motion-bounce) infinite;
  transform: translateZ(0);
}

.animate-shimmer {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.5) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 3s infinite;
  transform: translateZ(0);
}

.animate-fade-blur {
  animation: fadeInBlur 1.5s var(--motion-smooth) forwards;
  transform: translateZ(0);
}

.animate-spin-slow {
  animation: spin 12s linear infinite;
  transform: translateZ(0);
}

.animation-delay-200 {
  animation-delay: 200ms;
}

.animation-delay-400 {
  animation-delay: 400ms;
}

.animation-delay-600 {
  animation-delay: 600ms;
}

.animation-delay-800 {
  animation-delay: 800ms;
}

.animation-delay-1000 {
  animation-delay: 1000ms;
}

/* Float animation variants */
.animate-float {
  animation: float 6s var(--motion-smooth) infinite;
  transform: translateZ(0);
}

.animate-float-1 {
  animation: float 6s var(--motion-smooth) infinite;
  transform: translateZ(0);
}

.animate-float-2 {
  animation: float 7s var(--motion-smooth) infinite 0.5s;
  transform: translateZ(0);
}

.animate-float-3 {
  animation: float 8s var(--motion-smooth) infinite 1s;
  transform: translateZ(0);
}

.animate-bounce-slow {
  animation: bounceSlow 3s var(--motion-swing) infinite;
  transform: translateZ(0);
}

/* Button styles with enhanced effects */
.btn-primary {
  @apply relative overflow-hidden bg-[#1a3a6d] hover:bg-[#0f2b5c] text-white font-medium rounded-full px-6 py-3 transition-all duration-300 inline-flex items-center justify-center;
  box-shadow: 0 4px 15px rgba(212, 70, 239, 0.2);
}

.btn-primary::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to right, var(--logo-pink-dark), var(--logo-pink));
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 9999px;
}

.btn-primary:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 10px 25px rgba(212, 70, 239, 0.4);
}

.btn-primary:hover::before {
  opacity: 1;
}

.btn-primary::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(240, 171, 252, 0.3), transparent 50%);
  opacity: 0;
  transition: opacity 0.3s ease;
  transform: scale(1.3);
  pointer-events: none;
}

.btn-primary:hover::after {
  opacity: 1;
  transform: scale(1);
}

.btn-secondary {
  @apply relative overflow-hidden bg-[#ec4899] hover:bg-[#be185d] text-white font-medium rounded-full px-6 py-3 transition-all duration-300 inline-flex items-center justify-center;
}

.btn-secondary::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to right, var(--accent-dark), var(--accent));
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 9999px;
}

.btn-secondary:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 10px 25px rgba(236, 72, 153, 0.4);
}

.btn-secondary:hover::before {
  opacity: 1;
}

.btn-secondary::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(249, 168, 212, 0.3), transparent 50%);
  opacity: 0;
  transform: scale(1.3);
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.btn-secondary:hover::after {
  opacity: 1;
  transform: scale(1);
}

.btn-accent {
  @apply inline-block bg-[#ec4899] text-white font-medium py-3 px-8 rounded-full shadow-md transition-all duration-300 text-center relative overflow-hidden;
  box-shadow: 0 4px 15px rgba(236, 72, 153, 0.3);
}

.btn-accent::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, var(--logo-pink), var(--accent));
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 9999px;
}

.btn-accent:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 10px 25px rgba(212, 70, 239, 0.4);
}

.btn-accent:hover::before {
  opacity: 1;
}

.btn-outline {
  @apply relative overflow-hidden bg-transparent text-white border border-white/50 hover:border-white/90 font-medium rounded-full px-6 py-3 transition-all duration-300 inline-flex items-center justify-center;
}

.btn-outline:hover {
  transform: translateY(-3px) scale(1.02);
  background-color: rgba(255, 255, 255, 0.1);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.btn-outline-light {
  @apply inline-block bg-transparent border-2 border-white text-white font-medium py-3 px-8 rounded-full transition-all duration-300 text-center relative overflow-hidden;
}

.btn-outline-light:hover {
  transform: translateY(-3px) scale(1.02);
  background-color: rgba(255, 255, 255, 0.1);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: var(--neutral-100);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-light);
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary);
}

/* Custom shapes */
.shape-blob {
  border-radius: 42% 56% 72% 28% / 42% 42% 56% 48%;
}

.shape-blob-2 {
  border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
}

.shape-blob-3 {
  border-radius: 53% 47% 34% 66% / 63% 46% 54% 37%;
}

/* Utility classes for images */
.img-zoom-container {
  overflow: hidden;
}

.img-zoom {
  transition: transform var(--transition-normal);
}

.img-zoom:hover {
  transform: scale(1.1);
}

/* Text highlight effect */
.highlight-text {
  position: relative;
  display: inline-block;
}

.highlight-text::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 30%;
  background-color: rgba(236, 72, 153, 0.3);
  z-index: -1;
  transform: skewX(-12deg);
}

/* Circle decorations */
.circle-decoration {
  position: absolute;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(26, 58, 109, 0.3), rgba(26, 58, 109, 0.05));
  z-index: 0;
  animation: float 8s ease-in-out infinite;
}

/* Background patterns */
.bg-grid-pattern {
  background-image: 
    linear-gradient(to right, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
}

.bg-pattern {
  background-image: 
    radial-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
    radial-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 30px 30px;
  background-position: 0 0, 15px 15px;
}

/* Text gradient animations with more sophistication */
@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Advanced animations */
@keyframes float-slow {
  0% { transform: translateY(0) translateX(0); }
  25% { transform: translateY(-15px) translateX(10px); }
  50% { transform: translateY(5px) translateX(-5px); }
  75% { transform: translateY(-7px) translateX(-10px); }
  100% { transform: translateY(0) translateX(0); }
}

.animate-float-slow {
  animation: float-slow 18s ease-in-out infinite;
  transform: translateZ(0);
}

/* 3D Transformations and Perspective */
.perspective-1000 {
  perspective: 1000px;
}

.transform-3d {
  transform-style: preserve-3d;
}

.rotate-x-12 {
  transform: rotateX(12deg);
}

@media (prefers-reduced-motion: no-preference) {
  .group:hover .transform-3d-hover {
    transform: rotateY(5deg) translateZ(20px);
    transition: transform 0.5s ease-out;
  }
}

/* Animation for card interaction */
@keyframes float-shadow {
  0% { transform: translateY(0) scale(1); opacity: 0.5; }
  50% { transform: translateY(5px) scale(1.05); opacity: 0.7; }
  100% { transform: translateY(0) scale(1); opacity: 0.5; }
}

.animate-shadow {
  animation: float-shadow 6s ease-in-out infinite;
}

/* Premium card effects */
.premium-card-glow {
  position: relative;
}

.premium-card-glow::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #ec4899, #1a3a6d, #ec4899);
  z-index: -1;
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.4s ease;
}

.premium-card-glow:hover::before {
  opacity: 0.7;
  animation: rotate-gradient 3s linear infinite;
}

@keyframes rotate-gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Enhanced hover interactions */
.hover-lift {
  transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.hover-lift:hover {
  transform: translateY(-10px);
}

/* Premium button effects */
.btn-premium {
  position: relative;
  overflow: hidden;
  z-index: 1;
  transition: all 0.4s ease;
}

.btn-premium::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, #ec4899, #be185d);
  z-index: -1;
  transition: opacity 0.4s ease;
}

.btn-premium::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, #1a3a6d, #0f172a);
  z-index: -2;
  opacity: 0;
  transition: opacity 0.4s ease;
}

.btn-premium:hover::after {
  opacity: 1;
}

/* Blog post animations */
@keyframes fadeInSlow {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.animate-fade-in-slow {
  animation: fadeInSlow 1.5s ease-out forwards;
}

/* Scroll behavior for anchor links */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #1a3a6d;
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: #0f2d5c;
}

/* Enhanced blog typography */
.prose h2 {
  color: #1a3a6d;
  font-weight: 700;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.prose p {
  margin-bottom: 1.5rem;
  line-height: 1.8;
}

.prose a {
  color: #1a3a6d;
  text-decoration: underline;
  text-decoration-color: #ec4899;
  text-underline-offset: 3px;
  transition: all 0.2s ease;
}

.prose a:hover {
  color: #ec4899;
}

.prose ul {
  margin-left: 1.5rem;
  margin-bottom: 1.5rem;
}

.prose ul li {
  margin-bottom: 0.5rem;
}

/* Image hover effects */
.img-hover-zoom {
  overflow: hidden;
}

.img-hover-zoom img {
  transition: transform 0.75s ease;
}

.img-hover-zoom:hover img {
  transform: scale(1.05);
}

/* Enhanced scroll margins for anchor links */
.scroll-mt-24 {
  scroll-margin-top: 6rem;
}

/* Allow line clamping for text */
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

/* Performance optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-auto {
  will-change: auto;
}

/* Reduce layout shift for images */
.aspect-ratio-16-9 {
  aspect-ratio: 16 / 9;
}

.aspect-ratio-4-3 {
  aspect-ratio: 4 / 3;
}

.aspect-ratio-square {
  aspect-ratio: 1 / 1;
}

/* Critical rendering optimizations */
.critical-above-fold {
  contain: layout style paint;
}

/* Defer non-critical animations on mobile */
@media (max-width: 768px) {
  .animate-float,
  .animate-float-slow,
  .animate-pulse-slow {
    animation: none;
  }
  
  /* Reduce blur effects on mobile for performance */
  .blur-3xl {
    filter: blur(20px);
  }
}

/* GPU-optimized transforms */
.transform-gpu {
  transform: translateZ(0);
  will-change: transform;
}

/* Composite layer promotion for animations */
.composite-layer {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Optimized hover effects */
.hover-scale {
  transition: transform 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.hover-scale:hover {
  transform: scale(1.05) translateZ(0);
}

/* Layout shift prevention */
.prevent-layout-shift {
  contain: layout;
}

/* Fixed dimensions to prevent layout shifts */
.fixed-dimensions {
  min-height: 0;
  min-width: 0;
}

/* Force GPU acceleration for all animations */
.animate-fade-in {
  animation: fadeIn 1.2s var(--motion-smooth) forwards;
  transform: translateZ(0);
}

.animate-slide-up {
  animation: slideUp 1.2s var(--motion-smooth) forwards;
  transform: translateZ(0);
}

.animate-slide-right {
  animation: slideInRight 1s var(--motion-smooth) forwards;
  transform: translateZ(0);
}

.animate-pulse-slow {
  animation: pulse 4s var(--motion-bounce) infinite;
  transform: translateZ(0);
}

.animate-shimmer {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.5) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 3s infinite;
  transform: translateZ(0);
}

.animate-fade-blur {
  animation: fadeInBlur 1.5s var(--motion-smooth) forwards;
  transform: translateZ(0);
}

.animate-spin-slow {
  animation: spin 12s linear infinite;
  transform: translateZ(0);
}

/* Float animation variants with GPU acceleration */
.animate-float {
  animation: float 6s var(--motion-smooth) infinite;
  transform: translateZ(0);
}

.animate-float-1 {
  animation: float 6s var(--motion-smooth) infinite;
  transform: translateZ(0);
}

.animate-float-2 {
  animation: float 7s var(--motion-smooth) infinite 0.5s;
  transform: translateZ(0);
}

.animate-float-3 {
  animation: float 8s var(--motion-smooth) infinite 1s;
  transform: translateZ(0);
}

.animate-bounce-slow {
  animation: bounceSlow 3s var(--motion-swing) infinite;
  transform: translateZ(0);
}

.animate-float-slow {
  animation: float-slow 18s ease-in-out infinite;
  transform: translateZ(0);
}

/* Optimize testimonial carousel animations */
.testimonial-carousel {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Optimize before/after slider */
.before-after-slider {
  will-change: auto;
  contain: layout style;
  max-width: 100%;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

/* Ensure before/after slider is fully responsive */
@media (max-width: 640px) {
  .before-after-slider {
    margin: 0;
    padding: 0;
    max-width: 100vw;
  }
}

.before-after-handle {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Optimized transitions for common elements */
.optimized-transition {
  transition: transform 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  will-change: transform;
  transform: translateZ(0);
}

/* Prevent layout shifts in dynamic content */
.dynamic-content {
  min-height: 1px;
  contain: layout;
}

/* Container query optimizations */
@container (max-width: 768px) {
  .mobile-optimized {
    animation: none;
    transform: none;
  }
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  .animate-float,
  .animate-float-slow,
  .animate-pulse-slow,
  .animate-shimmer,
  .animate-bounce-slow,
  .animate-spin-slow,
  .animate-fade-blur {
    animation: none;
  }
  
  .optimized-transition {
    transition: none;
  }
}

/* Force hardware acceleration for image containers */
.image-container {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Optimize gallery grid layout */
.gallery-grid {
  contain: layout style;
}

.gallery-item {
  contain: layout;
  will-change: transform;
  transform: translateZ(0);
}

/* Accessible color utilities for WCAG AA compliance */
.text-accessible-white {
  color: #ffffff;
}

.text-accessible-light {
  color: #f8fafc; /* High contrast alternative to white/90 and white/80 */
}

.text-accessible-muted {
  color: #e2e8f0; /* High contrast alternative to white/70 */
}

.text-accessible-subtle {
  color: #cbd5e1; /* High contrast alternative to white/60 */
}

.text-accessible-gray {
  color: #475569; /* High contrast alternative to gray-500 */
}

.text-accessible-gray-dark {
  color: #334155; /* High contrast alternative to gray-400 */
}

/* Additional accessible utilities */
.bg-accessible-gray {
  background-color: #475569;
}

.border-accessible-gray {
  border-color: #475569;
}

.hover\:text-accessible-white:hover {
  color: #ffffff;
}

.hover\:text-accessible-light:hover {
  color: #f8fafc !important;
}

/* Mobile-specific hero optimizations */
@media (max-width: 640px) {
  .hero-mobile-fix {
    min-height: 100vh;
    min-height: 100dvh; /* Dynamic viewport height for mobile browsers */
  }
  
  .hero-content-mobile {
    padding-top: max(env(safe-area-inset-top), 4rem);
    padding-bottom: max(env(safe-area-inset-bottom), 2rem);
  }
  
  .mobile-text-responsive {
    font-size: clamp(1.5rem, 8vw, 2.5rem);
    line-height: 1.2;
  }
  
  .mobile-description {
    font-size: clamp(0.875rem, 4vw, 1rem);
    line-height: 1.5;
  }
  
  /* Reduce blur intensity on mobile for performance */
  .blur-mobile-optimized {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }
  
  /* Optimize animations for mobile */
  .animate-mobile-safe {
    animation-duration: 1.5s;
    animation-timing-function: ease-out;
  }
}

/* Tablet optimizations */
@media (min-width: 641px) and (max-width: 1024px) {
  .hero-tablet-fix {
    min-height: 100vh;
  }
  
  .tablet-text-responsive {
    font-size: clamp(2rem, 6vw, 3.5rem);
  }
}

/* Safe area insets for notched devices */
@supports (padding: max(0px)) {
  .safe-area-inset-top {
    padding-top: max(1rem, env(safe-area-inset-top));
  }
  
  .safe-area-inset-bottom {
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }
}

.hover\:text-accessible-light:hover {
  color: #f8fafc !important;
}
