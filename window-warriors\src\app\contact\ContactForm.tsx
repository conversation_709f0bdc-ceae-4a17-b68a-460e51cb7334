'use client';

import { useState, ChangeEvent, FormEvent } from 'react';
import Link from 'next/link';

interface FormData {
  name: string;
  email: string;
  phone: string;
  subject: string;
  message: string;
  serviceInterest: string;
}

interface FormErrors {
  [key: string]: string;
}

const ContactForm = () => {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
    serviceInterest: 'Not specified'
  });
  
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<null | 'success' | 'error'>(null);
  
  const validate = (): FormErrors => {
    const newErrors: FormErrors = {};
    
    if (!formData.name.trim()) newErrors['name'] = 'Name is required';
    
    if (!formData.email.trim()) {
      newErrors['email'] = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors['email'] = 'Email is invalid';
    }
    
    if (!formData.phone.trim()) {
      newErrors['phone'] = 'Phone number is required';
    }
    
    if (!formData.message.trim()) newErrors['message'] = 'Message is required';
    
    return newErrors;
  };
  
  const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  const submitToGoHighLevel = async (data: FormData): Promise<boolean> => {
    try {
      console.log('Submitting form data:', data);
      
      // Use our internal API route instead of calling GoHighLevel directly
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('API response:', response.status, errorText);
        throw new Error(`API error: ${response.status}`);
      }
      
      const responseData = await response.json();
      console.log('API response:', responseData);
      return true;
    } catch (error) {
      console.error('Error submitting form:', error);
      return false;
    }
  };
  
  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    const formErrors = validate();
    setErrors(formErrors);
    
    if (Object.keys(formErrors).length === 0) {
      setIsSubmitting(true);
      
      try {
        // Submit to Go High Level
        const ghlSuccess = await submitToGoHighLevel(formData);
        
        if (ghlSuccess) {
          setSubmitStatus('success');
          
          // Reset form after successful submission
          setFormData({
            name: '',
            email: '',
            phone: '',
            subject: '',
            message: '',
            serviceInterest: 'Not specified'
          });
          
          // Reset success message after 5 seconds
          setTimeout(() => {
            setSubmitStatus(null);
          }, 5000);
        } else {
          setSubmitStatus('error');
        }
      } catch (error) {
        console.error('Error submitting form:', error);
        setSubmitStatus('error');
      } finally {
        setIsSubmitting(false);
      }
    }
  };
  
  return (
    <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-5" id="form">
      {submitStatus === 'success' && (
        <div className="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg animate-fade-in">
          Thank you! Your message has been sent. Our team will contact you shortly.
        </div>
      )}
      
      {submitStatus === 'error' && (
        <div className="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg animate-fade-in">
          There was an error sending your message. Please try again or contact us directly.
        </div>
      )}
    
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-5">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-accessible-gray mb-1">
            Name <span className="text-[#ec4899]">*</span>
          </label>
          <input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            className={`w-full px-4 py-2 bg-white border rounded-lg focus:ring-2 focus:ring-[#ec4899]/40 focus:border-[#ec4899] transition-colors duration-300 text-gray-900 ${
              errors['name'] ? 'border-red-500 bg-red-50' : 'border-gray-300'
            }`}
            placeholder="Your name"
          />
          {errors['name'] && <p className="mt-1 text-sm text-red-600">{errors['name']}</p>}
        </div>
        
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-accessible-gray mb-1">
            Email <span className="text-[#ec4899]">*</span>
          </label>
          <input
            type="email"
            id="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            className={`w-full px-4 py-2 bg-white border rounded-lg focus:ring-2 focus:ring-[#ec4899]/40 focus:border-[#ec4899] transition-colors duration-300 text-gray-900 ${
              errors['email'] ? 'border-red-500 bg-red-50' : 'border-gray-300'
            }`}
            placeholder="Your email"
          />
          {errors['email'] && <p className="mt-1 text-sm text-red-600">{errors['email']}</p>}
        </div>
      </div>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-5">
        <div>
          <label htmlFor="phone" className="block text-sm font-medium text-accessible-gray mb-1">
            Phone Number <span className="text-[#ec4899]">*</span>
          </label>
          <input
            type="tel"
            id="phone"
            name="phone"
            value={formData.phone}
            onChange={handleChange}
            className={`w-full px-4 py-2 bg-white border rounded-lg focus:ring-2 focus:ring-[#ec4899]/40 focus:border-[#ec4899] transition-colors duration-300 text-gray-900 ${
              errors['phone'] ? 'border-red-500 bg-red-50' : 'border-gray-300'
            }`}
            placeholder="Your phone number"
          />
          {errors['phone'] && <p className="mt-1 text-sm text-red-600">{errors['phone']}</p>}
        </div>
        
        <div>
          <label htmlFor="serviceInterest" className="block text-sm font-medium text-accessible-gray mb-1">
            Service Interest
          </label>
          <select
            id="serviceInterest"
            name="serviceInterest"
            value={formData.serviceInterest}
            onChange={handleChange}
            className="w-full px-4 py-2 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#ec4899]/40 focus:border-[#ec4899] transition-colors duration-300 text-gray-900"
          >
            <option value="Not specified" className="bg-white text-gray-900">Select a service</option>
            <option value="Windows" className="bg-white text-gray-900">UPVC Windows</option>
            <option value="Doors" className="bg-white text-gray-900">Composite Doors</option>
            <option value="Conservatories" className="bg-white text-gray-900">Conservatories</option>
            <option value="Repairs" className="bg-white text-gray-900">Repairs & Maintenance</option>
            <option value="Other" className="bg-white text-gray-900">Other</option>
          </select>
        </div>
      </div>
      
      <div>
        <label htmlFor="subject" className="block text-sm font-medium text-accessible-gray mb-1">
          Subject
        </label>
        <input
          type="text"
          id="subject"
          name="subject"
          value={formData.subject}
          onChange={handleChange}
          className="w-full px-4 py-2 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#ec4899]/40 focus:border-[#ec4899] transition-colors duration-300 text-gray-900"
          placeholder="Subject of your message"
        />
      </div>
      
      <div>
        <label htmlFor="message" className="block text-sm font-medium text-accessible-gray mb-1">
          Message <span className="text-[#ec4899]">*</span>
        </label>
        <textarea
          id="message"
          name="message"
          value={formData.message}
          onChange={handleChange}
          rows={5}
          className={`w-full px-4 py-2 bg-white border rounded-lg focus:ring-2 focus:ring-[#ec4899]/40 focus:border-[#ec4899] transition-colors duration-300 text-gray-900 ${
            errors['message'] ? 'border-red-500 bg-red-50' : 'border-gray-300'
          }`}
          placeholder="How can we help you?"
        ></textarea>
        {errors['message'] && <p className="mt-1 text-sm text-red-600">{errors['message']}</p>}
      </div>
      
      <div className="flex items-start">
        <input
          id="privacy"
          name="privacy"
          type="checkbox"
          required
          className="h-4 w-4 mt-1 text-[#ec4899] focus:ring-[#ec4899] border-gray-300 rounded bg-white"
        />
        <label htmlFor="privacy" className="ml-2 text-sm text-accessible-gray">
          I agree to the{' '}
          <Link href="/privacy-policy" className="text-[#ec4899] hover:text-[#be185d] underline transition-colors">
            privacy policy
          </Link>{' '}
          and consent to being contacted regarding my inquiry.
        </label>
      </div>
      
      <div className="pt-2">
        <button
          type="submit"
          disabled={isSubmitting}
          className={`w-full sm:w-auto px-6 py-3 rounded-full text-white font-medium transition-all duration-300 btn-premium
            ${isSubmitting ? 'opacity-70 cursor-not-allowed' : 'hover:shadow-lg hover:shadow-[#ec4899]/20 hover:-translate-y-1'}
          `}
        >
          <span className="relative z-10 flex items-center justify-center">
            {isSubmitting ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Sending...
              </>
            ) : 'Send Message'}
          </span>
        </button>
      </div>
    </form>
  );
};

export default ContactForm; 