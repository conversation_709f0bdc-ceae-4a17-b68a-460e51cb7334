import { ReactNode } from "react";
import Image from "next/image";
import <PERSON>ript from "next/script";
import Header from "@/components/Header";

export default function BlogPostLayout({
  children,
}: {
  children: ReactNode;
}) {
  return (
    <div className="bg-gradient-to-b from-white to-gray-50/50">
      <Header />
      <div className="font-sans text-gray-900 antialiased selection:bg-[#1a3a6d]/20 selection:text-[#1a3a6d]">
        {/* Reading Progress Bar - Fixed to top - Mobile Optimized */}
        <div className="fixed top-0 left-0 right-0 h-1 sm:h-1.5 z-50 bg-gray-100">
          <div 
            id="reading-progress" 
            className="h-full bg-gradient-to-r from-[#1a3a6d] to-[#ec4899] w-0 transition-all duration-200"
          ></div>
        </div>
        
        {/* Main Content with enhanced typography settings - Mobile Optimized */}
        <div className="tracking-normal text-sm sm:text-base leading-relaxed sm:leading-loose">
          {children}
        </div>
        
        {/* Return to Top Button - Fixed to bottom right - Mobile Optimized */}
        <button 
          id="return-to-top" 
          className="fixed bottom-4 sm:bottom-6 right-4 sm:right-6 bg-[#1a3a6d] text-white p-2.5 sm:p-3 rounded-full shadow-lg opacity-0 invisible transition-all duration-300 hover:bg-[#ec4899] hover:scale-110 z-40"
          aria-label="Return to top of page"
        >
          <svg className="w-4 sm:w-5 h-4 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
          </svg>
        </button>
        
        {/* Client-side JavaScript using Next.js Script component to avoid hydration issues */}
        <Script id="blog-post-script" strategy="afterInteractive">
          {`
            // Reading progress functionality
            const progressBar = document.getElementById('reading-progress');
            const content = document.querySelector('main');
            
            window.addEventListener('scroll', () => {
              if (content) {
                const scrollTop = window.scrollY;
                const scrollHeight = content.offsetHeight - window.innerHeight;
                const progress = scrollHeight > 0 ? (scrollTop / scrollHeight) * 100 : 0;
                progressBar.style.width = \`\${progress}%\`;
              }
            });
            
            // Return to top button functionality
            const returnToTopBtn = document.getElementById('return-to-top');
            
            window.addEventListener('scroll', () => {
              if (window.scrollY > 300) { // Reduced threshold for mobile
                returnToTopBtn.classList.remove('opacity-0', 'invisible');
                returnToTopBtn.classList.add('opacity-100', 'visible');
              } else {
                returnToTopBtn.classList.add('opacity-0', 'invisible');
                returnToTopBtn.classList.remove('opacity-100', 'visible');
              }
            });
            
            returnToTopBtn.addEventListener('click', () => {
              window.scrollTo({
                top: 0,
                behavior: 'smooth'
              });
            });
            
            // Add smooth scrolling to all internal links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
              anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                  target.scrollIntoView({
                    behavior: 'smooth'
                  });
                }
              });
            });
          `}
        </Script>
      </div>
    </div>
  );
} 
