import Image from 'next/image';

type Benefit = {
  id: number;
  title: string;
  description: string;
  icon: React.ReactNode;
};

interface BenefitsSectionProps {
  title?: string;
  subtitle?: string;
  benefits: Benefit[];
  className?: string;
}

const BenefitsSection = ({
  title = "Why Choose Our UPVC Windows & Doors",
  subtitle = "Discover the advantages that make our products stand out",
  benefits,
  className = '',
}: BenefitsSectionProps) => {
  return (
    <section className={`py-16 bg-gray-50 ${className}`}>
      <div className="container mx-auto px-4">
        {/* Section header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#1a3a6d] mb-4">{title}</h2>
          <p className="text-accessible-gray max-w-2xl mx-auto">{subtitle}</p>
        </div>
        
        {/* Benefits grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {benefits.map((benefit) => (
            <div 
              key={benefit.id}
              className="bg-white rounded-lg shadow-md p-6 transition-transform hover:scale-105 hover:shadow-lg"
            >
              <div className="flex flex-col items-center text-center">
                <div className="w-16 h-16 flex items-center justify-center bg-[#1a3a6d]/10 text-[#1a3a6d] rounded-full mb-4">
                  {benefit.icon}
                </div>
                <h3 className="text-xl font-bold text-[#1a3a6d] mb-3">{benefit.title}</h3>
                <p className="text-accessible-gray">{benefit.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default BenefitsSection; 