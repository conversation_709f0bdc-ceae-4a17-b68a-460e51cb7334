import { Metadata } from 'next';
import SalesForm from './SalesForm';
import SalesCalendar from './SalesCalendar';
import ClientGHLForm from './ClientGHLForm';
import PinProtection from './PinProtection';

export const metadata: Metadata = {
  title: 'Sales Calendar | Window Warriors - Internal Use',
  description: 'Internal sales calendar for Window Warriors sales representatives to book appointments with clients.',
  robots: {
    index: false,
    follow: false,
  },
};

export default function SalesCalendarPage() {
  return (
    <PinProtection>
      <main className="flex min-h-screen flex-col">
      {/* Enhanced Hero Section - Mobile Optimized */}
      <section className="relative min-h-[500px] sm:min-h-[600px] lg:min-h-screen pt-20 sm:pt-24 md:pt-28 lg:pt-32 pb-12 sm:pb-16 md:pb-20 lg:pb-24 overflow-hidden">
        {/* Background gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-[#1a3a6d] to-[#ec4899]/30"></div>
        
        {/* Decorative elements */}
        <div className="hidden md:block absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-[#ec4899]/10 rounded-full blur-xl"></div>
          <div className="absolute bottom-1/3 right-1/4 w-24 h-24 bg-[#d946ef]/10 rounded-full blur-xl"></div>
        </div>
        
        {/* Hero Content */}
        <div className="container mx-auto px-4 sm:px-6 relative z-10 flex flex-col justify-center min-h-[400px] sm:min-h-[500px] lg:min-h-[calc(100vh-8rem)]">
          <div className="max-w-4xl animate-fade-in" style={{ animationDelay: '0.3s' }}>
            {/* Brand badge */}
            <div className="inline-flex items-center mb-4 sm:mb-6 md:mb-8">
              <div className="bg-white/10 backdrop-blur-sm border border-[#ec4899]/30 rounded-full px-4 sm:px-6 py-1.5 sm:py-2 animate-fade-blur" style={{ animationDelay: '0.2s' }}>
                <span className="text-accessible-light text-xs sm:text-sm md:text-base font-medium">SALES TEAM</span>
                <div className="w-full h-px bg-gradient-to-r from-transparent via-[#ec4899]/70 to-transparent mt-1"></div>
              </div>
            </div>
            
            {/* Title */}
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold text-white mb-3 sm:mb-4 md:mb-6 leading-tight">
              Sales <span className="relative inline-block">
                <span className="text-gradient-logo">Calendar</span>
                <div className="absolute -bottom-1 sm:-bottom-2 left-0 w-full h-0.5 sm:h-1 bg-gradient-to-r from-[#ec4899] to-[#d946ef] rounded-full animate-shimmer"></div>
              </span>
            </h1>
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-3 sm:mb-4 md:mb-6">
              Book Client Appointments
            </h2>
            
            {/* Description */}
            <div className="relative mb-4 sm:mb-6 md:mb-8">
              <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-accessible-light leading-relaxed animate-slide-up" style={{ animationDelay: '0.6s' }}>
                Streamlined two-step process: First collect client information, then book their appointment without re-entering details.
              </p>
              <div className="absolute -left-2 sm:-left-4 top-0 w-0.5 sm:w-1 h-full bg-gradient-to-b from-[#ec4899] to-transparent rounded-full opacity-60"></div>
            </div>
            
            {/* Feature badges */}
            <div className="animate-fade-in" style={{ animationDelay: '1.2s' }}>
              <p className="text-white/70 text-xs sm:text-sm md:text-base mb-3 sm:mb-4">Perfect for</p>
              <div className="flex flex-wrap gap-2 sm:gap-3 md:gap-4">
                <div className="flex items-center gap-1.5 sm:gap-2 bg-white/10 backdrop-blur-sm border border-white/20 px-3 sm:px-4 py-1.5 sm:py-2 rounded-full">
                  <div className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 bg-[#ec4899]/20 rounded-full flex items-center justify-center">
                    <svg className="w-2.5 h-2.5 sm:w-3 sm:h-3 md:w-4 md:h-4 text-[#ec4899]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <span className="text-white text-xs sm:text-sm font-medium">Client Info Collection</span>
                </div>
                <div className="flex items-center gap-1.5 sm:gap-2 bg-white/10 backdrop-blur-sm border border-white/20 px-3 sm:px-4 py-1.5 sm:py-2 rounded-full">
                  <div className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 bg-[#ec4899]/20 rounded-full flex items-center justify-center">
                    <svg className="w-2.5 h-2.5 sm:w-3 sm:h-3 md:w-4 md:h-4 text-[#ec4899]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <span className="text-white text-xs sm:text-sm font-medium">Instant Booking</span>
                </div>
                <div className="flex items-center gap-1.5 sm:gap-2 bg-white/10 backdrop-blur-sm border border-white/20 px-3 sm:px-4 py-1.5 sm:py-2 rounded-full">
                  <div className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 bg-[#ec4899]/20 rounded-full flex items-center justify-center">
                    <svg className="w-2.5 h-2.5 sm:w-3 sm:h-3 md:w-4 md:h-4 text-[#ec4899]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <span className="text-white text-xs sm:text-sm font-medium">No Duplicate Entry</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Client Information Section - Mobile Optimized */}
      <section className="py-12 sm:py-16 md:py-20 lg:py-24 bg-gray-50 relative overflow-hidden" id="client-info">
        {/* Decorative elements */}
        <div className="absolute top-0 left-0 w-full h-full pointer-events-none">
          <div className="absolute top-0 right-0 w-64 sm:w-80 md:w-96 h-64 sm:h-80 md:h-96 bg-[#ec4899]/5 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 left-0 w-48 sm:w-56 md:w-64 h-48 sm:h-56 md:h-64 bg-[#d946ef]/5 rounded-full blur-3xl"></div>
        </div>

        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-8 sm:mb-12 md:mb-16">
              <span className="inline-block text-[#ec4899] font-medium mb-2 sm:mb-3 md:mb-4 text-xs sm:text-sm md:text-base">STEP 1</span>
              <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-3 sm:mb-4 md:mb-6 text-[#1a3a6d]">
                Collect Client <span className="text-gradient-logo">Information</span>
              </h2>
              <p className="text-sm sm:text-base md:text-lg lg:text-xl text-accessible-gray max-w-2xl mx-auto">
                Have your client fill out their information below. After submission, their details will be displayed for you to use in the booking calendar.
              </p>
            </div>

            {/* Form Container */}
            <div className="bg-white border border-gray-200 p-4 sm:p-6 md:p-8 rounded-xl hover:border-[#ec4899]/50 hover:shadow-lg hover:shadow-[#ec4899]/10 transition-all duration-300">
              <SalesForm />
            </div>
          </div>
        </div>
      </section>

      {/* Appointment Booking Section - Mobile Optimized */}
      <section className="py-12 sm:py-16 md:py-20 lg:py-24 bg-white relative overflow-hidden" id="booking">
        {/* Decorative elements */}
        <div className="absolute top-0 left-0 w-full h-full pointer-events-none">
          <div className="absolute top-0 left-0 w-64 sm:w-80 md:w-96 h-64 sm:h-80 md:h-96 bg-[#d946ef]/5 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 right-0 w-48 sm:w-56 md:w-64 h-48 sm:h-56 md:h-64 bg-[#ec4899]/5 rounded-full blur-3xl"></div>
        </div>

        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-8 sm:mb-12 md:mb-16">
              <span className="inline-block text-[#ec4899] font-medium mb-2 sm:mb-3 md:mb-4 text-xs sm:text-sm md:text-base">STEP 2</span>
              <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-3 sm:mb-4 md:mb-6 text-[#1a3a6d]">
                Book Client <span className="text-gradient-logo">Appointment</span>
              </h2>
              <p className="text-sm sm:text-base md:text-lg lg:text-xl text-accessible-gray max-w-2xl mx-auto">
                Use the calendar below to schedule the appointment. Enter the client's information from Step 1 into the booking form.
              </p>
            </div>

            {/* Calendar Container */}
            <div className="bg-white border border-gray-200 p-4 sm:p-6 md:p-8 rounded-xl hover:border-[#ec4899]/50 hover:shadow-lg hover:shadow-[#ec4899]/10 transition-all duration-300">
              <SalesCalendar />
            </div>
          </div>
        </div>
      </section>

      {/* Instructions Section - Mobile Optimized */}
      <section className="py-12 sm:py-16 md:py-20 bg-white relative overflow-hidden">
        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <span className="inline-block text-[#ec4899] font-medium mb-2 sm:mb-3 md:mb-4 text-xs sm:text-sm md:text-base">INSTRUCTIONS</span>
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-3 sm:mb-4 md:mb-6 text-[#1a3a6d]">How to Use This Calendar</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 sm:gap-8 mt-8 sm:mt-12">
              <div className="text-center">
                <div className="w-12 h-12 sm:w-16 sm:h-16 bg-[#ec4899]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-[#ec4899] font-bold text-lg sm:text-xl">1</span>
                </div>
                <h3 className="text-lg sm:text-xl font-semibold text-[#1a3a6d] mb-2">Collect Client Info</h3>
                <p className="text-sm sm:text-base text-accessible-gray">Fill out the client information form</p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 sm:w-16 sm:h-16 bg-[#ec4899]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-[#ec4899] font-bold text-lg sm:text-xl">2</span>
                </div>
                <h3 className="text-lg sm:text-xl font-semibold text-[#1a3a6d] mb-2">Book Appointment</h3>
                <p className="text-sm sm:text-base text-accessible-gray">Schedule using the calendar</p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 sm:w-16 sm:h-16 bg-[#ec4899]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-[#ec4899] font-bold text-lg sm:text-xl">3</span>
                </div>
                <h3 className="text-lg sm:text-xl font-semibold text-[#1a3a6d] mb-2">Direct Submission</h3>
                <p className="text-sm sm:text-base text-accessible-gray">Client submits info directly to system</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Client Direct Submission Section - Mobile Optimized */}
      <section className="py-12 sm:py-16 md:py-20 lg:py-24 bg-gray-50 relative overflow-hidden" id="client-submission">
        {/* Decorative elements */}
        <div className="absolute top-0 left-0 w-full h-full pointer-events-none">
          <div className="absolute top-0 right-0 w-64 sm:w-80 md:w-96 h-64 sm:h-80 md:h-96 bg-[#ec4899]/5 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 left-0 w-48 sm:w-56 md:w-64 h-48 sm:h-56 md:h-64 bg-[#d946ef]/5 rounded-full blur-3xl"></div>
        </div>

        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-8 sm:mb-12 md:mb-16">
              <span className="inline-block text-[#ec4899] font-medium mb-2 sm:mb-3 md:mb-4 text-xs sm:text-sm md:text-base">STEP 3 - ALTERNATIVE</span>
              <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-3 sm:mb-4 md:mb-6 text-[#1a3a6d]">
                Client Direct <span className="text-gradient-logo">Submission</span>
              </h2>
              <p className="text-sm sm:text-base md:text-lg lg:text-xl text-accessible-gray max-w-2xl mx-auto">
                If the client prefers to submit their information directly to our system without booking an appointment immediately, they can use this form.
              </p>
            </div>

            {/* GHL Form Container */}
            <div className="bg-white border border-gray-200 p-4 sm:p-6 md:p-8 rounded-xl hover:border-[#ec4899]/50 hover:shadow-lg hover:shadow-[#ec4899]/10 transition-all duration-300">
              <ClientGHLForm />
            </div>
          </div>
        </div>
      </section>
    </main>
    </PinProtection>
  );
}
