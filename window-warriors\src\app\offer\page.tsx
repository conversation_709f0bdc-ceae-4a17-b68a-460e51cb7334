import Image from "next/image";
import Link from "next/link";
import { Metadata } from 'next';
import BookingCalendar from "@/components/BookingCalendar";

export const metadata: Metadata = {
  title: 'Special Offer | 25% Lifetime Discount | Window Warriors',
  description: 'Limited time offer - Get 25% lifetime discount on all premium window installations! Book your free appointment now.',
  keywords: 'window discount, special offer, window installation, UPVC windows, Newcastle, Durham, North East',
  openGraph: {
    title: 'Special Offer | 25% Lifetime Discount | Window Warriors',
    description: 'Limited time offer - Get 25% lifetime discount on all premium window installations! Book your free appointment now.',
    url: 'https://windowwarriors.uk/offer',
    siteName: 'Window Warriors',
    images: [
      {
        url: 'https://windowwarriors.uk/images/exterior-home.jpg',
        width: 1200,
        height: 630,
        alt: 'Window Warriors - Special Offer',
      },
    ],
    locale: 'en_GB',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Special Offer | 25% Lifetime Discount | Window Warriors',
    description: 'Limited time offer - Get 25% lifetime discount on all premium window installations! Book your free appointment now.',
    images: ['https://windowwarriors.uk/images/exterior-home.jpg'],
  },
  alternates: {
    canonical: 'https://windowwarriors.uk/offer',
  },
};

export default function OfferPage() {
  return (
    <main className="flex min-h-screen flex-col">
      {/* Enhanced Hero Section - Mobile Optimized */}
      <section className="relative min-h-[500px] sm:min-h-[600px] lg:min-h-screen pt-20 sm:pt-24 md:pt-28 lg:pt-32 pb-12 sm:pb-16 md:pb-20 lg:pb-24 overflow-hidden">
        {/* Simplified background */}
        <div className="absolute inset-0 bg-gradient-to-br from-[#1a3a6d] to-[#ec4899]/30"></div>
        
        {/* Simplified decorative elements */}
        <div className="hidden md:block absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-[#ec4899]/10 rounded-full blur-xl"></div>
        </div>
        
        {/* Enhanced Hero Content - Mobile Optimized */}
        <div className="container mx-auto px-4 sm:px-6 relative z-10 flex flex-col justify-center min-h-[400px] sm:min-h-[500px] lg:min-h-[calc(100vh-8rem)]">
          <div className="max-w-4xl animate-fade-in" style={{ animationDelay: '0.3s' }}>
            {/* Enhanced brand badge - Mobile responsive */}
            <div className="inline-flex items-center mb-4 sm:mb-6 md:mb-8">
              <div className="bg-white/10 backdrop-blur-sm border border-[#ec4899]/30 rounded-full px-4 sm:px-6 py-1.5 sm:py-2 animate-fade-blur" style={{ animationDelay: '0.2s' }}>
                <span className="text-accessible-light text-xs sm:text-sm md:text-base font-medium">SPECIAL OFFER</span>
                <div className="w-full h-px bg-gradient-to-r from-transparent via-[#ec4899]/70 to-transparent mt-1"></div>
              </div>
            </div>
            
            {/* Enhanced title with pink gradients - Mobile responsive */}
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl 2xl:text-8xl font-bold text-white mb-3 sm:mb-4 md:mb-6 leading-tight">
              Get <span className="relative inline-block">
                <span className="text-gradient-logo">25% Off</span>
                <div className="absolute -bottom-1 sm:-bottom-2 left-0 w-full h-0.5 sm:h-1 bg-gradient-to-r from-[#ec4899] to-[#d946ef] rounded-full animate-shimmer"></div>
                <div className="absolute -top-0.5 sm:-top-1 -right-0.5 sm:-right-1 w-2 sm:w-3 h-2 sm:h-3 bg-[#ec4899]/60 rounded-full animate-pulse"></div>
              </span>
            </h1>
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold text-white mb-3 sm:mb-4 md:mb-6">
              For Life
            </h2>
            
            {/* Enhanced description - Mobile responsive */}
            <div className="relative mb-4 sm:mb-6 md:mb-8">
              <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-accessible-light leading-relaxed animate-slide-up" style={{ animationDelay: '0.6s' }}>
                Limited time offer on all premium window installations. Schedule your appointment below to claim your discount and book a free consultation.
              </p>
              <div className="absolute -left-2 sm:-left-4 top-0 w-0.5 sm:w-1 h-full bg-gradient-to-b from-[#ec4899] to-transparent rounded-full opacity-60"></div>
            </div>
            
            {/* Enhanced buttons - Mobile responsive */}
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 md:gap-6 mb-6 sm:mb-8 md:mb-10 animate-slide-up" style={{ animationDelay: '0.9s' }}>
              <a href="#claim-form" className="btn-primary text-center text-sm sm:text-base px-6 sm:px-8 py-3 sm:py-4">
                Claim Your 25% Discount
              </a>
              <Link href="/products-services" className="inline-block bg-white/10 backdrop-blur-sm border-2 border-white/30 text-white hover:bg-white hover:text-[#1a3a6d] font-medium py-3 sm:py-4 px-6 sm:px-8 rounded-full transition-all duration-300 text-center group text-sm sm:text-base">
                <span className="flex items-center justify-center">
                  <svg className="w-4 sm:w-5 h-4 sm:h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                  </svg>
                  Explore Our Products
                </span>
              </Link>
            </div>
            
            {/* Enhanced feature badges - Mobile responsive */}
            <div className="animate-fade-in" style={{ animationDelay: '1.2s' }}>
              <p className="text-white/70 text-xs sm:text-sm md:text-base mb-3 sm:mb-4">What's included</p>
              <div className="flex flex-wrap gap-2 sm:gap-3 md:gap-4">
                <div className="flex items-center gap-1.5 sm:gap-2 bg-white/10 backdrop-blur-sm border border-white/20 px-3 sm:px-4 py-1.5 sm:py-2 rounded-full">
                  <div className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 bg-[#ec4899]/20 rounded-full flex items-center justify-center">
                    <svg className="w-2.5 h-2.5 sm:w-3 sm:h-3 md:w-4 md:h-4 text-[#ec4899]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <span className="text-white text-xs sm:text-sm font-medium">A-Rated Windows</span>
                </div>
                <div className="flex items-center gap-1.5 sm:gap-2 bg-white/10 backdrop-blur-sm border border-white/20 px-3 sm:px-4 py-1.5 sm:py-2 rounded-full">
                  <div className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 bg-[#ec4899]/20 rounded-full flex items-center justify-center">
                    <svg className="w-2.5 h-2.5 sm:w-3 sm:h-3 md:w-4 md:h-4 text-[#ec4899]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <span className="text-white text-xs sm:text-sm font-medium">10 Year Guarantee</span>
                </div>
                <div className="flex items-center gap-1.5 sm:gap-2 bg-white/10 backdrop-blur-sm border border-white/20 px-3 sm:px-4 py-1.5 sm:py-2 rounded-full">
                  <div className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 bg-[#ec4899]/20 rounded-full flex items-center justify-center">
                    <svg className="w-2.5 h-2.5 sm:w-3 sm:h-3 md:w-4 md:h-4 text-[#ec4899]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <span className="text-white text-xs sm:text-sm font-medium">Expert Installation</span>
                </div>
              </div>
            </div>
          </div>
          
          {/* Modern scroll indicator - Mobile responsive */}
          <div className="mt-auto pt-6 sm:pt-8 flex justify-center animate-bounce-slow">
            <div className="flex flex-col items-center">
              <div className="w-5 sm:w-6 h-8 sm:h-10 border-2 border-white/30 rounded-full flex justify-center">
                <div className="w-0.5 sm:w-1 h-2 sm:h-3 bg-white/60 rounded-full mt-1.5 sm:mt-2 animate-pulse"></div>
              </div>
              <span className="text-accessible-subtle text-xs mt-1 sm:mt-2 hidden sm:block">Claim your discount</span>
            </div>
          </div>
        </div>
      </section>

      {/* Offer Details Section - Mobile Optimized */}
      <section id="claim-form" className="py-12 sm:py-16 md:py-20 lg:py-24 xl:py-32 bg-white relative overflow-hidden">
        {/* Decorative elements - Mobile responsive */}
        <div className="absolute top-0 left-0 w-full h-full pointer-events-none">
          <div className="absolute top-0 right-0 w-64 sm:w-80 md:w-96 h-64 sm:h-80 md:h-96 bg-[#ec4899]/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 left-0 w-48 sm:w-56 md:w-64 h-48 sm:h-56 md:h-64 bg-[#d946ef]/5 rounded-full blur-3xl"></div>
        </div>
        
        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 md:gap-12 lg:gap-16 items-center">
            {/* Left column - Offer details - Mobile optimized */}
            <div>
              <div className="inline-flex items-center mb-3 sm:mb-4">
                <span className="h-px w-4 sm:w-6 md:w-8 bg-[#ec4899]"></span>
                <span className="text-[#ec4899] font-medium ml-2 sm:ml-3 md:ml-4 uppercase tracking-wider text-xs sm:text-sm">Special Offer</span>
              </div>
              <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-[#1a3a6d] mb-3 sm:mb-4 md:mb-6 leading-tight">
                Limited Time <span className="text-gradient-logo">Premium Discount</span>
              </h2>
              
              <div className="bg-gray-50 border border-gray-200 p-4 sm:p-6 md:p-8 rounded-xl mb-6 sm:mb-8 md:mb-10 hover:border-[#ec4899]/50 hover:bg-gray-100 hover:shadow-lg hover:shadow-[#ec4899]/10 transition-all duration-300">
                <div className="text-center mb-4 sm:mb-6">
                  <div className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold text-gradient-logo">25%</div>
                  <div className="text-lg sm:text-xl md:text-2xl text-accessible-gray font-medium">Lifetime Discount</div>
                </div>
                
                <div className="w-full h-px bg-gradient-to-r from-transparent via-[#ec4899]/50 to-transparent my-4 sm:my-6"></div>
                
                <ul className="space-y-2 sm:space-y-3 md:space-y-4">
                  <li className="flex items-start">
                    <div className="flex-shrink-0 w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 bg-[#ec4899]/20 rounded-full flex items-center justify-center mt-0.5 mr-2 sm:mr-3 md:mr-4">
                      <div className="w-1 h-1 sm:w-1.5 sm:h-1.5 md:w-2 md:h-2 bg-[#ec4899] rounded-full"></div>
                    </div>
                    <p className="text-sm sm:text-base md:text-lg text-accessible-gray">Valid on all premium UPVC window installations</p>
                  </li>
                  <li className="flex items-start">
                    <div className="flex-shrink-0 w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 bg-[#ec4899]/20 rounded-full flex items-center justify-center mt-0.5 mr-2 sm:mr-3 md:mr-4">
                      <div className="w-1 h-1 sm:w-1.5 sm:h-1.5 md:w-2 md:h-2 bg-[#ec4899] rounded-full"></div>
                    </div>
                    <p className="text-sm sm:text-base md:text-lg text-accessible-gray">Book your appointment by the end of the month</p>
                  </li>
                  <li className="flex items-start">
                    <div className="flex-shrink-0 w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 bg-[#ec4899]/20 rounded-full flex items-center justify-center mt-0.5 mr-2 sm:mr-3 md:mr-4">
                      <div className="w-1 h-1 sm:w-1.5 sm:h-1.5 md:w-2 md:h-2 bg-[#ec4899] rounded-full"></div>
                    </div>
                    <p className="text-sm sm:text-base md:text-lg text-accessible-gray">Free expert consultation with no obligation</p>
                  </li>
                  <li className="flex items-start">
                    <div className="flex-shrink-0 w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 bg-[#ec4899]/20 rounded-full flex items-center justify-center mt-0.5 mr-2 sm:mr-3 md:mr-4">
                      <div className="w-1 h-1 sm:w-1.5 sm:h-1.5 md:w-2 md:h-2 bg-[#ec4899] rounded-full"></div>
                    </div>
                    <p className="text-sm sm:text-base md:text-lg text-accessible-gray">Includes our standard 10-year guarantee</p>
                  </li>
                </ul>
              </div>
              
              <div className="bg-gray-50 border border-gray-200 p-3 sm:p-4 md:p-6 rounded-xl hover:border-[#ec4899]/50 hover:bg-gray-100 hover:shadow-md hover:shadow-[#ec4899]/10 transition-all duration-300">
                <div className="flex items-start mb-3 sm:mb-4">
                  <div className="flex-shrink-0 w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 bg-[#ec4899]/20 rounded-full flex items-center justify-center mr-2 sm:mr-3 md:mr-4">
                    <svg className="w-3 h-3 sm:w-4 sm:h-4 md:w-5 md:h-5 text-[#ec4899]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  </div>
                  <p className="text-xs sm:text-sm md:text-base text-accessible-gray">
                    This offer is available for a limited time only. Discount applies to the installation of new windows and cannot be combined with other promotions. Terms and conditions apply.
                  </p>
                </div>
              </div>
            </div>
            
            {/* Right column - Booking calendar - Mobile optimized */}
            <div className="bg-gray-50 border border-gray-200 p-4 sm:p-6 md:p-8 rounded-xl hover:border-[#ec4899]/50 hover:bg-gray-100 hover:shadow-lg hover:shadow-[#ec4899]/10 transition-all duration-300">
              <h3 className="text-xl sm:text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-4 sm:mb-6 text-center">
                Schedule Your <span className="text-gradient-logo">Consultation</span>
              </h3>
              
              {/* Replace the quote form with booking calendar */}
              <BookingCalendar />
            </div>
          </div>
        </div>
      </section>
      
      {/* CTA Section - Mobile Optimized */}
      <section className="py-12 sm:py-16 md:py-20 lg:py-24 bg-white relative overflow-hidden">
        {/* Decorative elements - Mobile responsive */}
        <div className="absolute top-0 left-0 w-full h-full pointer-events-none">
          <div className="absolute top-0 right-0 w-64 sm:w-80 md:w-96 h-64 sm:h-80 md:h-96 bg-[#ec4899]/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 left-0 w-48 sm:w-56 md:w-64 h-48 sm:h-56 md:h-64 bg-[#d946ef]/5 rounded-full blur-3xl"></div>
        </div>
        
        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <span className="inline-block text-[#ec4899] font-medium mb-2 sm:mb-3 md:mb-4 text-xs sm:text-sm md:text-base">QUESTIONS?</span>
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-3 sm:mb-4 md:mb-6 text-[#1a3a6d]">Need More Information?</h2>
            <p className="text-sm sm:text-base md:text-lg lg:text-xl text-accessible-gray mb-6 sm:mb-8 md:mb-10 lg:mb-12 max-w-2xl mx-auto">
              If you'd like to learn more about this special offer or our products, our friendly team is ready to help.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-3 sm:gap-4 md:gap-6">
              <Link href="/contact" className="btn-primary text-center text-sm sm:text-base px-4 sm:px-6 md:px-8 py-2.5 sm:py-3 md:py-4">
                Contact Us
              </Link>
              <Link href="/products-services" className="inline-block border-2 border-[#ec4899] text-[#ec4899] hover:bg-[#ec4899] hover:text-white font-medium py-2.5 sm:py-3 md:py-4 px-4 sm:px-6 md:px-8 rounded-full transition-all duration-300 text-center text-sm sm:text-base">
                Browse Our Products
              </Link>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
} 