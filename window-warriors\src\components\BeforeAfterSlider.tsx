'use client';

import { useState, useRef, useEffect } from 'react';
import Image from 'next/image';

interface BeforeAfterSliderProps {
  beforeImage: string;
  afterImage: string;
  beforeAlt?: string;
  afterAlt?: string;
  className?: string;
}

const BeforeAfterSlider = ({
  beforeImage,
  afterImage,
  beforeAlt = 'Before image',
  afterAlt = 'After image',
  className = '',
}: BeforeAfterSliderProps) => {
  const [sliderPosition, setSliderPosition] = useState(50);
  const [isDragging, setIsDragging] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // Handle mouse down on the slider handle
  const handleMouseDown = () => {
    setIsDragging(true);
  };

  // Handle mouse up (stop dragging)
  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Handle mouse movement when dragging
  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging || !containerRef.current) return;

    const containerRect = containerRef.current.getBoundingClientRect();
    const containerWidth = containerRect.width;
    const mouseX = e.clientX - containerRect.left;
    
    // Calculate position as percentage
    let position = (mouseX / containerWidth) * 100;
    
    // Constrain position between 0 and 100
    position = Math.max(0, Math.min(100, position));
    
    setSliderPosition(position);
  };

  // Handle touch events
  const handleTouchMove = (e: TouchEvent) => {
    if (!containerRef.current) return;

    const containerRect = containerRef.current.getBoundingClientRect();
    const containerWidth = containerRect.width;
    const touch = e.touches[0];
    const touchX = touch.clientX - containerRect.left;
    
    // Calculate position as percentage
    let position = (touchX / containerWidth) * 100;
    
    // Constrain position between 0 and 100
    position = Math.max(0, Math.min(100, position));
    
    setSliderPosition(position);
  };

  // Setup event listeners
  useEffect(() => {
    const handleMouseUpGlobal = () => {
      setIsDragging(false);
    };

    if (isDragging) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUpGlobal);
      window.addEventListener('touchmove', handleTouchMove, { passive: true });
      window.addEventListener('touchend', handleMouseUpGlobal);
    }

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUpGlobal);
      window.removeEventListener('touchmove', handleTouchMove);
      window.removeEventListener('touchend', handleMouseUpGlobal);
    };
  }, [isDragging]);

  return (
    <div className="relative w-full max-w-full prevent-layout-shift">
      {/* Instruction text */}
      <div className="text-center text-xs sm:text-sm text-accessible-gray mb-2 sm:mb-3 font-medium animate-pulse-slow">
        <span className="hidden sm:inline">Drag</span><span className="sm:hidden">Swipe</span> the slider to compare before and after
      </div>

      <div 
        ref={containerRef}
        className={`before-after-slider relative w-full max-w-full h-[200px] xs:h-[250px] sm:h-[300px] md:h-[350px] lg:h-[400px] xl:h-[450px] rounded-lg sm:rounded-xl overflow-hidden shadow-xl sm:shadow-2xl ${className}`}
      >
        {/* Before Image (Full width, shown underneath) */}
        <div className="absolute inset-0 w-full h-full image-container">
          <Image
            src={beforeImage}
            alt={beforeAlt}
            fill
            sizes="(max-width: 640px) 100vw, (max-width: 768px) 80vw, (max-width: 1024px) 60vw, 50vw"
            className="object-cover composite-layer"
            priority
          />
          <div className="absolute bottom-2 sm:bottom-3 md:bottom-4 left-2 sm:left-3 md:left-4 bg-black/70 backdrop-blur-sm px-2 py-1 sm:px-3 sm:py-1.5 md:px-4 md:py-2 rounded-full font-medium text-white text-xs sm:text-sm flex items-center">
            <span className="w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full bg-red-500 mr-1.5 sm:mr-2"></span>
            Before
          </div>
        </div>

        {/* After Image (Controlled by slider width, shown on top) */}
        <div 
          className="absolute inset-0 h-full overflow-hidden image-container"
          style={{ 
            width: `${sliderPosition}%`,
            contain: 'layout style paint'
          }}
        >
          <Image
            src={afterImage}
            alt={afterAlt}
            fill
            sizes="(max-width: 640px) 100vw, (max-width: 768px) 80vw, (max-width: 1024px) 60vw, 50vw"
            className="object-cover composite-layer"
            priority
          />
          <div className="absolute bottom-2 sm:bottom-3 md:bottom-4 right-2 sm:right-3 md:right-4 bg-black/70 backdrop-blur-sm px-2 py-1 sm:px-3 sm:py-1.5 md:px-4 md:py-2 rounded-full font-medium text-white text-xs sm:text-sm flex items-center">
            <span className="w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full bg-green-500 mr-1.5 sm:mr-2"></span>
            After
          </div>
        </div>

        {/* Slider Control */}
        <div 
          className="before-after-handle absolute top-0 bottom-0 w-0.5 sm:w-1 bg-white cursor-ew-resize z-10"
          style={{ 
            left: `${sliderPosition}%`, 
            transform: 'translate3d(-50%, 0, 0)',
            contain: 'layout'
          }}
          onMouseDown={handleMouseDown}
          onTouchStart={handleMouseDown}
        >
          <div className="absolute top-1/2 left-1/2 w-6 h-6 xs:w-7 xs:h-7 sm:w-8 sm:h-8 md:w-9 md:h-9 lg:w-10 lg:h-10 bg-white rounded-full border-2 border-gray-200 flex items-center justify-center shadow-lg touch-none composite-layer" 
               style={{ transform: 'translate3d(-50%, -50%, 0)' }}>
            <svg 
              width="12" 
              height="12"
              viewBox="0 0 24 24" 
              fill="none" 
              xmlns="http://www.w3.org/2000/svg"
              className="text-[#1a3a6d] xs:w-3 xs:h-3 sm:w-4 sm:h-4 md:w-5 md:h-5"
            >
              <path 
                d="M8 8L4 12L8 16M16 8L20 12L16 16" 
                stroke="currentColor" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round"
              />
            </svg>
          </div>
        </div>

        {/* Overlay for slider effect */}
        <div 
          className="absolute inset-0 pointer-events-none"
          style={{ 
            background: `linear-gradient(90deg, 
              transparent ${sliderPosition - 0.5}%, 
              rgba(255, 255, 255, 0.1) ${sliderPosition - 0.5}%, 
              rgba(255, 255, 255, 0.1) ${sliderPosition + 0.5}%, 
              transparent ${sliderPosition + 0.5}%)`,
            contain: 'layout'
          }}
        />
      </div>
    </div>
  );
};

export default BeforeAfterSlider; 