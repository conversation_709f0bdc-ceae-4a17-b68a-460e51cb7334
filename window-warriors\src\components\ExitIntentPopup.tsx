'use client';

import { useState, useEffect, useCallback } from 'react';
import Image from 'next/image';
import Link from 'next/link';

const ExitIntentPopup = () => {
  const [showPopup, setShowPopup] = useState(false);
  const [hasTriggered, setHasTriggered] = useState(false);
  const [isReady, setIsReady] = useState(false);
  const [isUserInteracting, setIsUserInteracting] = useState(false);
  
  // Function to show the popup
  const triggerPopup = useCallback(() => {
    if (!hasTriggered) {
      setShowPopup(true);
      setHasTriggered(true);
      
      // Store in session storage so it doesn't show again in the same browsing session
      sessionStorage.setItem('exitIntentShown', 'true');
    }
  }, [hasTriggered]);
  
  // Close the popup
  const closePopup = () => {
    setShowPopup(false);
  };

  useEffect(() => {
    // Only run in browser environment
    if (typeof window === 'undefined') return;
    
    // Check if popup has been shown in this session
    const hasBeenShown = sessionStorage.getItem('exitIntentShown') === 'true';
    if (hasBeenShown) {
      setHasTriggered(true);
      return;
    }
    
    // Set a delay before the popup can be triggered
    // This prevents it from firing immediately when the page loads
    const readyTimer = setTimeout(() => {
      setIsReady(true);
    }, 5000); // 5 seconds delay
    
    // Track when user is interacting with the page
    const startInteraction = () => setIsUserInteracting(true);
    const endInteraction = () => {
      // Use a small delay to ensure click events complete before disabling the interaction flag
      setTimeout(() => setIsUserInteracting(false), 500);
    };
    
    // Add event listeners for user interaction
    document.addEventListener('mousedown', startInteraction);
    document.addEventListener('touchstart', startInteraction);
    document.addEventListener('keydown', startInteraction);
    document.addEventListener('mouseup', endInteraction);
    document.addEventListener('touchend', endInteraction);
    document.addEventListener('keyup', endInteraction);
    
    // Mouse leave event detection - triggers when mouse leaves the document
    const handleMouseLeave = (e: MouseEvent) => {
      // Only trigger when:
      // 1. Exit intent is ready to be shown
      // 2. It hasn't been triggered yet
      // 3. The mouse is actually leaving the viewport (not just moving between elements)
      // 4. User isn't actively interacting (clicking, etc.)
      // 5. Mouse is at the top of the viewport
      if (isReady && !hasTriggered && e.clientY <= 5 && e.relatedTarget === null && !isUserInteracting) {
        triggerPopup();
      }
    };
    
    document.addEventListener('mouseout', handleMouseLeave);
    
    // Fallback: Show popup after user has been on the site for 60 seconds
    // but only if they haven't triggered it yet
    const fallbackTimer = setTimeout(() => {
      if (!hasTriggered) {
        triggerPopup();
      }
    }, 60000); // 60 seconds
    
    // Cleanup
    return () => {
      clearTimeout(readyTimer);
      clearTimeout(fallbackTimer);
      document.removeEventListener('mouseout', handleMouseLeave);
      document.removeEventListener('mousedown', startInteraction);
      document.removeEventListener('touchstart', startInteraction);
      document.removeEventListener('keydown', startInteraction);
      document.removeEventListener('mouseup', endInteraction);
      document.removeEventListener('touchend', endInteraction);
      document.removeEventListener('keyup', endInteraction);
    };
  }, [hasTriggered, isReady, isUserInteracting, triggerPopup]);

  if (!showPopup) return null;

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center p-4 sm:px-6">
      {/* Overlay */}
      <div
        className="absolute inset-0 bg-[#1a3a6d]/90 backdrop-blur-sm transition-opacity"
        onClick={closePopup}
      ></div>

      {/* Popup content */}
      <div className="glass-card-dark p-4 sm:p-6 md:p-8 lg:p-12 rounded-xl sm:rounded-2xl shadow-2xl premium-card-glow w-full max-w-sm sm:max-w-lg md:max-w-2xl relative z-10 animate-fade-in max-h-[90vh] overflow-y-auto">
        {/* Background decorative elements */}
        <div className="hidden sm:block absolute -top-20 -left-20 w-72 h-72 bg-[#ec4899]/10 rounded-full blur-3xl"></div>
        <div className="hidden sm:block absolute -bottom-20 -right-20 w-72 h-72 bg-[#1a3a6d]/30 rounded-full blur-3xl"></div>
        
        {/* Background pattern */}
        <div className="absolute inset-0 bg-pattern opacity-5 rounded-2xl"></div>
        
        {/* Close button */}
        <button
          onClick={closePopup}
          className="absolute top-2 right-2 sm:top-4 sm:right-4 w-8 h-8 sm:w-10 sm:h-10 flex items-center justify-center rounded-full bg-white/10 hover:bg-white/20 transition-all duration-300 z-20"
          aria-label="Close popup"
        >
          <svg className="w-4 h-4 sm:w-5 sm:h-5 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
        
        <div className="relative z-10">
          {/* Header */}
          <div className="text-center mb-6 sm:mb-8">
            <h2 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold text-white mb-3 sm:mb-4">
              <span className="text-gradient-premium">Wait!</span> Before You Go...
            </h2>
            <div className="w-16 sm:w-24 h-1 sm:h-1.5 bg-gradient-to-r from-[#ec4899] to-[#be185d] mx-auto rounded-full"></div>
          </div>
          
          {/* Content */}
          <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 md:gap-8 items-center">
            <div className="relative h-32 sm:h-36 md:h-40 lg:h-48 aspect-square shrink-0 overflow-hidden rounded-lg">
              <Image
                src="/images/optimized/exterior-home.webp"
                alt="Window Warriors special offer"
                fill
                className="object-cover composite-layer"
                sizes="(max-width: 640px) 128px, (max-width: 768px) 144px, (max-width: 1024px) 160px, 192px"
                loading="lazy"
              />
              <div className="absolute -top-1 -right-1 sm:-top-2 sm:-right-2 bg-[#ec4899] text-[#0b1423] w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 rounded-full flex items-center justify-center text-xs sm:text-sm md:text-base font-bold animate-pulse-slow">
                FREE
              </div>
            </div>
            
            <div className="flex-1 text-center sm:text-left">
              <h3 className="text-lg sm:text-xl md:text-2xl lg:text-3xl font-bold text-white mb-2 sm:mb-3">
                Get a Free Home Assessment
              </h3>
              <p className="text-accessible-light mb-4 sm:mb-6 text-sm sm:text-base md:text-lg">
                Our experts can help you save up to 25% on your energy bills with our premium energy-efficient windows.
              </p>
              <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center sm:justify-start">
                <Link
                  href="/contact"
                  className="btn-primary animate-pulse-slow text-center py-2 sm:py-3 px-4 sm:px-6 text-sm sm:text-base md:text-lg font-medium rounded-lg hover:scale-105 transition-all duration-300"
                  onClick={(e) => {
                    e.stopPropagation(); // Prevent the overlay click from firing
                    closePopup();
                  }}
                >
                  Book Free Consultation
                </Link>
                <button
                  className="text-white/70 hover:text-white underline text-base font-medium transition-colors duration-300 hover:scale-105"
                  onClick={closePopup}
                >
                  Maybe Later
                </button>
              </div>
            </div>
          </div>
          
          {/* Testimonial */}
          <div className="mt-8 pt-6 border-t border-white/10">
            <div className="flex items-center">
              <div className="flex space-x-1 mr-3">
                {[1, 2, 3, 4, 5].map((star) => (
                  <svg key={star} className="w-5 h-5 text-[#ec4899]" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                  </svg>
                ))}
              </div>
              <p className="text-base text-accessible-muted italic">
                "Window Warriors transformed our home! Our energy bills are down and the house stays warm all winter."
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExitIntentPopup; 
