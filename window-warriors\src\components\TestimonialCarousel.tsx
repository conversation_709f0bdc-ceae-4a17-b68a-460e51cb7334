'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';

interface Testimonial {
  id: number;
  name: string;
  location: string;
  text: string;
  image: string;
  rating: number;
  position: string;
}

interface TestimonialCarouselProps {
  testimonials: Testimonial[];
}

const TestimonialCarousel = ({ testimonials }: TestimonialCarouselProps) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const intervalTime = 8000; // 8 seconds per testimonial

  useEffect(() => {
    // Auto advance testimonials
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
    }, intervalTime);
    
    // Cleanup interval on unmount
    return () => clearInterval(interval);
  }, [testimonials.length, intervalTime]);

  return (
    <div className="overflow-hidden relative pb-10 prevent-layout-shift" style={{ minHeight: '200px' }}>
      <div 
        className="testimonial-carousel flex transition-transform duration-700 ease-in-out"
        style={{ 
          transform: `translate3d(-${currentIndex * 100}%, 0, 0)`,
          width: `${testimonials.length * 100}%`
        }}
      >
        {testimonials.map((testimonial) => (
          <div 
            key={testimonial.id}
            className="min-w-full flex flex-col sm:flex-row gap-4 sm:gap-8 items-center sm:items-start dynamic-content"
            style={{ width: `${100 / testimonials.length}%` }}
          >
            <div className="flex-shrink-0">
              <div className="relative w-16 h-16 sm:w-20 sm:h-20 rounded-full overflow-hidden border-2 border-[#ec4899]/20 image-container">
                <Image 
                  src={testimonial.image} 
                  alt={testimonial.name}
                  fill
                  className="object-cover composite-layer"
                  sizes="(max-width: 640px) 64px, 80px"
                />
              </div>
            </div>
            
            <div className="flex-grow min-h-0">
              <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-3 mb-2 sm:mb-3">
                <h3 className="text-xl sm:text-2xl font-bold text-[#ec4899] text-center sm:text-left">{testimonial.name}</h3>
                <div className="hidden sm:block w-1.5 h-1.5 rounded-full bg-[#ec4899]"></div>
                <p className="text-sm text-accessible-gray text-center sm:text-left">
                  {testimonial.position} - {testimonial.location}
                </p>
              </div>
              
              <div className="flex justify-center sm:justify-start mb-3 sm:mb-4">
                {[...Array(5)].map((_, i) => (
                  <svg 
                    key={i} 
                    className={`w-4 h-4 sm:w-5 sm:h-5 ${i < testimonial.rating ? 'text-[#ec4899]' : 'text-accessible-gray-dark'}`} 
                    fill="currentColor" 
                    viewBox="0 0 20 20"
                  >
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
              
              <p className="text-accessible-gray-dark text-sm sm:text-base md:text-lg text-center sm:text-left">
                "{testimonial.text}"
              </p>
            </div>
          </div>
        ))}
      </div>
      
      {/* Navigation dots - Improved for touch targets on mobile */}
      <div className="absolute bottom-2 left-0 right-0 flex justify-center space-x-3">
        {testimonials.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentIndex(index)}
            className={`w-2.5 h-2.5 sm:w-3 sm:h-3 rounded-full optimized-transition ${
              currentIndex === index ? 'bg-[#ec4899]' : 'bg-gray-300'
            }`}
            aria-label={`Go to testimonial ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
};

export default TestimonialCarousel; 
