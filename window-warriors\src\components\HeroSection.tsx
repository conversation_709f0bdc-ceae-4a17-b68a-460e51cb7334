import Link from 'next/link';
import Image from 'next/image';

interface HeroSectionProps {
  title: string;
  subtitle: string;
  ctaText: string;
  ctaLink: string;
  imageSrc: string;
  imageAlt: string;
  className?: string;
}

const HeroSection = ({
  title,
  subtitle,
  ctaText,
  ctaLink,
  imageSrc,
  imageAlt,
  className = '',
}: HeroSectionProps) => {
  return (
    <section className={`relative min-h-[600px] md:min-h-[700px] overflow-hidden prevent-layout-shift ${className}`}>
      {/* Background Image */}
      <div className="absolute inset-0 z-0">
        <Image
          src={imageSrc}
          alt={imageAlt}
          fill
          priority
          className="object-cover"
          sizes="100vw"
        />
        <div className="absolute inset-0 bg-gradient-to-r from-[#1a3a6d]/80 to-transparent"></div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-4 h-full flex items-center relative z-10">
        <div className="max-w-lg text-white">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-4 leading-tight">{title}</h1>
          <p className="text-lg md:text-xl mb-8 text-accessible-light">{subtitle}</p>
          
          <div className="flex flex-col sm:flex-row gap-4">
            <Link
              href={ctaLink}
              className="inline-block bg-[#ec4899] hover:bg-[#be185d] text-white font-semibold py-3 px-6 rounded-md transition duration-300 text-center"
            >
              {ctaText}
            </Link>
            <Link
              href="/products-services"
              className="inline-block bg-white hover:bg-gray-100 text-[#1a3a6d] font-semibold py-3 px-6 rounded-md transition duration-300 text-center"
            >
              Explore Our Products
            </Link>
          </div>

          {/* Trust badges */}
          <div className="mt-12">
            <p className="text-sm text-accessible-muted mb-3">Trusted By Homeowners Across The UK</p>
            <div className="flex flex-wrap gap-4 items-center">
              <div className="bg-white/90 rounded-md py-2 px-4 h-16 flex items-center">
                <Image 
                  src="/halo.jpeg" 
                  alt="Halo Certification" 
                  width={80} 
                  height={40}
                  className="object-contain"
                />
              </div>
              <div className="bg-white/90 rounded-md py-2 px-4 h-16 flex items-center">
                <Image 
                  src="/veka.jpeg" 
                  alt="Veka Certification" 
                  width={80} 
                  height={40}
                  className="object-contain"
                />
              </div>
              <div className="bg-white/90 rounded-md py-2 px-4 h-16 flex items-center">
                <Image 
                  src="/Assure Certified Logo 2.jpg" 
                  alt="Assure Certified" 
                  width={80} 
                  height={40}
                  className="object-contain"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection; 
