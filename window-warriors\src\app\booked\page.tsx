import { Metadata } from 'next';
import Link from 'next/link';
import Image from 'next/image';

export const metadata: Metadata = {
  title: 'Appointment Booked | Window Warriors - Thank You',
  description: 'Your appointment has been successfully booked with Window Warriors. Learn about next steps for your premium UPVC windows, doors, or conservatory consultation.',
  keywords: 'appointment booked, window consultation, door installation, upvc windows newcastle, conservatory quote',
  openGraph: {
    title: 'Appointment Booked | Window Warriors - Thank You',
    description: 'Your appointment has been successfully booked with Window Warriors. Learn about next steps for your premium UPVC windows, doors, or conservatory consultation.',
    url: 'https://windowwarriors.uk/booked',
    siteName: 'Window Warriors',
    images: [
      {
        url: 'https://windowwarriors.uk/images/exterior-home.jpg',
        width: 1200,
        height: 630,
        alt: 'Window Warriors - Appointment Confirmed',
      },
    ],
    locale: 'en_GB',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Appointment Booked | Window Warriors - Thank You',
    description: 'Your appointment has been successfully booked with Window Warriors. Learn about next steps for your consultation.',
    images: ['https://windowwarriors.uk/images/exterior-home.jpg'],
  },
  robots: {
    index: false,
    follow: false,
  },
};

export default function BookedPage() {
  return (
    <main className="flex min-h-screen flex-col">
      {/* Hero Section - Mobile Optimized */}
      <section className="relative min-h-[500px] sm:min-h-[600px] lg:min-h-screen pt-20 sm:pt-24 md:pt-28 lg:pt-32 pb-12 sm:pb-16 md:pb-20 lg:pb-24 overflow-hidden">
        {/* Background gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-[#1a3a6d] to-[#ec4899]/30"></div>
        
        {/* Decorative elements */}
        <div className="hidden md:block absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-[#ec4899]/10 rounded-full blur-xl"></div>
          <div className="absolute bottom-1/3 right-1/4 w-24 h-24 bg-[#d946ef]/10 rounded-full blur-xl"></div>
        </div>
        
        {/* Hero Content */}
        <div className="container mx-auto px-4 sm:px-6 relative z-10 flex flex-col justify-center min-h-[400px] sm:min-h-[500px] lg:min-h-[calc(100vh-8rem)]">
          <div className="max-w-4xl mx-auto text-center animate-fade-in" style={{ animationDelay: '0.3s' }}>
            {/* Success badge */}
            <div className="inline-flex items-center mb-4 sm:mb-6 md:mb-8">
              <div className="bg-green-500/20 backdrop-blur-sm border border-green-400/30 rounded-full px-4 sm:px-6 py-1.5 sm:py-2 animate-fade-blur" style={{ animationDelay: '0.2s' }}>
                <span className="text-green-300 text-xs sm:text-sm md:text-base font-medium">APPOINTMENT CONFIRMED</span>
                <div className="w-full h-px bg-gradient-to-r from-transparent via-green-400/70 to-transparent mt-1"></div>
              </div>
            </div>
            
            {/* Success icon */}
            <div className="w-20 h-20 sm:w-24 sm:h-24 md:w-28 md:h-28 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-6 sm:mb-8 animate-pulse-slow">
              <svg className="w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
              </svg>
            </div>
            
            {/* Title */}
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold text-white mb-3 sm:mb-4 md:mb-6 leading-tight">
              Thank You!
            </h1>
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6 sm:mb-8">
              Your Appointment is <span className="relative inline-block">
                <span className="text-gradient-logo">Confirmed</span>
                <div className="absolute -bottom-1 sm:-bottom-2 left-0 w-full h-0.5 sm:h-1 bg-gradient-to-r from-[#ec4899] to-[#d946ef] rounded-full animate-shimmer"></div>
              </span>
            </h2>
            
            {/* Description */}
            <div className="relative mb-8 sm:mb-12">
              <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-accessible-light leading-relaxed animate-slide-up max-w-3xl mx-auto" style={{ animationDelay: '0.6s' }}>
                We've received your appointment booking and you'll receive a confirmation email shortly. Our expert team is excited to help you transform your home with premium UPVC windows, doors, or conservatories.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Next Steps Section */}
      <section className="py-12 sm:py-16 md:py-20 lg:py-24 bg-white relative overflow-hidden">
        {/* Decorative elements */}
        <div className="absolute top-0 left-0 w-full h-full pointer-events-none">
          <div className="absolute top-0 right-0 w-64 sm:w-80 md:w-96 h-64 sm:h-80 md:h-96 bg-[#ec4899]/5 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 left-0 w-48 sm:w-56 md:w-64 h-48 sm:h-56 md:h-64 bg-[#d946ef]/5 rounded-full blur-3xl"></div>
        </div>
        
        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-8 sm:mb-12 md:mb-16">
              <span className="inline-block text-[#ec4899] font-medium mb-2 sm:mb-3 md:mb-4 text-xs sm:text-sm md:text-base">WHAT HAPPENS NEXT</span>
              <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-3 sm:mb-4 md:mb-6 text-[#1a3a6d]">
                Your Journey to <span className="text-gradient-logo">Better Windows</span>
              </h2>
              <p className="text-sm sm:text-base md:text-lg lg:text-xl text-accessible-gray max-w-2xl mx-auto">
                Here's what you can expect from your Window Warriors experience
              </p>
            </div>
            
            {/* Steps Grid */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 sm:gap-8 mb-12 sm:mb-16">
              <div className="text-center p-6 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors duration-300">
                <div className="w-16 h-16 bg-[#ec4899]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-[#ec4899] font-bold text-2xl">1</span>
                </div>
                <h3 className="text-lg sm:text-xl font-semibold text-[#1a3a6d] mb-3">Confirmation Call</h3>
                <p className="text-sm sm:text-base text-accessible-gray">
                  We'll call you within 24 hours to confirm your appointment details and answer any initial questions.
                </p>
              </div>
              
              <div className="text-center p-6 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors duration-300">
                <div className="w-16 h-16 bg-[#ec4899]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-[#ec4899] font-bold text-2xl">2</span>
                </div>
                <h3 className="text-lg sm:text-xl font-semibold text-[#1a3a6d] mb-3">Home Visit</h3>
                <p className="text-sm sm:text-base text-accessible-gray">
                  Our expert will visit your home to assess your needs and provide a detailed, no-obligation quote.
                </p>
              </div>
              
              <div className="text-center p-6 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors duration-300">
                <div className="w-16 h-16 bg-[#ec4899]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-[#ec4899] font-bold text-2xl">3</span>
                </div>
                <h3 className="text-lg sm:text-xl font-semibold text-[#1a3a6d] mb-3">Professional Installation</h3>
                <p className="text-sm sm:text-base text-accessible-gray">
                  If you proceed, our certified installers will transform your home with premium UPVC products.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* What to Expect Section */}
      <section className="py-12 sm:py-16 md:py-20 bg-gray-50 relative overflow-hidden">
        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-8 sm:mb-12">
              <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-4 text-[#1a3a6d]">
                What to Expect During Your <span className="text-gradient-logo">Consultation</span>
              </h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 sm:gap-8">
              <div className="bg-white p-6 rounded-xl shadow-sm">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 bg-[#ec4899]/10 rounded-full flex items-center justify-center mr-3">
                    <svg className="w-5 h-5 text-[#ec4899]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-[#1a3a6d]">Free Assessment</h3>
                </div>
                <p className="text-accessible-gray">
                  Complete evaluation of your current windows, doors, or conservatory needs with professional recommendations.
                </p>
              </div>

              <div className="bg-white p-6 rounded-xl shadow-sm">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 bg-[#ec4899]/10 rounded-full flex items-center justify-center mr-3">
                    <svg className="w-5 h-5 text-[#ec4899]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-[#1a3a6d]">Detailed Quote</h3>
                </div>
                <p className="text-accessible-gray">
                  Transparent pricing with no hidden costs. We'll explain all options and help you choose the best solution.
                </p>
              </div>

              <div className="bg-white p-6 rounded-xl shadow-sm">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 bg-[#ec4899]/10 rounded-full flex items-center justify-center mr-3">
                    <svg className="w-5 h-5 text-[#ec4899]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-[#1a3a6d]">Product Showcase</h3>
                </div>
                <p className="text-accessible-gray">
                  See samples of our premium UPVC windows, doors, and conservatory materials with different styles and finishes.
                </p>
              </div>

              <div className="bg-white p-6 rounded-xl shadow-sm">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 bg-[#ec4899]/10 rounded-full flex items-center justify-center mr-3">
                    <svg className="w-5 h-5 text-[#ec4899]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-[#1a3a6d]">Installation Timeline</h3>
                </div>
                <p className="text-accessible-gray">
                  Clear timeline for your project from order to completion, including preparation and installation schedules.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-12 sm:py-16 md:py-20 bg-white relative overflow-hidden">
        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <span className="inline-block text-[#ec4899] font-medium mb-2 sm:mb-3 md:mb-4 text-xs sm:text-sm md:text-base">QUESTIONS?</span>
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-3 sm:mb-4 md:mb-6 text-[#1a3a6d]">Need to Change Your Appointment?</h2>
            <p className="text-sm sm:text-base md:text-lg lg:text-xl text-accessible-gray mb-6 sm:mb-8 md:mb-10 lg:mb-12 max-w-2xl mx-auto">
              If you need to reschedule or have any questions before your appointment, don't hesitate to get in touch.
            </p>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 sm:gap-8 mb-8 sm:mb-12">
              <div className="bg-gray-50 p-6 rounded-xl text-center">
                <div className="w-12 h-12 bg-[#ec4899]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-[#ec4899]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-[#1a3a6d] mb-2">Call Us</h3>
                <p className="text-accessible-gray mb-3">Speak directly with our team</p>
                <a href="tel:01913592774" className="text-[#ec4899] font-semibold hover:text-[#be185d] transition-colors">
                  0191 359 2774
                </a>
              </div>

              <div className="bg-gray-50 p-6 rounded-xl text-center">
                <div className="w-12 h-12 bg-[#ec4899]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-[#ec4899]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-[#1a3a6d] mb-2">Email Us</h3>
                <p className="text-accessible-gray mb-3">Send us your questions</p>
                <a href="mailto:<EMAIL>" className="text-[#ec4899] font-semibold hover:text-[#be185d] transition-colors">
                  <EMAIL>
                </a>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row justify-center gap-3 sm:gap-4 md:gap-6">
              <Link href="/contact" className="btn-primary text-center text-sm sm:text-base px-4 sm:px-6 md:px-8 py-2.5 sm:py-3 md:py-4">
                Contact Us
              </Link>
              <Link href="/products-services" className="inline-block border-2 border-[#ec4899] text-[#ec4899] hover:bg-[#ec4899] hover:text-white font-medium py-2.5 sm:py-3 md:py-4 px-4 sm:px-6 md:px-8 rounded-full transition-all duration-300 text-center text-sm sm:text-base">
                View Our Products
              </Link>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}
