'use client';

import { useEffect } from 'react';

interface BookingCalendarProps {
  title?: string;
  subtitle?: string;
  className?: string;
}

const BookingCalendar = ({
  title = "Claim Your 25% Discount",
  subtitle = "Book your free consultation to get started",
  className = '',
}: BookingCalendarProps) => {
  
  useEffect(() => {
    // Load the Go High Level form embed script
    const script = document.createElement('script');
    script.src = 'https://link.msgsndr.com/js/form_embed.js';
    script.type = 'text/javascript';
    script.async = true;
    document.body.appendChild(script);
    
    return () => {
      // Clean up
      try {
        document.body.removeChild(script);
      } catch (e) {
        console.log('Error removing script:', e);
      }
    };
  }, []);

  return (
    <div className={`rounded-lg ${className}`}>
      <div className="text-center mb-6">
        <h3 className="text-2xl font-bold text-[#1a3a6d]">{title}</h3>
        <p className="text-accessible-gray mt-2">{subtitle}</p>
      </div>
      
      <div className="bg-gray-100 p-4 rounded-lg mb-6 border border-gray-200">
        <p className="text-sm sm:text-base text-accessible-gray">
          Select your preferred date and time below.
        </p>
      </div>
      
      {/* Calendar container */}
      <div className="rounded-lg overflow-hidden bg-white">
        <div 
          dangerouslySetInnerHTML={{ 
            __html: `
              <iframe 
                src="https://api.leadconnectorhq.com/widget/booking/J9Agt4DcVrPPf6KaTv8M" 
                style="width: 100%; height: 1100px; border: none; overflow: auto;" 
                scrolling="yes" 
                id="J9Agt4DcVrPPf6KaTv8M_1744200882865">
              </iframe>
            `
          }} 
        />
      </div>
    </div>
  );
};

export default BookingCalendar; 