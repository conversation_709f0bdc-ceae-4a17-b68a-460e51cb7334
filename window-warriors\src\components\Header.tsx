'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import Image from 'next/image';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const scrollPosition = useRef(0);

  // Handle scroll events to change header appearance
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      setIsScrolled(scrollTop > 50);
    };
    
    // Initialize scroll state on component mount
    handleScroll();
    
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMenu = () => {
    if (!isMenuOpen) {
      // Save current scroll position before opening menu
      scrollPosition.current = window.pageYOffset;
    }
    setIsMenuOpen(!isMenuOpen);
  };

  // Lock body scroll when mobile menu is open
  useEffect(() => {
    if (isMenuOpen) {
      // Save current scroll position and lock scrolling
      document.body.classList.add('overflow-hidden');
      document.body.style.position = 'fixed';
      document.body.style.top = `-${scrollPosition.current}px`;
      document.body.style.width = '100%';
    } else {
      // Restore scroll position when menu is closed
      document.body.classList.remove('overflow-hidden');
      document.body.style.position = '';
      document.body.style.top = '';
      document.body.style.width = '';
      window.scrollTo(0, scrollPosition.current);
    }

    return () => {
      // Clean up effect
      document.body.classList.remove('overflow-hidden');
      document.body.style.position = '';
      document.body.style.top = '';
      document.body.style.width = '';
    };
  }, [isMenuOpen]);

  return (
    <header 
      className={`fixed top-0 left-0 right-0 z-[550] transition-all duration-300 ${
        isScrolled 
          ? 'bg-white/95 backdrop-blur-lg border-b border-gray-200/50 shadow-lg' 
          : 'bg-transparent'
      }`}
      style={{
        backdropFilter: isScrolled ? 'blur(20px)' : 'none',
        WebkitBackdropFilter: isScrolled ? 'blur(20px)' : 'none',
      }}
    >
      <div className="container mx-auto px-3 sm:px-4 md:px-6">
        <div className="flex justify-between items-center h-16 sm:h-18 md:h-20">
          {/* Logo - Mobile Optimized */}
          <Link href="/" className="flex items-center flex-shrink-0">
            <div className="relative w-32 sm:w-40 md:w-48 lg:w-56 xl:w-64 h-10 sm:h-12 md:h-14 lg:h-16">
              <div className="relative w-full h-full">
                <Image 
                  src="/window-warriors-logo.png" 
                  alt="Window Warriors Logo" 
                  fill
                  sizes="(max-width: 640px) 128px, (max-width: 768px) 160px, (max-width: 1024px) 192px, 256px"
                  className={`object-contain transition-opacity duration-300 composite-layer ${
                    isScrolled ? 'opacity-0' : 'opacity-100'
                  }`}
                  priority
                />
                
                <Image 
                  src="/window-warriors-logo.png" 
                  alt="Window Warriors Logo" 
                  fill
                  sizes="(max-width: 640px) 128px, (max-width: 768px) 160px, (max-width: 1024px) 192px, 256px"
                  className={`object-contain transition-opacity duration-300 composite-layer ${
                    isScrolled ? 'opacity-100' : 'opacity-0'
                  }`}
                  priority
                />
              </div>
            </div>
          </Link>

          {/* Mobile menu button - Enhanced */}
          <button
            className="lg:hidden relative z-[600] focus:outline-none focus:ring-2 focus:ring-[#ec4899]/50 rounded-md p-2 -mr-2"
            onClick={toggleMenu}
            aria-label="Toggle menu"
            aria-expanded={isMenuOpen}
          >
            <div className="w-6 h-6 sm:w-7 sm:h-7 flex items-center justify-center">
              <span 
                className={`absolute h-0.5 transform transition-all duration-300 ease-in-out ${
                  isMenuOpen ? 'bg-white' : (isScrolled ? 'bg-[#1a3a6d]' : 'bg-white')
                } ${
                  isMenuOpen ? 'w-0 opacity-0' : 'w-5 sm:w-6'
                }`}
                style={{ top: 'calc(50% - 6px)' }}
              ></span>
              <span 
                className={`absolute h-0.5 w-5 sm:w-6 transform transition-all duration-300 ease-in-out ${
                  isMenuOpen ? 'bg-white' : (isScrolled ? 'bg-[#1a3a6d]' : 'bg-white')
                } ${
                  isMenuOpen ? 'rotate-45' : ''
                }`}
                style={{ top: '50%' }}
              ></span>
              <span 
                className={`absolute h-0.5 w-5 sm:w-6 transform transition-all duration-300 ease-in-out ${
                  isMenuOpen ? 'bg-white' : (isScrolled ? 'bg-[#1a3a6d]' : 'bg-white')
                } ${
                  isMenuOpen ? '-rotate-45' : ''
                }`}
                style={{ top: '50%' }}
              ></span>
              <span 
                className={`absolute h-0.5 transform transition-all duration-300 ease-in-out ${
                  isMenuOpen ? 'bg-white' : (isScrolled ? 'bg-[#1a3a6d]' : 'bg-white')
                } ${
                  isMenuOpen ? 'w-0 opacity-0' : 'w-5 sm:w-6'
                }`}
                style={{ top: 'calc(50% + 6px)' }}
              ></span>
            </div>
          </button>

          {/* Desktop Navigation - Responsive spacing */}
          <nav className="hidden lg:flex items-center space-x-4 xl:space-x-6 2xl:space-x-8">
            <NavLink href="/" isScrolled={isScrolled}>Home</NavLink>
            <NavLink href="/about" isScrolled={isScrolled}>About Us</NavLink>
            <NavLink href="/products-services" isScrolled={isScrolled}>Products & Services</NavLink>
            <NavLink href="/gallery" isScrolled={isScrolled}>Gallery</NavLink>
            <NavLink href="/blog" isScrolled={isScrolled}>Blog</NavLink>
            <NavLink href="/testimonials" isScrolled={isScrolled}>Testimonials</NavLink>
            <NavLink href="/contact" isScrolled={isScrolled}>Contact</NavLink>
            <Link
              href="/products-services#quote"
              className={`
                relative overflow-hidden group px-4 py-2 lg:px-5 lg:py-2.5 xl:px-6 xl:py-3 rounded-full text-sm lg:text-base
                font-medium transition-all duration-300 transform hover:-translate-y-1 hover:shadow-lg
                ${isScrolled
                  ? 'bg-[#ec4899] hover:bg-[#be185d] text-white shadow-md'
                  : 'bg-white hover:bg-[#ec4899] text-[#1a3a6d] hover:text-white shadow-lg'
                }
              `}
            >
              <span className="relative z-10">Get a Free Quote</span>
            </Link>
          </nav>
        </div>

        {/* Mobile Navigation - Enhanced */}
        <div 
          className={`lg:hidden fixed inset-0 z-[500] bg-black/80 backdrop-blur-sm transition-opacity duration-300 ${
            isMenuOpen 
              ? 'opacity-100 pointer-events-auto' 
              : 'opacity-0 pointer-events-none'
          }`}
          onClick={toggleMenu}
        >
          <div 
            className={`glass-card-dark h-full w-full max-w-[85%] sm:max-w-sm ml-auto py-16 sm:py-20 px-5 sm:px-6 md:px-8 overflow-y-auto transform transition-transform duration-300 ease-in-out ${
              isMenuOpen ? 'translate-x-0' : 'translate-x-full'
            }`}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Mobile Menu Header */}
            <div className="mb-8 sm:mb-10">
              <div className="flex items-center justify-center mb-4">
                <Image 
                  src="/window-warriors-logo.png" 
                  alt="Window Warriors Logo" 
                  width={120} 
                  height={40}
                  className="max-w-[120px] sm:max-w-[140px] h-auto opacity-90"
                />
              </div>
              <div className="w-16 h-0.5 bg-[#ec4899] mx-auto rounded-full"></div>
            </div>

            {/* Navigation Links */}
            <nav className="flex flex-col space-y-4 sm:space-y-5 mb-8 sm:mb-10">
              <MobileNavLink href="/" onClick={toggleMenu}>Home</MobileNavLink>
              <MobileNavLink href="/about" onClick={toggleMenu}>About Us</MobileNavLink>
              <MobileNavLink href="/products-services" onClick={toggleMenu}>Products & Services</MobileNavLink>
              <MobileNavLink href="/gallery" onClick={toggleMenu}>Gallery</MobileNavLink>
              <MobileNavLink href="/blog" onClick={toggleMenu}>Blog</MobileNavLink>
              <MobileNavLink href="/testimonials" onClick={toggleMenu}>Testimonials</MobileNavLink>
              <MobileNavLink href="/contact" onClick={toggleMenu}>Contact</MobileNavLink>
              
              {/* CTA Button */}
              <Link
                href="/products-services#quote"
                className="block text-center bg-gradient-to-r from-[#ec4899] to-[#be185d] hover:from-[#be185d] hover:to-[#ec4899] text-white font-semibold py-3.5 sm:py-4 px-6 rounded-full transition-all duration-300 mt-6 sm:mt-8 shadow-lg hover:shadow-xl transform hover:scale-105"
                onClick={toggleMenu}
              >
                Get a Free Quote
              </Link>
            </nav>
            
            {/* Social media links for mobile - Enhanced */}
            <div className="mb-6 sm:mb-8">
              <h4 className="text-accessible-white text-sm sm:text-base font-medium text-center mb-4">Follow Us</h4>
              <div className="flex space-x-4 sm:space-x-5 justify-center">
                <a 
                  href="https://www.facebook.com/share/16AtRv9XUC/?mibextid=wwXIfr" 
                  target="_blank" 
                  rel="noopener noreferrer" 
                  className="w-10 h-10 sm:w-11 sm:h-11 rounded-full bg-white/10 hover:bg-[#1877F2]/80 flex items-center justify-center transition-all duration-300 hover:scale-110"
                  aria-label="Follow us on Facebook"
                >
                  <svg className="w-5 h-5 sm:w-5.5 sm:h-5.5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path fillRule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clipRule="evenodd" />
                  </svg>
                </a>
                
                <a 
                  href="https://www.tiktok.com/@windowarriors?_t=ZN-8vO56sswgEw&_r=1" 
                  target="_blank" 
                  rel="noopener noreferrer" 
                  className="w-10 h-10 sm:w-11 sm:h-11 rounded-full bg-white/10 hover:bg-[#000000]/80 flex items-center justify-center transition-all duration-300 hover:scale-110"
                  aria-label="Follow us on TikTok"
                >
                  <svg className="w-5 h-5 sm:w-5.5 sm:h-5.5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path d="M16.6 5.82s.51.5 0 0A4.278 4.278 0 0 1 15.54 3h-3.09v12.4a2.592 2.592 0 0 1-2.59 2.5c-1.42 0-2.6-1.16-2.6-2.6 0-1.72 1.66-3.01 3.37-2.48V9.66c-3.45-.46-6.47 2.22-6.47 5.64 0 3.33 2.76 5.7 5.69 5.7 3.14 0 5.69-2.55 5.69-5.7V9.01a7.35 7.35 0 0 0 4.3 1.38V7.3s-1.88.09-3.24-1.48z" />
                  </svg>
                </a>
              </div>
            </div>
            
            {/* Contact information for mobile - Enhanced */}
            <div className="text-center">
              <h4 className="text-accessible-white text-sm sm:text-base font-medium mb-3 sm:mb-4">Contact us:</h4>
              <div className="space-y-2 sm:space-y-3">
                <p>
                  <a
                    href="tel:+441913592774"
                    className="text-accessible-muted hover:text-[#ec4899] transition-colors duration-300 text-sm sm:text-base flex items-center justify-center"
                  >
                    <svg className="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                    0191 359 2774
                  </a>
                </p>
                <p>
                  <a
                    href="mailto:<EMAIL>"
                    className="text-accessible-muted hover:text-[#ec4899] transition-colors duration-300 text-sm sm:text-base flex items-center justify-center"
                  >
                    <svg className="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    <EMAIL>
                  </a>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

type NavLinkProps = {
  href: string;
  children: React.ReactNode;
  isScrolled: boolean;
};

const NavLink = ({ href, children, isScrolled }: NavLinkProps) => {
  return (
    <Link 
      href={href} 
      className={`
        relative font-medium transition-colors duration-300 text-sm lg:text-base xl:text-base
        after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 
        after:bg-[#ec4899] after:transition-all after:duration-300
        hover:after:w-full ${isScrolled ? 'text-[#1a3a6d] hover:text-[#ec4899]' : 'text-white hover:text-[#f9a8d4]'}
      `}
    >
      {children}
    </Link>
  );
};

type MobileNavLinkProps = {
  href: string;
  children: React.ReactNode;
  onClick: () => void;
};

const MobileNavLink = ({ href, children, onClick }: MobileNavLinkProps) => {
  return (
    <Link 
      href={href} 
      className="block text-lg sm:text-xl text-white hover:text-[#ec4899] font-medium transition-all duration-300 py-2 px-4 rounded-lg hover:bg-white/5 border-l-2 border-transparent hover:border-[#ec4899]"
      onClick={onClick}
    >
      {children}
    </Link>
  );
};

export default Header; 