import React from 'react';
import { Metadata } from 'next';
import Link from 'next/link';
import CookieSettings from '@/components/CookieSettings';

export const metadata: Metadata = {
  title: 'Privacy Policy | Window Warriors - UPVC Windows & Doors North East',
  description: 'Our privacy policy explains how we collect, use, and protect your personal information when you use our website or services. Transparent data practices from Window Warriors.',
  keywords: 'privacy policy, GDPR compliance, data protection, Window Warriors privacy, UPVC windows privacy policy',
  openGraph: {
    title: 'Privacy Policy | Window Warriors',
    description: 'Our privacy policy explains how we collect, use, and protect your personal information when you use our website or services.',
    url: 'https://windowwarriors.uk/privacy-policy',
    siteName: 'Window Warriors',
    images: [
      {
        url: 'https://windowwarriors.uk/images/beautiful-view-blue-lake-captured-from-inside-villa.jpg',
        width: 1200,
        height: 630,
        alt: 'Window Warriors Privacy Policy',
      },
    ],
    locale: 'en_GB',
    type: 'website',
  },
  alternates: {
    canonical: 'https://windowwarriors.uk/privacy-policy',
  },
};

export default function PrivacyPolicyPage() {
  return (
    <main className="flex min-h-screen flex-col page-with-hero">
      {/* Enhanced Hero Section */}
      <section className="relative min-h-[60vh] sm:min-h-[70vh] md:min-h-screen py-16 sm:py-24 md:py-28 lg:py-32 xl:py-36 overflow-hidden">
        {/* Simplified background */}
        <div className="absolute inset-0 bg-gradient-to-br from-[#1a3a6d] to-[#ec4899]/30"></div>
          
          {/* Simplified decorative elements */}
          <div className="hidden md:block absolute inset-0">
            <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-[#ec4899]/10 rounded-full blur-xl"></div>
          </div>
        
        {/* Enhanced Hero Content */}
        <div className="container mx-auto px-4 sm:px-6 relative z-10 flex flex-col justify-center min-h-[60vh] sm:min-h-[70vh] md:min-h-screen pb-8 sm:pb-16">
          <div className="max-w-4xl animate-fade-in" style={{ animationDelay: '0.3s' }}>
            {/* Enhanced brand badge */}
            <div className="inline-flex items-center mb-4 sm:mb-6 md:mb-8">
              <div className="bg-white/10 backdrop-blur-sm border border-[#ec4899]/30 rounded-full px-4 sm:px-6 py-2 animate-fade-blur" style={{ animationDelay: '0.2s' }}>
                <span className="text-accessible-light text-xs sm:text-sm md:text-base font-medium">PRIVACY POLICY</span>
                <div className="w-full h-px bg-gradient-to-r from-transparent via-[#ec4899]/70 to-transparent mt-1"></div>
              </div>
            </div>
            
            {/* Enhanced title with pink gradients */}
            <h1 className="text-3xl sm:text-4xl md:text-6xl lg:text-7xl xl:text-8xl font-bold text-white mb-3 sm:mb-4 md:mb-6 leading-tight">
              Your <span className="relative inline-block">
                <span className="text-gradient-logo">Privacy</span>
                <div className="absolute -bottom-1 sm:-bottom-2 left-0 w-full h-0.5 sm:h-1 bg-gradient-to-r from-[#ec4899] to-[#d946ef] rounded-full animate-shimmer"></div>
                <div className="absolute -top-0.5 sm:-top-1 -right-0.5 sm:-right-1 w-2 sm:w-3 h-2 sm:h-3 bg-[#ec4899]/60 rounded-full animate-pulse"></div>
              </span>
            </h1>
            <h2 className="text-2xl sm:text-3xl md:text-5xl lg:text-6xl font-bold text-white mb-3 sm:mb-4 md:mb-6">
              Matters
            </h2>
            
            {/* Enhanced description */}
            <div className="relative mb-4 sm:mb-6 md:mb-8">
              <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-accessible-light leading-relaxed animate-slide-up" style={{ animationDelay: '0.6s' }}>
                How we collect, use, and protect your personal information with complete transparency and care.
              </p>
              <div className="absolute -left-2 sm:-left-4 top-0 w-0.5 sm:w-1 h-full bg-gradient-to-b from-[#ec4899] to-transparent rounded-full opacity-60"></div>
            </div>
            
            {/* Enhanced buttons */}
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 md:gap-6 mb-6 sm:mb-8 md:mb-10 animate-slide-up" style={{ animationDelay: '0.9s' }}>
              <Link href="/contact" className="btn-primary text-center text-sm sm:text-base">
                Contact Us
              </Link>
              <Link href="/" className="inline-block bg-white/10 backdrop-blur-sm border-2 border-white/30 text-white hover:bg-white hover:text-[#1a3a6d] font-medium py-3 sm:py-4 px-6 sm:px-8 rounded-full transition-all duration-300 text-center group text-sm sm:text-base">
                <span className="flex items-center justify-center">
                  <svg className="w-4 h-4 sm:w-5 sm:h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                  </svg>
                  Back to Home
                </span>
              </Link>
            </div>
            
            {/* Enhanced trust badges */}
            <div className="animate-fade-in" style={{ animationDelay: '1.2s' }}>
              <p className="text-white/70 text-xs sm:text-sm md:text-base mb-3 sm:mb-4">Our commitments</p>
              <div className="flex flex-wrap gap-2 sm:gap-3 md:gap-4">
                <div className="flex items-center gap-1.5 sm:gap-2 bg-white/10 backdrop-blur-sm border border-white/20 px-3 sm:px-4 py-1.5 sm:py-2 rounded-full">
                  <div className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 bg-[#ec4899]/20 rounded-full flex items-center justify-center">
                    <svg className="w-2.5 h-2.5 sm:w-3 sm:h-3 md:w-4 md:h-4 text-[#ec4899]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                  </div>
                  <span className="text-white text-xs sm:text-sm font-medium">GDPR Compliant</span>
                </div>
                <div className="flex items-center gap-1.5 sm:gap-2 bg-white/10 backdrop-blur-sm border border-white/20 px-3 sm:px-4 py-1.5 sm:py-2 rounded-full">
                  <div className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 bg-[#ec4899]/20 rounded-full flex items-center justify-center">
                    <svg className="w-2.5 h-2.5 sm:w-3 sm:h-3 md:w-4 md:h-4 text-[#ec4899]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                  </div>
                  <span className="text-white text-xs sm:text-sm font-medium">Secure Data</span>
                </div>
                <div className="flex items-center gap-1.5 sm:gap-2 bg-white/10 backdrop-blur-sm border border-white/20 px-3 sm:px-4 py-1.5 sm:py-2 rounded-full">
                  <div className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 bg-[#ec4899]/20 rounded-full flex items-center justify-center">
                    <svg className="w-2.5 h-2.5 sm:w-3 sm:h-3 md:w-4 md:h-4 text-[#ec4899]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <span className="text-white text-xs sm:text-sm font-medium">Full Transparency</span>
                </div>
              </div>
            </div>
          </div>
          
          {/* Modern scroll indicator */}
          <div className="mt-auto pt-6 sm:pt-8 flex justify-center animate-bounce-slow">
            <div className="flex flex-col items-center">
              <div className="w-5 sm:w-6 h-8 sm:h-10 border-2 border-white/30 rounded-full flex justify-center">
                <div className="w-0.5 sm:w-1 h-2 sm:h-3 bg-white/60 rounded-full mt-1.5 sm:mt-2 animate-pulse"></div>
              </div>
              <span className="text-accessible-subtle text-xs mt-2 hidden sm:block">Learn about privacy</span>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content Section */}
      <section className="py-8 sm:py-12 md:py-16 lg:py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 max-w-4xl">
          <div className="prose prose-sm sm:prose-base lg:prose-lg mx-auto">
            <div className="mb-8 sm:mb-12">
              <p className="text-accessible-gray mb-3 sm:mb-4 text-sm sm:text-base">
                Last Updated: {new Date().toLocaleDateString('en-GB', { day: 'numeric', month: 'long', year: 'numeric' })}
              </p>
              <p className="mb-4 sm:mb-6 text-accessible-gray text-sm sm:text-base leading-relaxed">
                At Window Warriors, we are committed to protecting your privacy and ensuring the security of your personal information. 
                This Privacy Policy explains how we collect, use, and safeguard your data when you visit our website or use our services.
              </p>
              <p className="mb-4 sm:mb-6 text-accessible-gray text-sm sm:text-base leading-relaxed">
                By using our website or services, you agree to the terms outlined in this Privacy Policy. 
                Please take the time to read this policy carefully to understand our practices regarding your personal information.
              </p>
            </div>

            <div id="cookie-settings" className="mb-8 sm:mb-12">
              <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-3 sm:mb-4 relative inline-block">
                Cookie Settings
                <span className="absolute -bottom-1 sm:-bottom-2 left-0 w-12 sm:w-16 h-0.5 sm:h-1 bg-[#ec4899]/50 rounded-full"></span>
              </h2>
              <p className="mb-4 sm:mb-6 text-accessible-gray text-sm sm:text-base leading-relaxed">
                You can customize your cookie preferences below. Essential cookies cannot be disabled as they are necessary for the website to function properly.
              </p>
              
              {/* Cookie Settings Component */}
              <CookieSettings />
            </div>

            <div className="mb-8 sm:mb-12">
              <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-[#1a3a6d] mb-3 sm:mb-4 relative inline-block">
                Information We Collect
                <span className="absolute -bottom-1 sm:-bottom-2 left-0 w-12 sm:w-16 h-0.5 sm:h-1 bg-[#ec4899]/50 rounded-full"></span>
              </h2>
              <p className="mb-3 sm:mb-4 text-accessible-gray text-sm sm:text-base">We may collect the following types of information:</p>
              
              <h3 className="text-lg sm:text-xl font-bold text-[#1a3a6d] mb-2 sm:mb-3 mt-4 sm:mt-6">Personal Information</h3>
              <ul className="list-disc pl-4 sm:pl-6 mb-4 sm:mb-6 text-accessible-gray text-sm sm:text-base space-y-1">
                <li>Name, email address, phone number, and postal address</li>
                <li>Information provided when submitting forms on our website</li>
                <li>Information provided when requesting a quote or consultation</li>
                <li>Communications with us via email, phone, or other channels</li>
              </ul>
              
              <h3 className="text-lg sm:text-xl font-bold text-[#1a3a6d] mb-2 sm:mb-3 mt-4 sm:mt-6">Technical Information</h3>
              <ul className="list-disc pl-4 sm:pl-6 mb-4 sm:mb-6 text-accessible-gray text-sm sm:text-base space-y-1">
                <li>IP address and browser information</li>
                <li>Device information (such as device type, operating system)</li>
                <li>Cookies and similar tracking technologies</li>
                <li>Pages visited and actions taken on our website</li>
              </ul>
            </div>

            <div className="mb-12">
              <h2 className="text-2xl sm:text-3xl font-bold text-[#1a3a6d] mb-4 relative inline-block">
                How We Use Your Information
                <span className="absolute -bottom-2 left-0 w-16 h-1 bg-[#ec4899]/50 rounded-full"></span>
              </h2>
              <p className="mb-4 text-accessible-gray">We use the information we collect for the following purposes:</p>
              <ul className="list-disc pl-6 mb-6 text-accessible-gray">
                <li>To provide and improve our services</li>
                <li>To process and fulfill your requests for quotes, consultations, or installations</li>
                <li>To communicate with you about our products and services</li>
                <li>To send you important updates and notifications</li>
                <li>To personalize your experience on our website</li>
                <li>To analyze and improve our website's performance and effectiveness</li>
                <li>To comply with legal obligations</li>
              </ul>
            </div>

            <div className="mb-12">
              <h2 className="text-2xl sm:text-3xl font-bold text-[#1a3a6d] mb-4 relative inline-block">
                Data Sharing and Disclosure
                <span className="absolute -bottom-2 left-0 w-16 h-1 bg-[#ec4899]/50 rounded-full"></span>
              </h2>
              <p className="mb-4 text-accessible-gray">We may share your information with:</p>
              <ul className="list-disc pl-6 mb-6 text-accessible-gray">
                <li>Service providers who assist us in operating our business</li>
                <li>Professional advisers, including lawyers, bankers, and insurers</li>
                <li>Government authorities or law enforcement officials when required by law</li>
                <li>Third parties in connection with a business transfer or reorganization</li>
              </ul>
              <p className="mb-6 text-accessible-gray">
                We will never sell your personal information to third parties for marketing purposes.
              </p>
            </div>

            <div className="mb-12">
              <h2 className="text-2xl sm:text-3xl font-bold text-[#1a3a6d] mb-4 relative inline-block">
                Cookies and Tracking Technologies
                <span className="absolute -bottom-2 left-0 w-16 h-1 bg-[#ec4899]/50 rounded-full"></span>
              </h2>
              <p className="mb-4 text-accessible-gray">
                We use cookies and similar tracking technologies to collect information about your browsing activities on our website. 
                These technologies help us analyze website traffic, improve user experience, and customize content.
                For detailed information about the cookies we use, please visit our <Link href="/cookies" className="text-[#1a3a6d] hover:text-[#ec4899] transition-colors font-medium">Cookie Policy</Link> page.
              </p>
              
              <h3 className="text-xl font-bold text-[#1a3a6d] mb-3 mt-6 inline-block relative">
                Types of Cookies We Use
                <span className="absolute -bottom-1 left-0 w-8 h-0.5 bg-[#ec4899]/70 rounded-full"></span>
              </h3>
              
              <div className="space-y-6 mt-6">
                <div className="bg-gradient-to-r from-white to-gray-50 p-5 rounded-xl border border-gray-100 shadow-sm transition-all duration-300 hover:shadow-md relative overflow-hidden">
                  <div className="relative z-10">
                    <h4 className="text-lg font-semibold text-[#1a3a6d] mb-2">Essential Cookies</h4>
                    <p className="text-accessible-gray">
                      These cookies are necessary for the website to function properly. They enable basic functions like page navigation 
                      and access to secure areas of the website. The website cannot function properly without these cookies.
                    </p>
                  </div>
                  <div className="absolute top-0 right-0 w-24 h-24 bg-[#1a3a6d]/5 rounded-bl-full pointer-events-none"></div>
                </div>
                
                <div className="bg-gradient-to-r from-white to-gray-50 p-5 rounded-xl border border-gray-100 shadow-sm transition-all duration-300 hover:shadow-md relative overflow-hidden">
                  <div className="relative z-10">
                    <h4 className="text-lg font-semibold text-[#1a3a6d] mb-2">Analytics Cookies</h4>
                    <p className="text-accessible-gray">
                      These cookies help us understand how visitors interact with our website by collecting and reporting information anonymously. 
                      This helps us improve the way our website works, for example, by ensuring that users are finding what they are looking for easily.
                    </p>
                  </div>
                  <div className="absolute top-0 right-0 w-24 h-24 bg-[#1a3a6d]/5 rounded-bl-full pointer-events-none"></div>
                </div>
                
                <div className="bg-gradient-to-r from-white to-gray-50 p-5 rounded-xl border border-gray-100 shadow-sm transition-all duration-300 hover:shadow-md relative overflow-hidden">
                  <div className="relative z-10">
                    <h4 className="text-lg font-semibold text-[#1a3a6d] mb-2">Marketing Cookies</h4>
                    <p className="text-accessible-gray">
                      These cookies are used to track visitors across websites. The intention is to display ads that are relevant and engaging for the 
                      individual user and thereby more valuable for publishers and third-party advertisers.
                    </p>
                  </div>
                  <div className="absolute top-0 right-0 w-24 h-24 bg-[#1a3a6d]/5 rounded-bl-full pointer-events-none"></div>
                </div>
                
                <div className="bg-gradient-to-r from-white to-gray-50 p-5 rounded-xl border border-gray-100 shadow-sm transition-all duration-300 hover:shadow-md relative overflow-hidden">
                  <div className="relative z-10">
                    <h4 className="text-lg font-semibold text-[#1a3a6d] mb-2">Preference Cookies</h4>
                    <p className="text-accessible-gray">
                      These cookies enable the website to remember information that changes the way the website behaves or looks, 
                      like your preferred language or the region that you are in.
                    </p>
                  </div>
                  <div className="absolute top-0 right-0 w-24 h-24 bg-[#1a3a6d]/5 rounded-bl-full pointer-events-none"></div>
                </div>
              </div>
              
              <p className="mt-6 mb-6 text-accessible-gray">
                You can manage cookie preferences through your browser settings or using our <a href="#cookie-settings" className="text-[#1a3a6d] hover:text-[#ec4899] transition-colors">Cookie Settings</a> panel above. 
                However, disabling certain cookies may limit your ability to use some features of our website.
              </p>
            </div>

            <div className="mb-12">
              <h2 className="text-2xl sm:text-3xl font-bold text-[#1a3a6d] mb-4 relative inline-block">
                Data Security
                <span className="absolute -bottom-2 left-0 w-16 h-1 bg-[#ec4899]/50 rounded-full"></span>
              </h2>
              <p className="mb-6 text-accessible-gray">
                We implement appropriate technical and organizational measures to protect your personal information from unauthorized access, 
                disclosure, alteration, or destruction. However, no method of transmission over the internet or electronic storage is 100% secure, 
                so we cannot guarantee absolute security.
              </p>
            </div>

            <div className="mb-12">
              <h2 className="text-2xl sm:text-3xl font-bold text-[#1a3a6d] mb-4 relative inline-block">
                Your Rights and Choices
                <span className="absolute -bottom-2 left-0 w-16 h-1 bg-[#ec4899]/50 rounded-full"></span>
              </h2>
              <p className="mb-4 text-accessible-gray">
                Under applicable data protection laws, you may have the following rights:
              </p>
              <ul className="list-disc pl-6 mb-6 text-accessible-gray">
                <li>Right to access your personal information</li>
                <li>Right to rectify inaccurate or incomplete information</li>
                <li>Right to erasure (or "right to be forgotten")</li>
                <li>Right to restrict processing of your information</li>
                <li>Right to data portability</li>
                <li>Right to object to processing of your information</li>
                <li>Right to withdraw consent at any time</li>
              </ul>
              <p className="mb-6 text-accessible-gray">
                To exercise any of these rights, please contact us using the information provided in the "Contact Us" section below.
              </p>
            </div>

            <div className="mb-12">
              <h2 className="text-2xl sm:text-3xl font-bold text-[#1a3a6d] mb-4 relative inline-block">
                Children's Privacy
                <span className="absolute -bottom-2 left-0 w-16 h-1 bg-[#ec4899]/50 rounded-full"></span>
              </h2>
              <p className="mb-6 text-accessible-gray">
                Our website is not intended for children under the age of 16. We do not knowingly collect personal information from children. 
                If you are a parent or guardian and believe that your child has provided us with personal information, 
                please contact us immediately so we can take appropriate action.
              </p>
            </div>

            <div className="mb-12">
              <h2 className="text-2xl sm:text-3xl font-bold text-[#1a3a6d] mb-4 relative inline-block">
                Changes to This Privacy Policy
                <span className="absolute -bottom-2 left-0 w-16 h-1 bg-[#ec4899]/50 rounded-full"></span>
              </h2>
              <p className="mb-6 text-accessible-gray">
                We may update this Privacy Policy from time to time to reflect changes in our practices or legal requirements. 
                We will notify you of any significant changes by posting the updated policy on our website with a revised "Last Updated" date. 
                We encourage you to review this policy periodically to stay informed about how we protect your information.
              </p>
            </div>

            <div className="mb-12">
              <h2 className="text-2xl sm:text-3xl font-bold text-[#1a3a6d] mb-4 relative inline-block">
                Contact Us
                <span className="absolute -bottom-2 left-0 w-16 h-1 bg-[#ec4899]/50 rounded-full"></span>
              </h2>
              <p className="mb-4 text-accessible-gray">
                If you have any questions, concerns, or requests regarding this Privacy Policy or our data practices, please contact us at:
              </p>
              <div className="relative overflow-hidden bg-gradient-to-br from-gray-50 to-white p-6 rounded-xl border border-gray-200 mb-6 shadow-sm transition-all duration-300 hover:shadow-md">
                <div className="relative z-10">
                  <p className="text-accessible-gray mb-2"><strong>Window Warriors</strong></p>
                  <p className="text-accessible-gray mb-2">Email: <a href="mailto:<EMAIL>" className="text-[#1a3a6d] hover:text-[#ec4899] transition-colors"><EMAIL></a></p>
                  <p className="text-accessible-gray mb-2">Phone: <a href="tel:+441913592774" className="text-[#1a3a6d] hover:text-[#ec4899] transition-colors">0191 359 2774</a></p>
                </div>
                {/* Decorative elements */}
                <div className="absolute top-0 right-0 w-32 h-32 bg-[#1a3a6d]/5 rounded-bl-full pointer-events-none"></div>
                <div className="absolute bottom-0 left-0 w-16 h-16 bg-[#ec4899]/5 rounded-tr-full pointer-events-none"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-12 sm:py-16 bg-gray-50 relative overflow-hidden">
        {/* Decorative background elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/4 left-1/4 w-48 h-48 bg-[#ec4899]/5 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-[#1a3a6d]/5 rounded-full blur-3xl"></div>
        </div>
        
        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-2xl sm:text-3xl font-bold text-[#1a3a6d] mb-6 relative inline-block">
              Have Questions About Our Privacy Practices?
              <span className="absolute -bottom-2 left-0 right-0 mx-auto w-24 h-1 bg-[#ec4899]/70 rounded-full"></span>
            </h2>
            <p className="text-accessible-gray mb-8">
              Our team is here to help. Reach out to us for any questions or concerns about how we handle your personal information.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Link href="/contact" className="btn-primary text-center">
                Contact Us
              </Link>
              <Link href="/" className="inline-block border-2 border-[#ec4899] text-[#ec4899] hover:bg-[#ec4899] hover:text-white font-medium py-3 px-8 rounded-full transition-all duration-300 text-center">
                Back to Home
              </Link>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
} 
