import Image from "next/image";
import Link from "next/link";

export const metadata = {
  title: "How to Choose the Perfect Front Door for Your Home | Window Warriors",
  description: "Your front door makes a statement about your home. Learn how to choose the right style, material, and security features for your Newcastle property.",
  keywords: "front door styles, door installation, home security doors, upvc doors, composite doors, newcastle door company, window warriors doors",
  icons: {
    icon: '/window-warriors-logo.png',
    apple: '/window-warriors-logo.png',
  },
  openGraph: {
    title: "How to Choose the Perfect Front Door for Your Home | Window Warriors",
    description: "Your front door makes a statement about your home. Learn how to choose the right style, material, and security features.",
    url: 'https://windowwarriors.uk/blog/posts/choosing-perfect-front-door',
    siteName: 'Window Warriors',
    images: [
      {
        url: 'https://windowwarriors.uk/images/front-view-front-door-with-blue-wall.jpg',
        width: 1200,
        height: 630,
        alt: 'Choosing the Perfect Front Door - Window Warriors',
      },
    ],
    locale: 'en_GB',
    type: 'article',
  },
  twitter: {
    card: 'summary_large_image',
    title: "How to Choose the Perfect Front Door for Your Home | Window Warriors",
    description: "Your front door makes a statement about your home. Learn how to choose the right style, material, and security features.",
    images: ['https://windowwarriors.uk/images/front-view-front-door-with-blue-wall.jpg'],
  },
  alternates: {
    canonical: 'https://windowwarriors.uk/blog/posts/choosing-perfect-front-door',
  },
};

export default function ChoosingPerfectFrontDoorPage() {
  return (
    <div className="flex min-h-screen flex-col bg-white">
      {/* Hero Section - Following Main Website Pattern - Mobile Optimized */}
      <section className="relative min-h-[60vh] sm:min-h-[70vh] md:min-h-screen overflow-hidden bg-gradient-to-br from-[#1a3a6d] via-[#1a3a6d] to-[#ec4899]/20 pt-24 sm:pt-28 md:pt-32 lg:pt-36">
        {/* Background Image */}
        <div className="absolute inset-0 bg-cover bg-center" style={{
          backgroundImage: 'url("/images/front-view-front-door-with-blue-wall.jpg")',
          backgroundAttachment: 'scroll',
          backgroundPosition: 'center 50%',
        }}></div>
        
        {/* Multi-layered Background Overlays */}
        <div className="absolute inset-0">
          {/* Primary gradient background */}
          <div className="absolute inset-0 bg-gradient-to-br from-[#1a3a6d]/95 via-[#1a3a6d]/90 to-[#ec4899]/70"></div>
          
          {/* Animated gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-[#ec4899]/10 to-transparent animate-gradient-x"></div>
          
          {/* Noise texture */}
          <div className="absolute inset-0 opacity-[0.02] bg-noise"></div>
        </div>

        {/* Floating Pink Elements - Mobile Responsive */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {/* Large floating blurs - Mobile responsive sizing */}
          <div className="absolute top-1/4 left-1/4 w-32 sm:w-48 md:w-64 h-32 sm:h-48 md:h-64 bg-[#ec4899]/20 rounded-full blur-3xl animate-float-slow"></div>
          <div className="absolute top-1/3 right-1/3 w-24 sm:w-36 md:w-48 h-24 sm:h-36 md:h-48 bg-[#d946ef]/15 rounded-full blur-2xl animate-float-reverse"></div>
          <div className="absolute bottom-1/4 left-1/3 w-28 sm:w-42 md:w-56 h-28 sm:h-42 md:h-56 bg-[#f9a8d4]/10 rounded-full blur-3xl animate-float"></div>
          
          {/* Glass particles - Hidden on mobile */}
          <div className="hidden sm:block absolute top-[20%] right-[20%] w-6 sm:w-8 h-6 sm:h-8 bg-white/10 rounded-full backdrop-blur-sm animate-float-slow border border-white/20"></div>
          <div className="hidden sm:block absolute top-[60%] left-[15%] w-4 sm:w-6 h-4 sm:h-6 bg-[#ec4899]/20 rounded-full backdrop-blur-sm animate-float border border-[#ec4899]/30"></div>
          <div className="hidden md:block absolute top-[40%] right-[10%] w-3 sm:w-4 h-3 sm:h-4 bg-[#d946ef]/30 rounded-full animate-pulse"></div>
          <div className="hidden md:block absolute bottom-[40%] left-[20%] w-2 sm:w-3 h-2 sm:h-3 bg-white/30 rounded-full animate-ping"></div>
          
          {/* Geometric accents - Hidden on mobile */}
          <div className="hidden sm:block absolute top-[25%] left-[60%] w-8 sm:w-10 md:w-12 h-8 sm:h-10 md:h-12 border border-[#ec4899]/30 rotate-45 animate-spin-slow"></div>
          <div className="hidden sm:block absolute bottom-[35%] right-[25%] w-6 sm:w-8 h-6 sm:h-8 border-2 border-white/20 rounded-full animate-pulse"></div>
        </div>

        {/* Main Content - Mobile Optimized */}
        <div className="container mx-auto px-4 sm:px-6 relative z-20 min-h-[50vh] sm:min-h-[60vh] flex flex-col justify-center">
          <div className="max-w-4xl">
            {/* Brand Tag - Mobile responsive */}
            <div className="inline-flex items-center mb-4 sm:mb-6 px-3 sm:px-4 py-1.5 sm:py-2 rounded-full border border-[#ec4899]/30 bg-white/5 backdrop-blur-sm animate-fade-in">
              <div className="w-1.5 sm:w-2 h-1.5 sm:h-2 bg-[#ec4899] rounded-full mr-2 sm:mr-3 animate-pulse"></div>
              <span className="text-accessible-light text-xs sm:text-sm font-medium tracking-wider">DOORS • JULY 3, 2023</span>
            </div>

            {/* Main Heading - Mobile responsive */}
            <h1 className="text-2xl sm:text-3xl md:text-5xl lg:text-6xl xl:text-7xl 2xl:text-8xl font-bold mb-4 sm:mb-6 animate-slide-up">
              <span className="text-white block mb-1 sm:mb-2">Choosing the Perfect</span>
              <span className="text-gradient-logo relative inline-block">
                Front Door
                <div className="absolute -bottom-1 sm:-bottom-2 left-0 w-full h-0.5 sm:h-1 bg-gradient-to-r from-[#ec4899] to-[#d946ef] rounded-full animate-shimmer"></div>
              </span>
            </h1>

            {/* Description - Mobile responsive */}
            <p className="text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl text-accessible-muted mb-6 sm:mb-8 max-w-2xl leading-relaxed animate-fade-in" style={{ animationDelay: '0.3s' }}>
              Your front door makes a statement about your home. Learn how to choose the right style, material, and security features.
            </p>

            {/* Author Info - Mobile responsive */}
            <div className="flex items-center space-x-3 sm:space-x-4 text-accessible-light mb-6 sm:mb-8 animate-fade-in" style={{ animationDelay: '0.6s' }}>
              <div className="flex items-center">
                <Image 
                  src="/window-warriors-logo.png" 
                  alt="Window Warriors Author" 
                  width={32} 
                  height={32}
                  className="sm:w-10 sm:h-10 rounded-full border-2 border-white/30 shadow-sm"
                />
                <span className="ml-2 sm:ml-2.5 font-medium text-sm sm:text-base">Window Warriors Team</span>
              </div>
              <span className="text-accessible-subtle text-sm sm:text-base">•</span>
              <span className="flex items-center text-sm sm:text-base">
                <svg className="w-3 sm:w-4 h-3 sm:h-4 mr-1 sm:mr-1.5 text-[#ec4899]" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd"></path>
                </svg>
                7 min read
              </span>
            </div>
          </div>
        </div>

        {/* Scroll Indicator - Mobile responsive */}
        <div className="absolute bottom-3 sm:bottom-4 left-1/2 transform -translate-x-1/2 z-30 animate-bounce">
          <div className="w-5 sm:w-6 h-8 sm:h-10 border-2 border-white/30 rounded-full flex justify-center">
            <div className="w-0.5 sm:w-1 h-2 sm:h-3 bg-[#ec4899] rounded-full mt-1.5 sm:mt-2 animate-pulse"></div>
          </div>
          <p className="text-accessible-subtle text-xs mt-1 sm:mt-2 text-center hidden sm:block">Scroll</p>
        </div>
      </section>

      {/* Article Content - Mobile Optimized */}
      <section className="py-8 sm:py-10 md:py-16">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-3xl mx-auto">
            {/* Table of Contents - Mobile Optimized */}
            <div className="bg-gray-50 rounded-lg p-4 sm:p-5 mb-8 sm:mb-10 border border-gray-100">
              <h2 className="text-base sm:text-lg font-semibold text-[#1a3a6d] mb-3">In this article</h2>
              <ul className="space-y-2">
                <li>
                  <a href="#introduction" className="text-[#1a3a6d] hover:text-[#ec4899] transition-colors flex items-center text-sm sm:text-base">
                    <span className="mr-2">•</span>
                    Introduction
                  </a>
                </li>
                <li>
                  <a href="#door-style" className="text-[#1a3a6d] hover:text-[#ec4899] transition-colors flex items-center text-sm sm:text-base">
                    <span className="mr-2">•</span>
                    Choosing the Right Door Style
                  </a>
                </li>
                <li>
                  <a href="#conclusion" className="text-[#1a3a6d] hover:text-[#ec4899] transition-colors flex items-center text-sm sm:text-base">
                    <span className="mr-2">•</span>
                    Transform Your Home's Entrance
                  </a>
                </li>
              </ul>
            </div>
            
            {/* Introduction - Mobile Optimized */}
            <div id="introduction" className="mb-10 sm:mb-12">
              <h2 className="text-xl sm:text-2xl font-bold text-[#1a3a6d] mb-4 sm:mb-5 pb-2 border-b border-gray-200">Introduction</h2>
              <div className="prose prose-lg max-w-none">
                <p className="text-lg sm:text-xl text-[#1a3a6d] font-medium mb-3 sm:mb-4">
                  Your front door is more than just an entrance to your home—it's a statement piece that sets the tone for your entire property, affects your security, and impacts your energy efficiency. Selecting the right door involves careful consideration of style, materials, security features, and energy performance.
                </p>
                
                <p className="text-gray-700 mb-3 sm:mb-4 text-sm sm:text-base">
                  At Window Warriors, we've helped countless Newcastle homeowners transform their properties with stunning front doors that perfectly balance aesthetics and functionality. In this comprehensive guide, we'll walk you through everything you need to know to select the ideal front door for your North East home.
                </p>
              </div>
              
              <figure className="my-6 sm:my-8 rounded-lg overflow-hidden shadow-md">
                <Image
                  src="/images/front-view-front-door-with-blue-wall.jpg"
                  alt="A beautiful blue front door on a modern home"
                  width={800}
                  height={450}
                  className="w-full object-cover"
                />
                <figcaption className="bg-gray-50 text-gray-600 text-xs sm:text-sm p-2 sm:p-3 italic">
                  A front door creates the first impression of your home and reflects your personal style
                </figcaption>
              </figure>
            </div>

            {/* Section 1: Style and Aesthetics - Mobile Optimized */}
            <div id="door-style" className="mb-10 sm:mb-12">
              <div className="flex items-center mb-4 sm:mb-5">
                <div className="flex items-center justify-center w-8 sm:w-10 h-8 sm:h-10 rounded-full bg-[#1a3a6d]/10 text-[#1a3a6d] font-bold mr-2 sm:mr-3">
                  <svg className="w-4 sm:w-5 h-4 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                </div>
                <h2 className="text-xl sm:text-2xl font-bold text-[#1a3a6d]">Choosing the Right Door Style for Your Home</h2>
              </div>
              
              <div className="prose prose-lg max-w-none">
                <p className="mb-4">
                  Your front door should complement your property's architectural style while reflecting your personal taste. The right door style enhances your home's curb appeal and can even increase its market value.
                </p>
                
                <div className="bg-blue-50 p-5 my-6 rounded-lg border border-blue-100">
                  <h3 className="text-lg font-bold text-[#1a3a6d] mb-3">Traditional Properties</h3>
                  <p className="text-gray-700 mb-3">
                    For period homes in areas like Jesmond or Gosforth, consider:
                  </p>
                  <ul className="space-y-2 ml-6 list-disc text-gray-700">
                    <li>
                      <span className="text-[#1a3a6d] font-medium">Georgian or Victorian panel styles</span> with authentic detailing
                    </li>
                    <li>
                      <span className="text-[#1a3a6d] font-medium">Traditional timber-look finishes</span> (even in modern materials)
                    </li>
                    <li>
                      <span className="text-[#1a3a6d] font-medium">Glass panels</span> with leaded or stained glass designs
                    </li>
                    <li>
                      <span className="text-[#1a3a6d] font-medium">Classic hardware</span> in brass or black iron finishes
                    </li>
                  </ul>
                </div>
                
                <div className="bg-gray-50 p-5 my-6 rounded-lg border border-gray-100">
                  <h3 className="text-lg font-bold text-[#1a3a6d] mb-3">Contemporary Homes</h3>
                  <p className="text-gray-700 mb-3">
                    For newer properties or modernist designs, explore:
                  </p>
                  <ul className="space-y-2 ml-6 list-disc text-gray-700">
                    <li>
                      <span className="text-[#1a3a6d] font-medium">Minimalist flush designs</span> with clean lines
                    </li>
                    <li>
                      <span className="text-[#1a3a6d] font-medium">Large glass panels</span> for maximum light
                    </li>
                    <li>
                      <span className="text-[#1a3a6d] font-medium">Bold colors</span> or sleek monochromatic schemes
                    </li>
                    <li>
                      <span className="text-[#1a3a6d] font-medium">Modern hardware</span> in chrome or brushed steel
                    </li>
                  </ul>
                </div>
                
                <p>
                  Remember that your door's color is a crucial stylistic choice. While white remains popular for its timeless appeal, we're seeing increased interest in statement colors like heritage greens, classic navy, and even vibrant reds among Newcastle homeowners looking to make their properties stand out.
                </p>
              </div>
              
              <figure className="my-8 rounded-lg overflow-hidden shadow-md">
                <Image
                  src="/images/optimized/front-view-front-door-with-blue-wall.webp"
                  alt="Stylish front door options for different home styles"
                  width={800}
                  height={450}
                  className="w-full object-cover"
                  loading="lazy"
                />
                <figcaption className="bg-gray-50 text-gray-600 text-sm p-3 italic">
                  Door styles should complement your home's architecture while reflecting your personal taste
                </figcaption>
              </figure>
              
              <div className="bg-amber-50 border-l-4 border-[#ec4899] p-4 my-6 rounded-r-lg">
                <p className="text-[#1a3a6d] font-medium">Style Tip</p>
                <p className="text-gray-700 text-sm">Consider how your front door coordinates with other external features like windows, roof color, and landscaping to create a cohesive look.</p>
              </div>
            </div>

            {/* Call to Action */}
            <div id="conclusion" className="bg-gray-50 border border-gray-100 rounded-lg p-6 sm:p-8 mb-12">
              <h2 className="text-2xl font-bold text-[#1a3a6d] mb-4">
                Transform Your Home's Entrance with Window Warriors
              </h2>
              <p className="text-gray-700 mb-4">
                Your front door creates that crucial first impression of your home. Whether you're looking for traditional elegance, contemporary minimalism, or uncompromising security, Window Warriors can help you find the perfect door for your Newcastle property.
              </p>
              <p className="text-gray-700 mb-4">
                Our extensive range includes premium composite, UPVC, and aluminum doors, all available with bespoke styling options to perfectly complement your home's character. Every door we install is fitted by our expert team to ensure optimal operation and security.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 mt-6">
                <Link 
                  href="/contact"
                  className="bg-[#1a3a6d] hover:bg-[#0f2d5c] text-white font-medium py-3 px-6 rounded-md text-center shadow-sm transition-colors"
                >
                  Book Your Free Door Consultation
                </Link>
                <Link
                  href="/gallery"
                  className="bg-white hover:bg-gray-100 text-[#1a3a6d] border border-[#1a3a6d] font-medium py-3 px-6 rounded-md text-center shadow-sm transition-colors"
                >
                  View Our Door Gallery
                </Link>
              </div>
            </div>
            
            {/* Author Section */}
            <div className="flex items-start space-x-4 border-t border-gray-200 pt-8 mb-10">
              <Image 
                src="/window-warriors-logo.png" 
                alt="Window Warriors Author" 
                width={60} 
                height={60}
                className="rounded-full border-2 border-gray-200"
              />
              <div>
                <h3 className="font-medium text-[#1a3a6d]">Window Warriors Team</h3>
                <p className="text-gray-600 text-sm mt-1">Our team of door and window specialists has over 15 years of experience serving Newcastle and the North East. We're committed to helping homeowners find the perfect front door that balances style, security, and energy efficiency.</p>
              </div>
            </div>
            
            {/* Related Articles */}
            <div className="mb-12">
              <h3 className="text-xl font-bold text-[#1a3a6d] mb-6">You might also like</h3>
              <div className="grid sm:grid-cols-2 gap-6">
                <Link href="/blog/posts/benefits-of-upvc-windows" className="bg-white rounded-lg overflow-hidden hover:shadow-md transition-all duration-300 border border-gray-100 flex flex-col">
                  <div className="relative h-48">
                    <Image 
                      src="/images/beautiful-view-blue-lake-captured-from-inside-villa.jpg" 
                      alt="Benefits of UPVC Windows" 
                      fill 
                      className="object-cover"
                    />
                  </div>
                  <div className="p-4">
                    <span className="text-xs font-medium text-[#ec4899] uppercase">Windows</span>
                    <h4 className="text-[#1a3a6d] font-bold mt-1">5 Benefits of UPVC Windows You Need to Know</h4>
                  </div>
                </Link>
                
                <Link href="/blog/posts/energy-efficiency-windows" className="bg-white rounded-lg overflow-hidden hover:shadow-md transition-all duration-300 border border-gray-100 flex flex-col">
                  <div className="relative h-48">
                    <Image 
                      src="/images/exterior-home.jpg" 
                      alt="Energy Efficiency Windows" 
                      fill 
                      className="object-cover"
                    />
                  </div>
                  <div className="p-4">
                    <span className="text-xs font-medium text-[#ec4899] uppercase">Energy</span>
                    <h4 className="text-[#1a3a6d] font-bold mt-1">Energy Efficiency: How New Windows Can Reduce Your Bills</h4>
                  </div>
                </Link>
              </div>
            </div>
            
            {/* Tags */}
            <div className="border-t border-gray-200 pt-6 mb-12">
              <div className="flex flex-wrap gap-2">
                <span className="text-gray-500 text-sm">Related Topics:</span>
                {["Front Doors", "Home Renovation", "Security", "Energy Efficiency"].map((tag, index) => (
                  <Link 
                    key={index} 
                    href={`/blog?tag=${tag.toLowerCase().replace(/\s+/g, '-')}`}
                    className="bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm px-3 py-1 rounded-full transition-colors"
                  >
                    {tag}
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
} 

