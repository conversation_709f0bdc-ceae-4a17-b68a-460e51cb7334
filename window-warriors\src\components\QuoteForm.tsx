'use client';

import React, { useState } from 'react';
import <PERSON> from 'next/link';

interface QuoteFormProps {
  title?: string;
  subtitle?: string;
  className?: string;
}

const QuoteForm = ({
  title = "Get Your Free Quote Today",
  subtitle = "Fill in the form below and our team will get back to you within 24 hours",
  className = '',
}: QuoteFormProps) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    postcode: '',
    service: '',
    message: '',
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState('');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');

    try {
      // Create contact payload for GHL API
      const ghlPayload = {
        email: formData.email,
        phone: formData.phone,
        firstName: formData.name.split(' ')[0],
        lastName: formData.name.split(' ').slice(1).join(' ') || '',
        address1: {
          postalCode: formData.postcode
        },
        customField: {
          service: formData.service,
          message: formData.message
        },
        tags: ["get-free-quote"] // Add the tag as specified
      };

      // Submit to Go High Level API
      const response = await fetch('https://rest.gohighlevel.com/v1/contacts/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2NhdGlvbl9pZCI6Im80U0hMV09zU0tJT0x3S0NKM251IiwiY29tcGFueV9pZCI6ImZhRFFmdDAyY3pYZXZYS0swQ2VmIiwidmVyc2lvbiI6MSwiaWF0IjoxNzExNTUwNDI0MzY2LCJzdWIiOiI1Njc3ZnlGRm5JUUJldm5JcFh2SCJ9.zPUwpmykAz2pSr_QL1KnYFhqDoqmOTGdgLxKBCmB-SI'
        },
        body: JSON.stringify(ghlPayload)
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }

      setIsSuccess(true);
      setFormData({
        name: '',
        email: '',
        phone: '',
        postcode: '',
        service: '',
        message: '',
      });
    } catch (err) {
      console.error('Form submission error:', err);
      setError('There was an error submitting your request. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 md:p-8 ${className}`}>
      <div className="text-center mb-6">
        <h3 className="text-2xl font-bold text-[#1a3a6d]">{title}</h3>
        <p className="text-gray-600 mt-2">{subtitle}</p>
      </div>

      {isSuccess ? (
        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md">
          <p className="font-medium">Thank you for your enquiry!</p>
          <p>We've received your request and will contact you shortly.</p>
        </div>
      ) : (
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label htmlFor="name" className="block text-gray-700 mb-1 font-medium">
                Full Name*
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#ec4899]"
              />
            </div>
            
            <div>
              <label htmlFor="email" className="block text-gray-700 mb-1 font-medium">
                Email Address*
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#ec4899]"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label htmlFor="phone" className="block text-gray-700 mb-1 font-medium">
                Phone Number*
              </label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#ec4899]"
              />
            </div>
            
            <div>
              <label htmlFor="postcode" className="block text-gray-700 mb-1 font-medium">
                Postcode*
              </label>
              <input
                type="text"
                id="postcode"
                name="postcode"
                value={formData.postcode}
                onChange={handleChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#ec4899]"
              />
            </div>
          </div>

          <div className="mb-4">
            <label htmlFor="service" className="block text-gray-700 mb-1 font-medium">
              Service Required*
            </label>
            <select
              id="service"
              name="service"
              value={formData.service}
              onChange={handleChange}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#ec4899]"
            >
              <option value="">Select a service</option>
              <option value="windows">UPVC Windows</option>
              <option value="doors">UPVC Doors</option>
              <option value="conservatories">Conservatories</option>
              <option value="repairs">Repairs & Replacements</option>
              <option value="other">Other</option>
            </select>
          </div>

          <div className="mb-6">
            <label htmlFor="message" className="block text-gray-700 mb-1 font-medium">
              Additional Information
            </label>
            <textarea
              id="message"
              name="message"
              value={formData.message}
              onChange={handleChange}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#ec4899]"
            ></textarea>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-4">
              {error}
            </div>
          )}

          <button
            type="submit"
            disabled={isSubmitting}
            className="w-full bg-[#ec4899] hover:bg-[#be185d] text-white font-semibold py-3 px-6 rounded-md transition duration-300 disabled:opacity-70 disabled:cursor-not-allowed"
          >
            {isSubmitting ? 'Submitting...' : 'Get My Free Quote'}
          </button>
          
          <p className="text-xs text-accessible-gray mt-4 text-center">
            By submitting this form, you agree to our <Link href="/privacy-policy" className="underline text-[#1a3a6d] hover:text-[#ec4899] transition-colors">Privacy Policy</Link> and consent to being contacted about our services.
          </p>
        </form>
      )}
    </div>
  );
};

export default QuoteForm; 