'use client';

import { useState, useEffect } from 'react';
import { getCookie, setCookie } from '@/utils/cookieUtils';

const CookieSettings = () => {
  const [settings, setSettings] = useState({
    essential: true, // Essential cookies are always enabled
    analytics: false,
    marketing: false,
    preference: false
  });
  
  const [saveMessage, setSaveMessage] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    // Load cookie preferences when component mounts
    const cookieConsent = getCookie('cookie-consent');
    
    if (cookieConsent) {
      setSettings({
        essential: true, // Always true
        analytics: getCookie('analytics-cookies') === 'true',
        marketing: getCookie('marketing-cookies') === 'true',
        preference: getCookie('preference-cookies') === 'true'
      });
    }
  }, []);

  const handleSave = () => {
    setIsSaving(true);
    
    // Determine the consent level
    const consentLevel = (settings.analytics && settings.marketing && settings.preference) 
      ? 'all' 
      : 'essential';
    
    // Save cookie preferences
    setCookie('cookie-consent', consentLevel, 180); // 6 months
    setCookie('analytics-cookies', settings.analytics ? 'true' : 'false', 180);
    setCookie('marketing-cookies', settings.marketing ? 'true' : 'false', 180);
    setCookie('preference-cookies', settings.preference ? 'true' : 'false', 180);
    
    // Activate or deactivate Google Analytics immediately if it exists
    if (typeof window !== 'undefined' && window.gtag) {
      (window.gtag as any)('consent', 'update', {
        'analytics_storage': settings.analytics ? 'granted' : 'denied',
        'ad_storage': settings.marketing ? 'granted' : 'denied'
      });
    }
    
    // Dispatch custom event to notify that consent has been updated
    const consentEvent = new Event('consentUpdated');
    window.dispatchEvent(consentEvent);
    
    // Show saving animation
    setTimeout(() => {
      setIsSaving(false);
      // Show confirmation message
      setSaveMessage('Your preferences have been saved!');
      
      // Hide the message after 3 seconds
      setTimeout(() => {
        setSaveMessage('');
      }, 3000);
    }, 600);
  };

  const handleChange = (type: 'analytics' | 'marketing' | 'preference') => {
    setSettings(prev => ({
      ...prev,
      [type]: !prev[type]
    }));
  };

  return (
    <div className="relative bg-gray-50 p-8 rounded-xl border border-gray-200 mb-8 overflow-hidden">
      {/* Decorative elements */}
      <div className="absolute top-1/4 right-1/4 w-64 h-64 bg-[#ec4899]/5 rounded-full blur-3xl pointer-events-none"></div>
      <div className="absolute bottom-1/3 left-1/6 w-48 h-48 bg-[#1a3a6d]/10 rounded-full blur-3xl pointer-events-none"></div>
      
      <div className="relative">
        <h3 className="text-xl sm:text-2xl font-bold text-[#1a3a6d] mb-4 relative inline-block">
          Cookie Settings
          <span className="absolute -bottom-1 left-0 w-12 h-1 bg-[#ec4899]/70 rounded-full"></span>
        </h3>
        <p className="mb-8 text-accessible-gray max-w-3xl">
          Customize your cookie preferences below. Essential cookies are necessary for basic website functionality and cannot be disabled.
        </p>
        
        <div className="space-y-5 mb-8 relative">
          {/* Essential Cookies - always enabled */}
          <div className="flex items-center justify-between p-5 bg-white rounded-xl border border-gray-200 transition-shadow duration-300 hover:shadow-md">
            <div className="pr-4">
              <h4 className="font-bold text-[#1a3a6d] text-lg mb-1">Essential Cookies</h4>
              <p className="text-accessible-gray text-sm sm:text-base">
                Necessary for the website to function properly. Cannot be disabled.
              </p>
            </div>
            <div className="flex-shrink-0">
              <div className="relative w-14 h-7">
                <div className="absolute inset-0 bg-[#1a3a6d] rounded-full opacity-20"></div>
                <div className="absolute inset-0 rounded-full bg-[#1a3a6d] shadow-inner"></div>
                <div className="absolute top-1 left-1 w-5 h-5 bg-white rounded-full shadow-md transform translate-x-6"></div>
              </div>
            </div>
          </div>
          
          {/* Analytics Cookies */}
          <div className="flex items-center justify-between p-5 bg-white rounded-xl border border-gray-200 transition-shadow duration-300 hover:shadow-md">
            <div className="pr-4">
              <h4 className="font-bold text-[#1a3a6d] text-lg mb-1">Analytics Cookies</h4>
              <p className="text-accessible-gray text-sm sm:text-base">
                Help us understand how you interact with our website and improve your experience.
              </p>
            </div>
            <div className="flex-shrink-0">
              <button 
                onClick={() => handleChange('analytics')}
                className="relative focus:outline-none group"
                aria-pressed={settings.analytics}
                role="switch"
              >
                <div className={`block w-14 h-7 rounded-full transition-colors duration-300 ease-in-out ${
                  settings.analytics ? 'bg-[#1a3a6d]' : 'bg-gray-200'
                }`}></div>
                <div className={`absolute top-1 left-1 bg-white w-5 h-5 rounded-full transition-transform duration-300 ease-in-out shadow-md ${
                  settings.analytics ? 'transform translate-x-7' : ''
                }`}></div>
              </button>
            </div>
          </div>
          
          {/* Marketing Cookies */}
          <div className="flex items-center justify-between p-5 bg-white rounded-xl border border-gray-200 transition-shadow duration-300 hover:shadow-md">
            <div className="pr-4">
              <h4 className="font-bold text-[#1a3a6d] text-lg mb-1">Marketing Cookies</h4>
              <p className="text-accessible-gray text-sm sm:text-base">
                Used to track visitors across websites to display relevant advertisements.
              </p>
            </div>
            <div className="flex-shrink-0">
              <button 
                onClick={() => handleChange('marketing')}
                className="relative focus:outline-none group"
                aria-pressed={settings.marketing}
                role="switch"
              >
                <div className={`block w-14 h-7 rounded-full transition-colors duration-300 ease-in-out ${
                  settings.marketing ? 'bg-[#1a3a6d]' : 'bg-gray-200'
                }`}></div>
                <div className={`absolute top-1 left-1 bg-white w-5 h-5 rounded-full transition-transform duration-300 ease-in-out shadow-md ${
                  settings.marketing ? 'transform translate-x-7' : ''
                }`}></div>
              </button>
            </div>
          </div>
          
          {/* Preference Cookies */}
          <div className="flex items-center justify-between p-5 bg-white rounded-xl border border-gray-200 transition-shadow duration-300 hover:shadow-md">
            <div className="pr-4">
              <h4 className="font-bold text-[#1a3a6d] text-lg mb-1">Preference Cookies</h4>
              <p className="text-accessible-gray text-sm sm:text-base">
                Enable personalized content and remember your preferences.
              </p>
            </div>
            <div className="flex-shrink-0">
              <button 
                onClick={() => handleChange('preference')}
                className="relative focus:outline-none group"
                aria-pressed={settings.preference}
                role="switch"
              >
                <div className={`block w-14 h-7 rounded-full transition-colors duration-300 ease-in-out ${
                  settings.preference ? 'bg-[#1a3a6d]' : 'bg-gray-200'
                }`}></div>
                <div className={`absolute top-1 left-1 bg-white w-5 h-5 rounded-full transition-transform duration-300 ease-in-out shadow-md ${
                  settings.preference ? 'transform translate-x-7' : ''
                }`}></div>
              </button>
            </div>
          </div>
        </div>
        
        <div className="flex items-center justify-between">
          <button 
            onClick={handleSave}
            disabled={isSaving}
            className={`relative overflow-hidden group px-6 py-3 rounded-full text-white font-medium transition-all duration-300
              before:absolute before:inset-0 before:bg-gradient-accent before:translate-x-full 
              before:transition-transform before:duration-300 hover:before:translate-x-0
              ${isSaving ? 'bg-gray-400 cursor-not-allowed' : 'bg-[#1a3a6d]'}`}
          >
            <span className="relative z-10 flex items-center">
              {isSaving ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Saving...
                </>
              ) : 'Save Preferences'}
            </span>
          </button>
          
          {saveMessage && (
            <div className="text-green-600 font-medium flex items-center animate-fade-in">
              <svg className="w-5 h-5 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
              </svg>
              {saveMessage}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CookieSettings; 