import { isCookieTypeAccepted } from './cookieUtils';

// Google Analytics Measurement ID
const GA_MEASUREMENT_ID = 'G-7F6E8B4RVH';

// Extend Window interface to include Google Analytics properties
declare global {
  interface Window {
    dataLayer: any[];
    gtag: (...args: any[]) => void;
  }
}

/**
 * Initialize analytics tracking only if the user has consented to analytics cookies
 */
export function initializeAnalytics(): void {
  if (typeof window === 'undefined') return; // Don't run on server-side
  
  if (isCookieTypeAccepted('analytics')) {
    console.log('Analytics initialized - user has given consent');
    
    // Load the Google Analytics script
    const script = document.createElement('script');
    script.src = `https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`;
    script.async = true;
    document.head.appendChild(script);

    // Initialize Google Analytics
    window.dataLayer = window.dataLayer || [];
    function gtag(...args: any[]) {
      // Use spread operator to handle the arguments object correctly
      window.dataLayer.push(args.length === 1 ? args[0] : args);
    }
    gtag('js', new Date());
    gtag('config', GA_MEASUREMENT_ID);
    
    // Make gtag accessible globally
    window.gtag = gtag;
  } else {
    console.log('Analytics not initialized - user has not given consent');
  }
}

/**
 * Track page view only if the user has consented to analytics cookies
 * @param url The URL being tracked
 */
export function trackPageView(url: string): void {
  if (typeof window === 'undefined') return; // Don't run on server-side
  
  if (isCookieTypeAccepted('analytics')) {
    console.log(`Page view tracked: ${url}`);
    
    // Google Analytics page view tracking
    window.gtag && window.gtag('config', GA_MEASUREMENT_ID, {
      page_path: url,
    });
  }
}

/**
 * Track event only if the user has consented to analytics cookies
 * @param action The action that occurred
 * @param category The event category
 * @param label Optional label for the event
 * @param value Optional value for the event
 */
export function trackEvent(
  action: string,
  category: string,
  label?: string,
  value?: number
): void {
  if (typeof window === 'undefined') return; // Don't run on server-side
  
  if (isCookieTypeAccepted('analytics')) {
    console.log(`Event tracked: ${action} (${category})${label ? ` - ${label}` : ''}${value !== undefined ? ` - ${value}` : ''}`);
    
    // Google Analytics event tracking
    window.gtag && window.gtag('event', action, {
      event_category: category,
      event_label: label,
      value: value,
    });
  }
} 