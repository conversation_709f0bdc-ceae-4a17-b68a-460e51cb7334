'use client';

import { useState } from 'react';

const SalesForm = () => {
  const [isFormSubmitted, setIsFormSubmitted] = useState(false);
  const [copiedField, setCopiedField] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    service: '',
    notes: ''
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsFormSubmitted(true);
  };

  const copyToClipboard = async (text: string, fieldName: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedField(fieldName);
      setTimeout(() => setCopiedField(null), 2000); // Reset after 2 seconds
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  return (
    <div className="w-full">
      <div className="bg-gray-100 p-4 rounded-lg mb-6 border border-gray-200">
        <div className="flex items-center mb-2">
          <svg className="w-5 h-5 text-[#ec4899] mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
          <h4 className="text-sm sm:text-base font-medium text-[#1a3a6d]">Client Information Form</h4>
        </div>
        <p className="text-sm sm:text-base text-gray-700">
          Have your client fill out this form. After they submit it, their information will be displayed below for you to reference when booking their appointment.
        </p>
      </div>

      {/* Custom Client Information Form */}
      <div className="bg-white border border-gray-200 p-6 rounded-lg">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Client Name *</label>
              <input
                type="text"
                required
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#ec4899] focus:border-transparent"
                placeholder="Enter client's full name"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
              <input
                type="email"
                required
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#ec4899] focus:border-transparent"
                placeholder="Enter client's email"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Phone Number *</label>
              <input
                type="tel"
                required
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#ec4899] focus:border-transparent"
                placeholder="Enter client's phone number"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Service Interest</label>
              <select
                value={formData.service}
                onChange={(e) => handleInputChange('service', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#ec4899] focus:border-transparent"
              >
                <option value="">Select service</option>
                <option value="Windows">Windows</option>
                <option value="Doors">Doors</option>
                <option value="Conservatory">Conservatory</option>
                <option value="Windows & Doors">Windows & Doors</option>
                <option value="Full Installation">Full Installation</option>
                <option value="Other">Other</option>
              </select>
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">Address</label>
              <input
                type="text"
                value={formData.address}
                onChange={(e) => handleInputChange('address', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#ec4899] focus:border-transparent"
                placeholder="Enter client's address"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">Notes</label>
              <textarea
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                rows={4}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#ec4899] focus:border-transparent"
                placeholder="Any additional notes or requirements"
              />
            </div>
          </div>

          <div className="text-center">
            <button
              type="submit"
              className="bg-[#ec4899] hover:bg-[#be185d] text-white font-medium py-3 px-8 rounded-lg transition-colors duration-300 text-lg"
            >
              Save Client Information
            </button>
          </div>
        </form>
      </div>



      {/* Client Information Display */}
      {isFormSubmitted && (
        <div className="mt-6 p-6 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center mb-4">
            <svg className="w-6 h-6 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
            </svg>
            <h4 className="text-lg font-semibold text-green-800">Client Information Summary</h4>
          </div>

          <div className="bg-white p-6 rounded-lg border border-green-200 mb-4">
            <h5 className="font-medium text-gray-800 mb-4 text-lg">Client Information Captured:</h5>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-gray-50 p-3 rounded-lg">
                <div className="flex items-center justify-between mb-1">
                  <label className="block text-sm font-medium text-gray-600">Name:</label>
                  <button
                    onClick={() => copyToClipboard(formData.name, 'name')}
                    className="text-[#ec4899] hover:text-[#be185d] transition-colors duration-200"
                    title="Copy name"
                  >
                    {copiedField === 'name' ? (
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                      </svg>
                    ) : (
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                      </svg>
                    )}
                  </button>
                </div>
                <p className="text-lg font-semibold text-gray-900">{formData.name}</p>
              </div>

              <div className="bg-gray-50 p-3 rounded-lg">
                <div className="flex items-center justify-between mb-1">
                  <label className="block text-sm font-medium text-gray-600">Email:</label>
                  <button
                    onClick={() => copyToClipboard(formData.email, 'email')}
                    className="text-[#ec4899] hover:text-[#be185d] transition-colors duration-200"
                    title="Copy email"
                  >
                    {copiedField === 'email' ? (
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                      </svg>
                    ) : (
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                      </svg>
                    )}
                  </button>
                </div>
                <p className="text-lg font-semibold text-gray-900">{formData.email}</p>
              </div>

              <div className="bg-gray-50 p-3 rounded-lg">
                <div className="flex items-center justify-between mb-1">
                  <label className="block text-sm font-medium text-gray-600">Phone:</label>
                  <button
                    onClick={() => copyToClipboard(formData.phone, 'phone')}
                    className="text-[#ec4899] hover:text-[#be185d] transition-colors duration-200"
                    title="Copy phone"
                  >
                    {copiedField === 'phone' ? (
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                      </svg>
                    ) : (
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                      </svg>
                    )}
                  </button>
                </div>
                <p className="text-lg font-semibold text-gray-900">{formData.phone}</p>
              </div>

              <div className="bg-gray-50 p-3 rounded-lg">
                <div className="flex items-center justify-between mb-1">
                  <label className="block text-sm font-medium text-gray-600">Service Interest:</label>
                  <button
                    onClick={() => copyToClipboard(formData.service || 'Not specified', 'service')}
                    className="text-[#ec4899] hover:text-[#be185d] transition-colors duration-200"
                    title="Copy service"
                  >
                    {copiedField === 'service' ? (
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                      </svg>
                    ) : (
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                      </svg>
                    )}
                  </button>
                </div>
                <p className="text-lg font-semibold text-gray-900">{formData.service || 'Not specified'}</p>
              </div>

              {formData.address && (
                <div className="bg-gray-50 p-3 rounded-lg md:col-span-2">
                  <div className="flex items-center justify-between mb-1">
                    <label className="block text-sm font-medium text-gray-600">Address:</label>
                    <button
                      onClick={() => copyToClipboard(formData.address, 'address')}
                      className="text-[#ec4899] hover:text-[#be185d] transition-colors duration-200"
                      title="Copy address"
                    >
                      {copiedField === 'address' ? (
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                        </svg>
                      ) : (
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                      )}
                    </button>
                  </div>
                  <p className="text-lg font-semibold text-gray-900">{formData.address}</p>
                </div>
              )}

              {formData.notes && (
                <div className="bg-gray-50 p-3 rounded-lg md:col-span-2">
                  <div className="flex items-center justify-between mb-1">
                    <label className="block text-sm font-medium text-gray-600">Notes:</label>
                    <button
                      onClick={() => copyToClipboard(formData.notes, 'notes')}
                      className="text-[#ec4899] hover:text-[#be185d] transition-colors duration-200"
                      title="Copy notes"
                    >
                      {copiedField === 'notes' ? (
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                        </svg>
                      ) : (
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                      )}
                    </button>
                  </div>
                  <p className="text-lg font-semibold text-gray-900">{formData.notes}</p>
                </div>
              )}
            </div>
          </div>

          <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg mb-4">
            <div className="flex items-start">
              <svg className="w-5 h-5 text-blue-600 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div>
                <h6 className="text-sm font-medium text-blue-800 mb-1">Next Step:</h6>
                <p className="text-sm text-blue-700">
                  Copy this information and use it when filling out the calendar booking form below.
                  This ensures the appointment is booked with the correct client details.
                </p>
              </div>
            </div>
          </div>

          <div className="mt-4 text-center">
            <a
              href="#booking"
              className="inline-block bg-[#ec4899] hover:bg-[#be185d] text-white font-medium py-3 px-6 rounded-lg transition-colors duration-300"
            >
              Go to Booking Calendar ↓
            </a>
          </div>
        </div>
      )}
    </div>
  );
};

export default SalesForm;
