import { Metadata } from 'next';
import Link from 'next/link';
import CookieSettings from '@/components/CookieSettings';

export const metadata: Metadata = {
  title: 'Cookie Policy | Window Warriors - UPVC Windows & Doors North East',
  description: 'Learn about the cookies used on the Window Warriors website and how to manage your cookie preferences.',
  keywords: 'cookie policy, cookie settings, browser cookies, Window Warriors cookies, GDPR compliance',
  openGraph: {
    title: 'Cookie Policy | Window Warriors',
    description: 'Learn about the cookies used on the Window Warriors website and how to manage your preferences.',
    url: 'https://windowwarriors.uk/cookies',
    siteName: 'Window Warriors',
    images: [
      {
        url: 'https://windowwarriors.uk/images/beautiful-view-blue-lake-captured-from-inside-villa.jpg',
        width: 1200,
        height: 630,
        alt: 'Window Warriors Cookie Policy',
      },
    ],
    locale: 'en_GB',
    type: 'website',
  },
  alternates: {
    canonical: 'https://windowwarriors.uk/cookies',
  },
};

export default function CookiesPage() {
  return (
    <main className="flex min-h-screen flex-col page-with-hero">
      {/* Enhanced Hero Section */}
      <section className="relative min-h-[60vh] sm:min-h-[70vh] md:min-h-screen py-16 sm:py-24 md:py-28 lg:py-32 xl:py-36 overflow-hidden">
        {/* Simplified background */}
        <div className="absolute inset-0 bg-gradient-to-br from-[#1a3a6d] to-[#ec4899]/30"></div>
        
        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-2xl sm:text-3xl font-bold text-[#1a3a6d] mb-6 relative inline-block">
              Need More Information?
              <span className="absolute -bottom-2 left-0 right-0 mx-auto w-24 h-1 bg-[#ec4899]/70 rounded-full"></span>
            </h2>
            <p className="text-accessible-gray mb-8">
              If you have any questions about our cookie practices or privacy policy, our team is here to help.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Link href="/contact" className="btn-primary text-center">
                Contact Us
              </Link>
              <Link href="/privacy-policy" className="inline-block border-2 border-[#ec4899] text-[#ec4899] hover:bg-[#ec4899] hover:text-white font-medium py-3 px-8 rounded-full transition-all duration-300 text-center">
                View Privacy Policy
              </Link>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
} 
